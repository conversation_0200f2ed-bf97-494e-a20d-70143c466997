.card {
  display: inline-flex;
  gap: 16px;
  align-items: center;
  width: auto;
  width: 220px;
  margin-right: 16px;
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  &:hover,
  &.active {
    background-color: rgba(19, 194, 194, 0.1);
    border-color: rgba(19, 194, 194, 0.1);
    border-color: var(--primary-color);
    box-shadow: 0 1px 2px -2px rgba(19, 194, 194, 0.16), 0 3px 6px 0 rgba(19, 194, 194, 0.12),
      0 5px 12px 4px rgba(19, 194, 194, 0.09);
  }
}
