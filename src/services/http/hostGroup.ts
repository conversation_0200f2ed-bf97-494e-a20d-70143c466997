// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 修改 PUT /api/v1/rump/host/group/${param0}/update */
export async function hostGroupUpdateById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.HostGroupUpdateByIdParams,
  body: API.HostGroupCURequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/api/v1/rump/host/group/${param0}/update`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 根据id查询 GET /api/v1/rump/host/group/byId/${param0} */
export async function hostGroupFindById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.HostGroupFindByIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultHostGroupVO>(`/api/v1/rump/host/group/byId/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 创建 POST /api/v1/rump/host/group/create */
export async function hostGroupCreate(
  body: API.HostGroupCURequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/host/group/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除 POST /api/v1/rump/host/group/delete */
export async function hostGroupDeleteByIds(
  body: API.BatchIdRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/host/group/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 列表 POST /api/v1/rump/host/group/list */
export async function hostGroupList(options?: { [key: string]: any }) {
  return request<API.ResultListHostGroupListVO>('/api/v1/rump/host/group/list', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 分页 POST /api/v1/rump/host/group/page */
export async function hostGroupPage(
  body: API.HostGroupPageRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultPageVOHostGroupPageVO>('/api/v1/rump/host/group/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
