import { Select } from 'antd';
import { FC } from 'react';

const handleChange = (value: string[]) => {
  console.log(`selected ${value}`);
};

const options = [
  {
    label: '',
    value: 'rounded',
  },
  {
    label: 'smooth',
    value: 'smooth',
  },
  {
    label: 'Japan',
    value: 'japan',
    emoji: '🇯🇵',
    desc: 'Japan (日本)',
  },
  {
    label: 'Korea',
    value: 'korea',
    emoji: '🇰🇷',
    desc: 'Korea (韩国)',
  },
];

const EdgeSelector: FC = () => {
  return (
    <Select
      style={{ width: 300 }}
      onChange={handleChange}
      options={options}
      // optionRender={(option) => (
      //   <Space>
      //     <span role="img" aria-label={option.data.label}>
      //       {option.data.emoji}
      //     </span>
      //     {option.data.desc}
      //   </Space>
      // )}
    />
  );
};

export default EdgeSelector;
