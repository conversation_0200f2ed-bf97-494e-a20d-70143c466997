import { SEVERITIES } from '@/enums';
import { zabbix } from '@/services/zabbix';
import {
  DrawerForm,
  DrawerFormProps,
  ProFormDependency,
  ProFormRadio,
  ProFormSwitch,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Form } from 'antd';
import React, { memo, useEffect } from 'react';

const UpdateDrawer: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  const [form] = Form.useForm();
  useEffect(() => {
    form.setFieldsValue(initialValues);
  }, [initialValues]);

  // 修改
  const { run: update } = useRequest((value) => zabbix({ ...value, method: 'event.acknowledge' }), {
    manual: true,
    onSuccess: () => {},
    formatResult: (res) => res,
  });
  if (!form) return <></>;
  return (
    <DrawerForm
      width={460}
      form={form}
      title="更新问题"
      open={open}
      onOpenChange={onOpenChange}
      drawerProps={{
        destroyOnClose: true,
      }}
      onFinish={async (values) => {
        const { change, confirm, close, message, severity } = values;
        let action = 0;
        if (change) action += 8;
        if (confirm) action += 2;
        if (close) action += 1;
        if (message) action += 4;
        const params = {
          eventids: [initialValues?.eventid],
          message,
          action,
          severity,
        };
        const res = await update(params);
        onFinish?.(values);
        return res?.data;
      }}
    >
      <ProFormTextArea
        label="问题"
        name="name"
        disabled
        fieldProps={{
          autoSize: { minRows: 1, maxRows: 4 },
        }}
      />
      <ProFormTextArea
        label="消息"
        name="message"
        fieldProps={{
          autoSize: { minRows: 2, maxRows: 4 },
        }}
      />
      <ProFormSwitch label="更改严重性" name="change" />
      <ProFormDependency name={['change']}>
        {({ change }) => {
          return (
            change && (
              <ProFormRadio.Group
                name="severity"
                label="严重性"
                options={SEVERITIES}
                initialValue={initialValues?.severity}
                fieldProps={{
                  optionType: 'button',
                  buttonStyle: 'solid',
                }}
              />
            )
          );
        }}
      </ProFormDependency>
      <ProFormSwitch label="确认" name="confirm" />
      <ProFormSwitch label="关闭问题" name="close" />
    </DrawerForm>
  );
};

export default memo(UpdateDrawer);
