import classNames from 'classnames/bind';
import { ReactNode } from 'react';
import styles from './index.less';

const cx = classNames.bind(styles);

interface HeadTitleProps {
  children: ReactNode;
  className?: string;
}

const HeadTitle: React.FC<HeadTitleProps> = ({ children, className }: HeadTitleProps) => {
  return <div className={cx('title', className)}>{children}</div>;
};

export default HeadTitle;
