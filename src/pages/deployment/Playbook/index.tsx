import { getAtomicTaskPath } from '@/services/http/atomicTask';
import { HomeFilled, MoreOutlined, ReloadOutlined, UnorderedListOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Access, Link, useAccess, useRequest } from '@umijs/max';
import { Breadcrumb, Button, Card, Col, Dropdown, Empty, MenuProps, Row, Space, Spin } from 'antd';
import React, { useCallback, useRef } from 'react';
import File from './components/File';
import Upload from './components/Upload';
import styles from './index.less';

const Playbook: React.FC = () => {
  const rootPath = useRef<string | undefined>('');
  const subpath = useRef<string[]>([]);
  const rootDir = useRef(true);

  // 获取权限
  const { isSuperAdmin = false } = useAccess();

  // 获取所有文件
  const { data, run, loading, refresh } = useRequest((path) => getAtomicTaskPath({ path }), {
    onSuccess: (res) => {
      rootPath.current = rootPath.current || res?.path;
    },
  });

  const items: MenuProps['items'] = [
    ...(rootDir.current
      ? [
          {
            label: <Upload />,
            key: 'upload',
          },
        ]
      : []),
    {
      label: (
        <Link to="/deployment/playbook/upload-list">
          <Space>
            <UnorderedListOutlined />
            解压列表
          </Space>
        </Link>
      ),
      key: 'list',
    },
  ];

  const breadcrumbItems = [
    {
      key: 'home',
      title: (
        <HomeFilled
          className={styles.icon}
          onClick={() => {
            rootDir.current = true;
            subpath.current = [];
            run(rootPath.current);
          }}
        />
      ),
    },
    ...subpath.current?.map((item, index) => ({
      key: index,
      title: (
        <a
          onClick={() => {
            run(item);

            const arr = subpath.current;
            const index = arr.indexOf(item);

            if (index !== -1) {
              const newArr = arr.slice(0, index + 1);
              subpath.current = newArr;
            }
          }}
        >
          {item.split('/').pop()}
        </a>
      ),
    })),
  ];

  const Header = useCallback(
    () => (
      <div className={styles['breadcrumb']}>
        <Breadcrumb items={breadcrumbItems} />
        <Space>
          <Button type="link" icon={<ReloadOutlined />} onClick={refresh} />
          <Access accessible={isSuperAdmin}>
            <Dropdown menu={{ items }} trigger={['click']}>
              <MoreOutlined className={styles['more']} />
            </Dropdown>
          </Access>
        </Space>
      </div>
    ),
    [subpath.current],
  );

  const handleClick = async (file: API.AtomicTaskPathVO) => {
    const { isDirectory, path } = file;
    if (isDirectory) {
      await run(path);
      rootDir.current = false;
      subpath.current = [...subpath.current, path!];
    }
  };
  return (
    <PageContainer header={{ title: false }}>
      <Card bordered={false} title={<Header />} className={styles.card}>
        {!data?.children?.length && (
          <div className={styles.empty}>
            <Empty description="空目录" />
          </div>
        )}
        <Spin spinning={loading}>
          <Row gutter={[16, 24]}>
            {data?.children?.map((item: API.AtomicTaskPathVO) => (
              <Col lg={4} md={6} sm={8} key={item.path}>
                <File {...item} onClick={() => handleClick(item)} isRoot={rootDir.current} />
              </Col>
            ))}
          </Row>
        </Spin>
      </Card>
    </PageContainer>
  );
};

export default Playbook;
