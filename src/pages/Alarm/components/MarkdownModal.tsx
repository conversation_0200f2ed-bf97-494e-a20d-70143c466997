import { Modal } from 'antd';
import Cherry from 'cherry-markdown';
import 'cherry-markdown/dist/cherry-markdown.css';
import React, { memo, useRef } from 'react';
import config from '../config';

const MarkdownModal: React.FC<{
  open: boolean;
  setMarkdownModalVisit: (open: boolean) => void;
  content: string;
}> = ({ open, setMarkdownModalVisit, content = '' }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const cherryInstanceRef = useRef<Cherry>();

  const afterOpenChange = (isOpen: boolean) => {
    if (isOpen) {
      try {
        if (cherryInstanceRef.current) cherryInstanceRef.current?.destroy();
        if (containerRef?.current) {
          const cherryInstance = new Cherry({
            ...config,
          });

          cherryInstance.setMarkdown(content);
          cherryInstanceRef.current = cherryInstance;
        }
      } catch (error) {
        console.error('Error in MarkdownEditor useEffect: ', error);
      }
    }
  };

  return (
    <Modal
      width="70%"
      destroyOnClose
      title="AI分析结果"
      open={open}
      footer={null}
      afterOpenChange={afterOpenChange}
      onCancel={() => setMarkdownModalVisit(false)}
    >
      <div style={{ width: '100%', height: '65vh', overflow: 'auto' }}>
        <div id="markdown-container" ref={containerRef} />
      </div>
    </Modal>
  );
};

export default memo(MarkdownModal);
