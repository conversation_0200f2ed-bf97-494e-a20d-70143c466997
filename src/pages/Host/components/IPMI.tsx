import HeadTitle from '@/components/HeadTitle';
import RKCol from '@/components/RKCol';
import { IPMI_AUTH_TYPE, IPMI_PRIVILEGE } from '@/enums';
import { ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { Row } from 'antd';

export default function IPMI() {
  return (
    <>
      <HeadTitle>IPMI配置</HeadTitle>
      <Row gutter={24}>
        <RKCol>
          <ProFormSelect label="认证算法" name="ipmi_authtype" options={IPMI_AUTH_TYPE} />
        </RKCol>
        <RKCol>
          <ProFormSelect label="优先权层级" name="ipmi_privilege" options={IPMI_PRIVILEGE} />
        </RKCol>
        <RKCol>
          <ProFormText label="用户名称" name="ipmi_username" />
        </RKCol>
        <RKCol>
          <ProFormText.Password
            label="密码"
            name="ipmi_password"
            fieldProps={{
              autoComplete: 'new-password',
            }}
          />
        </RKCol>
      </Row>
    </>
  );
}
