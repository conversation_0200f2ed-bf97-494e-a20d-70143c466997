import { EXECUTE_TYPE, HOST_TYPE, KEY_DISTRIBUTION_METHOD, KEY_TYPES } from '@/enums';
import {
  hostCreate,
  hostDeployKey,
  hostFindById,
  hostTestConnection,
  hostUpdateById,
} from '@/services/http/host';
import { hostSshKeyPage } from '@/services/http/hostSshKey';
import { option2enum } from '@/utils';
import { requiredRule } from '@/utils/setting';
import { nameReg, PortReg } from '@/utils/validator';
import {
  DrawerForm,
  DrawerFormProps,
  ProForm,
  ProFormCheckbox,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message, Space, Spin, Tag } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import React, { useEffect, useMemo, useRef, useState } from 'react';

const HostDrawerForm: React.FC<DrawerFormProps> = ({
  title,
  open,
  onOpenChange,
  initialValues,
  disabled,
  onFinish,
}) => {
  const formRef = useRef<ProFormInstance>();
  // 测试连接后的对象
  const connectRef = useRef<any>({});
  // 判断是否是新增
  const isAdd = useMemo(() => {
    return !!!initialValues?.id;
  }, [initialValues?.id, open]);

  const [canDeploy, setCanDeploy] = useState(false);
  // 是否显示Agent配置
  const [showAgentConfig, setShowAgentConfig] = useState(false);
  // 获取主机信息
  const { run: getHost, loading: hostLoading } = useRequest(
    () => hostFindById({ id: initialValues?.id }),
    {
      formatResult: (res) => res,
      manual: true,
      onSuccess: (res) => {
        if (res?.code !== 200) return;
        const { sshKey, agent, ...rest } = res.data;
        setCanDeploy(rest?.authenticationType === 'SSH_KEY' && rest?.deployKeyType === 'AUTO');

        // 如果有agent配置，则显示Agent配置并设置deployOptions
        if (agent) {
          setShowAgentConfig(true);
          formRef.current?.setFieldsValue({
            ...rest,
            sshKeyId: sshKey?.id,
            agent,
            deployOptions: ['agent'],
          });
        } else {
          formRef.current?.setFieldsValue({ ...rest, sshKeyId: sshKey?.id });
        }
      },
    },
  );

  useEffect(() => {
    if (open && initialValues?.id) {
      getHost();
    }

    // 如果初始值中有agent配置，则显示Agent配置
    if (initialValues?.agent) {
      setShowAgentConfig(true);
    }
  }, [open, initialValues?.id]);

  // 测试连接
  const { run: testConnect, fetches } = useRequest(
    (value) => {
      // 创建一个新对象，不包含 deployOptions 字段
      const requestData = { ...value };
      // 删除 deployOptions 字段
      delete requestData.deployOptions;
      return hostTestConnection(requestData);
    },
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code !== 200) return;
        connectRef.current = res?.data;
        message.success('已连接');
      },
      fetchKey: (params) => params.hostName,
      formatResult: (res) => res,
    },
  );
  // 下发密钥
  const { run: deploy } = useRequest(
    (value) => {
      // 创建一个新对象，不包含 deployOptions 字段
      const requestData = { ...value };
      // 删除 deployOptions 字段
      delete requestData.deployOptions;
      return hostDeployKey(requestData);
    },
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code !== 200) return;
        connectRef.current = res?.data;
        message.success('已下发');
      },
      formatResult: (res) => res,
    },
  );

  // 获取密钥

  const { data, loading: keyLoading } = useRequest(() =>
    hostSshKeyPage({
      page: {
        page: 1,
        size: 1000,
      },
    }),
  );
  return (
    <DrawerForm
      width="auto"
      style={{ minHeight: 200 }}
      formRef={formRef}
      title={title}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        // 处理表单数据
        const formData = { ...value };

        // 如果没有选择Agent配置，则删除agent字段
        if (!showAgentConfig || !formData.deployOptions?.includes('agent')) {
          delete formData.agent;
        }

        // 移除 deployOptions 字段
        delete formData.deployOptions;

        const data = { ...formData, ...connectRef.current };
        const { result } = isAdd
          ? await hostCreate(data)
          : await hostUpdateById({ id: value?.id }, data);

        if (result) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return result;
      }}
      autoFocusFirstInput
      initialValues={{ connectionPort: 22 }}
      drawerProps={{
        destroyOnClose: true,
      }}
      submitter={
        !disabled && {
          render: (props, defaultDoms) => {
            const [cancelBtn, submitBtn] = defaultDoms;
            return [
              cancelBtn,
              <Button
                key="testConnect"
                type="primary"
                loading={fetches[props?.form?.getFieldsValue()?.hostName]?.loading}
                onClick={() => {
                  props?.form?.validateFields().then((res) => {
                    testConnect(res);
                  });
                }}
              >
                测试连接
              </Button>,
              <Button
                disabled={!canDeploy}
                key="deploy"
                type="primary"
                onClick={() => {
                  props?.form?.validateFields().then((res) => {
                    deploy(res);
                  });
                }}
              >
                下发密钥
              </Button>,
              submitBtn,
            ];
          },
        }
      }
      disabled={disabled}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="id" />
      </div>
      <Spin spinning={hostLoading}>
        <ProForm.Group>
          <ProFormText
            width="md"
            name="host"
            label="主机IP"
            tooltip="主机名或IP地址（主机名必须可以解析成对应IP）"
            rules={[requiredRule]}
          />
          <ProFormSelect width="md" name="type" label="主机类型" options={HOST_TYPE} />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormDigit
            width="md"
            name="port"
            label="端口号"
            rules={[
              requiredRule,
              {
                pattern: PortReg,
                message: '请输入正确的端口号',
              },
            ]}
          />
          <ProFormDependency name={['deployKeyType']}>
            {({ deployKeyType }) => {
              return (
                <ProFormSelect
                  width="md"
                  name="authenticationType"
                  label="认证类型"
                  options={EXECUTE_TYPE}
                  rules={[requiredRule]}
                  fieldProps={{
                    onChange: (value) => {
                      setCanDeploy(value === 'SSH_KEY' && deployKeyType === 'AUTO');
                    },
                  }}
                />
              );
            }}
          </ProFormDependency>
        </ProForm.Group>
        <ProFormDependency name={['authenticationType']}>
          {({ authenticationType }) => {
            return (
              authenticationType === 'SSH_KEY' && (
                <ProForm.Group>
                  <ProFormSelect
                    width="md"
                    name="sshKeyId"
                    label="密钥"
                    rules={[requiredRule]}
                    fieldProps={{
                      showSearch: true,
                      options: data?.data?.map((item) => ({
                        value: item.id,
                        label: (
                          <Space size={2}>
                            <Tag color={option2enum(KEY_TYPES)[item.keyType!].color}>
                              {option2enum(KEY_TYPES)[item.keyType!]?.label}
                            </Tag>
                            {item.name}
                          </Space>
                        ),
                      })) as DefaultOptionType[],
                      loading: keyLoading,
                    }}
                  />
                  <ProFormSelect
                    width="md"
                    name="deployKeyType"
                    label="密钥下发方式"
                    options={KEY_DISTRIBUTION_METHOD}
                    rules={[requiredRule]}
                    fieldProps={{
                      onChange: (value) => {
                        setCanDeploy(value === 'AUTO' && authenticationType === 'SSH_KEY');
                      },
                    }}
                  />
                </ProForm.Group>
              )
            );
          }}
        </ProFormDependency>
        <ProForm.Group>
          <ProFormText
            width="md"
            name="username"
            label="用户名"
            fieldProps={{
              autoComplete: 'new-password',
            }}
            rules={[
              requiredRule,
              {
                pattern: nameReg,
                message: '用户名不能以特殊符号及数字开头',
              },
            ]}
          />
          <ProFormText.Password
            width="md"
            name="password"
            label="密码"
            fieldProps={{
              autoComplete: 'new-password',
            }}
          />
        </ProForm.Group>
        {/* <ProFormTextArea
          name="description"
          label="描述"
          fieldProps={{
            autoSize: {
              minRows: 1,
              maxRows: 3,
            },
          }}
        /> */}
        <ProForm.Group>
          <ProFormCheckbox.Group
            name="deployOptions"
            options={[
              {
                label: 'Agent配置',
                value: 'agent',
              },
            ]}
            fieldProps={{
              onChange: (values) => {
                setShowAgentConfig(values.includes('agent'));
              },
            }}
          />
        </ProForm.Group>

        <ProFormDependency name={['deployOptions']}>
          {({ deployOptions }) => {
            if (deployOptions && deployOptions.includes('agent')) {
              return (
                <>
                  <ProForm.Group>
                    <ProFormRadio.Group
                      width="md"
                      name={['agent', 'ipType']}
                      initialValue="Server"
                      rules={[{ required: true, message: '请选择IP类型' }]}
                      options={[
                        { label: 'ServerIP', value: 'Server' },
                        { label: 'ProxyIP', value: 'Proxy' },
                      ]}
                    />
                  </ProForm.Group>
                  <ProFormDependency name={[['agent', 'ipType']]}>
                    {(values) => {
                      const ipType = values?.agent?.ipType;
                      return (
                        <ProForm.Group>
                          <ProFormText
                            width="md"
                            name={['agent', 'ip']}
                            label={ipType === 'Server' ? 'ServerIP地址' : 'ProxyIP地址'}
                            rules={[
                              {
                                required: true,
                                message: `请输入${
                                  ipType === 'Server' ? 'ServerIP' : 'ProxyIP'
                                }地址`,
                              },
                            ]}
                          />
                        </ProForm.Group>
                      );
                    }}
                  </ProFormDependency>
                  <ProForm.Group>
                    <ProFormText
                      width="md"
                      name={['agent', 'port']}
                      label="Agent端口"
                      rules={[requiredRule]}
                    />
                    <ProFormText
                      width="md"
                      name={['agent', 'path']}
                      label="安装路径"
                      rules={[requiredRule]}
                    />
                  </ProForm.Group>
                </>
              );
            }
            return null;
          }}
        </ProFormDependency>
      </Spin>
    </DrawerForm>
  );
};

export default HostDrawerForm;
