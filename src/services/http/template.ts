// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 创建 POST /api/v1/rump/template/create */
export async function create(body: API.TemplateCreateRequest, options?: { [key: string]: any }) {
  return request<API.ResultTemplateIdDTO>('/api/v1/rump/template/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除 POST /api/v1/rump/template/delete */
export async function deleteUsingPost(body: API.BatchIdRequest, options?: { [key: string]: any }) {
  return request<API.ResultTemplateIdDTO>('/api/v1/rump/template/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询 POST /api/v1/rump/template/get */
export async function get(body: API.TemplateGetRequest, options?: { [key: string]: any }) {
  return request<API.ResultListTemplateGetDTO>('/api/v1/rump/template/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量新增 POST /api/v1/rump/template/massre/add */
export async function massreAdd(
  body: API.TemplateMassreAddRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultTemplateIdDTO>('/api/v1/rump/template/massre/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除 POST /api/v1/rump/template/massre/move */
export async function massreMove(
  body: API.TemplateMassreMoveRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultTemplateIdDTO>('/api/v1/rump/template/massre/move', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量修改 POST /api/v1/rump/template/massre/update */
export async function massreUpdate(
  body: API.TemplateMassreUpdateRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultTemplateIdDTO>('/api/v1/rump/template/massre/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改 POST /api/v1/rump/template/update */
export async function update(body: API.TemplateUpdateRequest, options?: { [key: string]: any }) {
  return request<API.ResultTemplateIdDTO>('/api/v1/rump/template/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
