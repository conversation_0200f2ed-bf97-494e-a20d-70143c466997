// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 检查认证 POST /api/v1/rump/user/checkAuthentication */
export async function checkAuthentication(
  body: API.UserAuthenticationRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultUserAuthenticationResponse>('/api/v1/rump/user/checkAuthentication', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 统计 POST /api/v1/rump/user/count */
export async function userCount(body: API.UserInfoRequest, options?: { [key: string]: any }) {
  return request<API.ResultString>('/api/v1/rump/user/count', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建 POST /api/v1/rump/user/create */
export async function userCreate(body: API.UserCreateRequest, options?: { [key: string]: any }) {
  return request<API.ResultUserIdsResponse>('/api/v1/rump/user/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/v1/rump/user/current */
export async function currentUser(options?: { [key: string]: any }) {
  return request<API.ResultUserVO>('/api/v1/rump/user/current', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 删除 POST /api/v1/rump/user/delete */
export async function userDelete(body: API.BatchIdRequest, options?: { [key: string]: any }) {
  return request<API.ResultUserIdsResponse>('/api/v1/rump/user/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询 POST /api/v1/rump/user/get */
export async function userGet(body: API.UserInfoRequest, options?: { [key: string]: any }) {
  return request<API.ResultListUserInfoResponse>('/api/v1/rump/user/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 登录 POST /api/v1/rump/user/login */
export async function userLogin(body: API.LoginRequest, options?: { [key: string]: any }) {
  return request<API.ResultLoginVO>('/api/v1/rump/user/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 注销 POST /api/v1/rump/user/logout */
export async function userLogout(options?: { [key: string]: any }) {
  return request<API.ResultBoolean>('/api/v1/rump/user/logout', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 解锁用户 POST /api/v1/rump/user/unblock */
export async function unblock(body: API.BatchIdRequest, options?: { [key: string]: any }) {
  return request<API.ResultUserIdsResponse>('/api/v1/rump/user/unblock', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改 POST /api/v1/rump/user/update */
export async function userUpdate(body: API.UserUpdateRequest, options?: { [key: string]: any }) {
  return request<API.ResultUserIdsResponse>('/api/v1/rump/user/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
