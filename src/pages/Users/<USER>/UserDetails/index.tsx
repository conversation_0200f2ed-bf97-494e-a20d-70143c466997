import RKCol from '@/components/RKCol';
import { ALARM_LEVEL, MEDIA_ACTIVE } from '@/enums';
import { useRoleList } from '@/hooks/useRoleList';
import { userGet } from '@/services/http/user';
import { userGroupGet } from '@/services/http/userGroup';
import { zabbix, zabbixPost } from '@/services/zabbix';
import {
  binaryToArray,
  getRandomId,
  onSuccessAndGoBack,
  option2enum,
  queryFormData,
  queryOptions,
} from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProColumns,
  ProForm,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import { <PERSON><PERSON>, <PERSON>, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import MediaSetter from '../MediaSetter';

export type MediasItem = RK_API.MediasItem & {
  id?: string;
};

const UserDetails: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const [modalVisit, setModalVisit] = useState(false);
  const initialValues = useRef<RK_API.MediatypesItem>();
  // 获取报警媒介类型
  const { data: mediaTypeList } = useRequest(() => zabbix({ method: 'mediatype.get' }));
  const { roleList, loading: roleLoading } = useRoleList();

  const columns: ProColumns<MediasItem>[] = [
    {
      dataIndex: 'mediatypeid',
      title: '类型',
      width: 150,
      renderText: (text) => {
        return mediaTypeList?.find((item: RK_API.MediasItem) => item.mediatypeid === text)?.name;
      },
    },
    {
      dataIndex: 'sendto',
      width: 200,
      title: '收件人',
      renderText: (text = []) => {
        return text.join(',');
      },
    },
    {
      dataIndex: 'period',
      title: '时间窗口',
      width: 200,
    },
    {
      dataIndex: 'severity',
      title: '告警级别',
      width: 300,
      render: (_, entity) => {
        const { severity } = entity;
        const arr = binaryToArray(ALARM_LEVEL, severity);
        return arr.map((tag) => {
          const obj = ALARM_LEVEL.find((item: Record<string, any>) => tag === item.value);
          return (
            <Tag key={tag} color={obj?.color}>
              {obj?.label}
            </Tag>
          );
        });
      },
    },
    {
      title: '状态',
      dataIndex: 'active',
      width: 120,
      valueEnum: option2enum(MEDIA_ACTIVE),
    },
    {
      title: '操作',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (text, record) => [
        <a
          key="edit"
          onClick={() => {
            initialValues.current = record;
            setModalVisit(true);
          }}
        >
          编辑
        </a>,
        <a
          key="del"
          onClick={() => {
            const arr: MediasItem[] = formRef.current?.getFieldValue('medias');
            formRef.current?.setFieldValue(
              'medias',
              arr.filter((item) => item.id === record.id),
            );
          }}
        >
          删除
        </a>,
      ],
    },
  ];

  // 判断是否为编辑页面
  const { id } = useParams();
  const isEditPage = !!id;

  // 新建
  const { run: add, loading: addLoading } = useRequest(
    (value) => zabbixPost({ ...value, method: 'user.create' }),
    {
      manual: true,
      onSuccess: onSuccessAndGoBack,
      formatResult: (res) => res,
    },
  );
  // 修改
  const { run: update, loading: editLoading } = useRequest(
    (value) => zabbixPost({ ...value, method: 'user.update' }),
    {
      manual: true,
      onSuccess: onSuccessAndGoBack,
      formatResult: (res) => res,
    },
  );

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<RK_API.UserInfoResponse>
        formRef={formRef}
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          onReset: () => {
            history.go(-1);
          },

          render: (props, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
          submitButtonProps: {
            loading: addLoading || editLoading,
          },
        }}
        onFinish={async (values) => {
          const params = {
            ...values,
            medias: values?.medias?.map(({ severity, period, sendto, mediatypeid, active }) => ({
              severity,
              period,
              sendto,
              mediatypeid,
              active,
            })),
          };
          if (isEditPage) {
            update(params);
          } else {
            add(params);
          }
        }}
        request={async () => {
          const data = await queryFormData(
            {
              userids: [id],
              selectUsrgrps: 'extend',
              selectMedias: 'extend',
              selectMediatypes: 'extend',
              output: 'extend',
            },
            isEditPage,
            userGet,
          );
          return {
            ...data,
            medias:
              data &&
              data?.medias?.map((item: RK_API.MediasItem) => ({
                ...item,
                id: getRandomId(),
              })),
          };
        }}
      >
        <div className="rk-none ">
          <ProFormText name="userid" />
          <ProFormText name="medias" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText label="用户名" name="username" rules={[requiredRule]} />
          </RKCol>
          {!isEditPage && (
            <>
              <RKCol>
                <ProFormText.Password
                  label="密码"
                  name="passwd"
                  rules={[
                    requiredRule,
                    {
                      min: 4,
                    },
                  ]}
                  fieldProps={{
                    autoComplete: 'new-password',
                  }}
                />
              </RKCol>
              <RKCol>
                <ProFormText.Password
                  label="确认密码"
                  name="confirmPwd"
                  dependencies={['passwd']}
                  // 不需要回传
                  transform={(value, namePath) => ({
                    [namePath]: undefined,
                  })}
                  rules={[
                    requiredRule,
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('passwd') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('两次输入的密码必须相等'));
                      },
                    }),
                  ]}
                />
              </RKCol>
            </>
          )}

          <RKCol>
            <ProFormSelect
              label="角色"
              name="roleid"
              fieldProps={{
                options: roleList,
                loading: roleLoading,
                fieldNames: {
                  value: 'roleid',
                  label: 'name',
                },
              }}
              allowClear={false}
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              label="用户组"
              name="usrgrps"
              mode="multiple"
              allowClear={false}
              showSearch
              rules={[requiredRule]}
              fieldProps={{
                mode: 'multiple',
                fieldNames: {
                  label: 'name',
                  value: 'usrgrpid',
                },
                maxLength: 19,
              }}
              getValueFromEvent={(value = []) => value.map((item: string) => ({ usrgrpid: item }))}
              convertValue={(value = []) => value.map((item: RK_API.UsrgrpsItem) => item?.usrgrpid)}
              transform={(value: RK_API.UsrgrpsItem[], namePath) => ({
                [namePath]: value.map(({ usrgrpid }) => ({ usrgrpid })),
              })}
              request={() => queryOptions({ output: 'extend' }, userGroupGet)}
            />
          </RKCol>
        </Row>
        <ProFormDependency name={['medias']}>
          {({ medias = [] }) => {
            return (
              <ProTable<MediasItem>
                {...defaultTableConfig}
                rowKey="id"
                className="inner-table"
                headerTitle="报警媒介"
                search={false}
                dataSource={medias}
                columns={columns}
                toolbar={{
                  actions: [
                    <Button
                      key="primary"
                      type="primary"
                      onClick={() => {
                        initialValues.current = undefined;
                        setModalVisit(true);
                      }}
                    >
                      新建
                    </Button>,
                  ],
                }}
              />
            );
          }}
        </ProFormDependency>
      </ProForm>
      <MediaSetter
        initialValues={initialValues.current}
        mediaTypeList={mediaTypeList}
        open={modalVisit}
        onOpenChange={setModalVisit}
        onFinish={async (value) => {
          const arr: MediasItem[] = formRef.current?.getFieldValue('medias') || [];
          const isEdit = arr.some((item) => item?.id === value?.id);
          if (isEdit) {
            const index = arr.findIndex((item) => item?.id === value?.id);
            arr[index] = value;
          } else {
            arr.push(value);
          }

          formRef.current?.setFieldValue('medias', arr);
        }}
      />
    </PageContainer>
  );
};

export default UserDetails;
