import { HOST_TYPE, HOST_USABILITY } from '@/enums';
import { hostPage } from '@/services/http/host';
import { option2enum, queryRkPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { DrawerForm, DrawerFormProps, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button } from 'antd';
import { FC, memo, useState } from 'react';

const columns: ProColumns<API.HostPageVO>[] = [
  {
    title: '主机名',
    dataIndex: 'host',
  },
  {
    title: '主机类型',
    dataIndex: 'type',
    valueEnum: option2enum(HOST_TYPE),
  },
  {
    title: '状态',
    dataIndex: 'usability',
    valueEnum: option2enum(HOST_USABILITY),
  },
  {
    title: '描述',
    width: 200,
    ellipsis: true,
    dataIndex: 'description',
    hideInSearch: true,
  },
];

const SelectHostDrawer: FC<
  DrawerFormProps & {
    handleSelect?: (selectedRows: API.HostPageVO[]) => void;
  }
> = ({ handleSelect, initialValues }) => {
  const [selectedRows, setSelectedRows] = useState<API.HostPageVO[]>([]);
  const onFinish = async () => {
    handleSelect?.(selectedRows);
    return true;
  };

  return (
    <DrawerForm
      width={600}
      title="关联主机"
      trigger={
        <Button type="primary">
          <PlusOutlined />
          关联主机
        </Button>
      }
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      onFinish={onFinish}
    >
      <ProTable<API.HostPageVO>
        className="inner-table"
        {...defaultTableConfig}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          getCheckboxProps: (record) => {
            return {
              disabled: initialValues?.hostList
                ?.map((i: API.HostPageVO) => i.id)
                ?.includes(record.id),
            };
          },
        }}
        options={{
          search: true,
          setting: false,
          density: false,
        }}
        search={false}
        columns={columns}
        headerTitle="主机列表"
        request={async ({ usability, type, keyword: host, ...params }) => {
          return queryRkPagingTable(
            {
              filter: {
                usability,
                type,
              },
              ...params,
              host,
            },
            hostPage,
          );
        }}
      />
    </DrawerForm>
  );
};

export default memo(SelectHostDrawer);
