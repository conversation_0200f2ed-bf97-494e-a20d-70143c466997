.job-node-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 6px 10px;
  color: @colorTextBase;
  background-color: #fff;
  border-left: solid 4px var(--primary-color);
  box-shadow: 0 0 15px 0 rgb(0 0 0 / 10%);
  cursor: move;
  .icon {
    color: @colorBgMask;
  }
  .info {
    display: flex;
    flex: 1;
    flex-direction: column;
    margin-right: 24px;
    margin-left: 10px;
    overflow: hidden;
    span {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .descriptions {
      color: @colorTextTertiary;
      font-size: 12px;
    }
    .title {
      font-size: 14px;
    }
  }
  .error {
    position: absolute;
    top: 6px;
    right: 10px;
    color: @colorError;
  }
  .hover-icon {
    visibility: hidden;
    cursor: pointer;
  }
  &:hover {
    .hover-icon {
      visibility: visible;
    }
  }
}
.start-node-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #b7eb8f;
  border: solid 1px #73d13d;
  border-radius: 50%;

  .inner {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    width: calc(100% + 20px);
    height: 100%;
    padding-right: 16px;
    .icon {
      position: absolute;
      right: 0;
      z-index: 100;
      color: #73d13d;
      visibility: hidden;
      cursor: pointer;
    }

    &:hover {
      .icon {
        visibility: visible;
      }
    }
  }
}
.loading {
  color: #13c2c2;
}
.success {
  color: @colorSuccess;
}
.fail {
  color: @colorError;
}
