// 严重性
export const SEVERITIES = [
  { label: '未分类', value: '0', tagColor: 'default' },
  { label: '信息', value: '1', tagColor: '#2db7f5' },
  { label: '警告', value: '2', tagColor: '#fbd26a' },
  { label: '一般严重', value: '3', tagColor: '#ff7a45' },
  { label: '严重', value: '4', tagColor: '#e60000' },
  { label: '灾难', value: '5', tagColor: '#900021' },
];

// 主机状态

export const HOST_STATUS = [
  { label: '启用', value: 0, status: 'success' },
  { label: '停用', value: 1, status: 'error' },
];

// 标记筛选器的条件

export const TAG_CONDITION = [
  { label: 'Exists', value: '4' },
  { label: 'Equal', value: '1' },
  { label: 'Contain', value: '0' },
  { label: 'Does not exist', value: '5' },
  { label: 'Does not equal', value: '3' },
  { label: 'Does not contain', value: '2' },
];

// 标记查询条件
export const TAG_EVAL_TYPE = [
  { label: 'AND/OR', value: 0 },
  { label: 'OR', value: 2 },
];

// 查看问题
export const PROBLEMS_SHOW = [
  { label: '最近的问题', value: '0' },
  // { label: '问题', value: 1 },
  { label: '历史记录', value: '1' },
];

export const SUPPRESSED_STATUS = [
  { label: '问题', value: '0', status: 'error' },
  { label: '已解决', value: '1', status: 'success' },
];

// 触发器状态
export const TRIGGER_STATUS = [
  { label: '问题', value: '1', status: 'error' },
  { label: '已解决', value: '0', status: 'success' },
];

// 通用状态
export const STATUS = [
  { label: '启用', value: '0', status: 'success' },
  { label: '禁用', value: '1', status: 'error' },
];
// 用户组状态
export const USERS_STATUS = [
  { label: '启用', value: 0, status: 'success' },
  { label: '禁用', value: 1, status: 'error' },
];

// 权限级别
export const PERMISSION_LEVEL = [
  { label: '读写', value: '3' },
  { label: '只读', value: '2' },
  { label: '禁止', value: '0' },
];

// 用户角色
export const USER_ROLE = [
  { label: 'User role', value: '1' },
  { label: 'Admin role', value: '2' },
  { label: 'Super admin role', value: '3' },
  { label: 'Guest role', value: '4' },
];

// 报警媒介状态

export const MEDIA_ACTIVE = [
  { label: '启用', value: 0, status: 'success' },
  { label: '禁用', value: 1, status: 'error' },
];

// 告警级别
export const ALARM_LEVEL = [
  { label: '未分类', value: 0, color: 'default' },
  { label: '信息', value: 1, color: 'blue' },
  { label: '警告', value: 2, color: 'orange' },
  { label: '一般严重', value: 3, color: 'volcano' },
  { label: '严重', value: 4, color: 'error' },
  { label: '灾难', value: 5, color: '#f50' },
];

// 宏类型

export const MACRO_TYPE = [
  { label: '文本', value: '0' },
  { label: '密文', value: '1 ' },
  { label: '密钥', value: '2' },
];

// 监控类型
export const MONITOR_TYPE = [
  {
    value: '0',
    label: 'rkmon agent',
  },
  {
    value: '2',
    label: 'rkmon采集器',
  },
  {
    value: '3',
    label: '简单检查',
  },
  {
    value: '5',
    label: '内部检查',
  },
  {
    value: '7',
    label: 'rkmon agent (主动)',
  },
  {
    value: '9',
    label: 'web 监控项',
  },
  {
    value: '10',
    label: '外部检查',
  },
  {
    value: '11',
    label: '数据库监控',
  },
  {
    value: '12',
    label: 'IPMI agent',
  },
  {
    value: '13',
    label: 'SSH agent',
  },
  {
    value: ' 14',
    label: 'Telnet agent',
  },
  {
    value: '15',
    label: '可计算的监控',
  },
  {
    value: '16',
    label: 'JMX agent',
  },
  {
    value: '17',
    label: 'SNMP trap',
  },
  {
    value: '18',
    label: '相关项监控',
  },
  {
    value: '19',
    label: 'HTTP agent',
  },
  {
    value: '20',
    label: 'SNMP agent',
  },
  {
    value: '21',
    label: '脚本。',
  },
];

// 监控数据类型
export const MONITOR_DATA_TYPE = [
  {
    label: '数字（无正负）',
    value: '3',
  },
  {
    label: '浮点型',
    value: '0',
  },
  {
    label: '字符',
    value: '1',
  },
  {
    label: '日志',
    value: '2',
  },

  {
    label: '文本',
    value: '4',
  },
];

// 监控时间间隔
export const DATE_UNIT = [
  {
    label: '秒',
    value: 's',
  },
  {
    label: '分',
    value: 'm',
  },
  {
    label: '时',
    value: 'h',
  },
  {
    label: '周',
    value: 'w',
  },
  {
    label: '天',
    value: 'd',
  },
];

// 接口类型
export const INTERFACE_TYPE = [
  {
    label: 'AGENT',
    value: '1',
  },
  {
    label: 'SNMP',
    value: '2',
  },
  {
    label: 'IPMI',
    value: '3',
  },
  {
    label: 'JMX',
    value: '4',
  },
];

export const CONNECTION_TYPE = [
  {
    label: 'IP',
    value: '1',
  },
  {
    label: 'DNS',
    value: '0',
  },
];
// SNMP版本
export const SNMP_VERSION = [
  {
    label: 'SNMPv1',
    value: '1',
  },
  {
    label: 'SNMPv2c',
    value: '2',
  },
  {
    label: 'SNMPv3',
    value: '3',
  },
];
// SNMPv3安全级别
export const SECURITY_LEVEL = [
  {
    label: 'noAuthNoPriv',
    value: '0',
  },
  {
    label: 'authNoPriv',
    value: '1',
  },
  {
    label: 'authPriv',
    value: '2',
  },
];
// SNMPv3身份认证协议
export const AUTH_PROTOCOL = [
  {
    label: 'MD5',
    value: '0',
  },
  {
    label: 'SHA1',
    value: '1',
  },
  {
    label: 'SHA224',
    value: '2',
  },
  {
    label: 'SHA256',
    value: '3',
  },
  {
    label: 'SHA384',
    value: '4',
  },
  {
    label: 'SHA512',
    value: '5',
  },
];
// SNMPv3隐私协议
export const PRIV_PROTOCOL = [
  {
    label: 'DES',
    value: '0',
  },
  {
    label: 'AES128',
    value: '1',
  },
  {
    label: 'AES192',
    value: '2',
  },
  {
    label: 'AES256',
    value: '3',
  },
  {
    label: 'AES192C',
    value: '4',
  },
  {
    label: 'AES256C',
    value: '5',
  },
];

// IPMI 验证算法
export const IPMI_AUTH_TYPE = [
  {
    label: 'default',
    value: '-1',
  },
  {
    label: 'none',
    value: '0',
  },
  {
    label: 'MD2',
    value: '1',
  },
  {
    label: 'MD54',
    value: '2',
  },
  {
    label: 'OEM',
    value: '5',
  },
  {
    label: 'RMCP+',
    value: '6',
  },
];

// IPMI 验证算法
export const IPMI_PRIVILEGE = [
  {
    label: 'callback',
    value: '1',
  },
  {
    label: 'user',
    value: '2',
  },
  {
    label: 'operator',
    value: '3',
  },
  {
    label: 'admin',
    value: '4',
  },
  {
    label: 'OEM',
    value: '5',
  },
];
// 连接主机
export const TLS_ACCEPT = [
  {
    label: '未加密',
    value: '1',
  },
  {
    label: 'PSK（预共享密钥）',
    value: '2',
  },
  {
    label: '已认证',
    value: '4',
  },
];
// 从主机连接
export const TLS_CONNECT = [
  {
    label: '不加密',
    value: '1',
  },
  {
    label: 'PSK（使用预共享密钥）',
    value: '2',
  },
  {
    label: '已加密',
    value: '4',
  },
];
// 动作过滤条件类型
export const CONDITION_TYPE = [
  {
    label: '触发器名称',
    value: '3',
  },
  {
    label: '触发器',
    value: '2',
  },
  {
    label: '触发器严重等级',
    value: '4',
  },
  {
    label: '主机',
    value: '1',
  },
  {
    label: '对象群组',
    value: '0',
  },
  {
    label: '问题被抑制',
    value: '16',
  },
  {
    label: '标签名',
    value: '25',
  },
  {
    label: '标签值',
    value: '26',
  },
  {
    label: '主机监控模板',
    value: '13',
  },
  {
    label: '时间段',
    value: '6',
  },
];

// 动作过滤条件运算符
export const CONDITION_OPERATOR = [
  {
    label: '等于',
    value: '0',
  },
  {
    label: '不等于',
    value: '1',
  },
  {
    label: '包含',
    value: '2',
  },
  {
    label: '不包含',
    value: '3',
  },
  {
    label: '在',
    value: '4',
  },
  {
    label: '大于或等于',
    value: '5',
  },
  {
    label: '小于或等于',
    value: '6',
  },
  {
    label: '不在',
    value: '7',
  },
  {
    label: '匹配',
    value: '8',
  },
  {
    label: '不匹配',
    value: '9',
  },
  {
    label: '是',
    value: '10',
  },
  {
    label: '否',
    value: '11',
  },
];
// 动作条件计算方法
export const EVAL_TYPE = [
  {
    label: 'and/or',
    value: '0',
  },
  {
    label: 'and',
    value: '1',
  },
  {
    label: 'or',
    value: '2',
  },
  {
    label: '自定义表达式',
    value: '3',
  },
];
// 操作类型
export const OPERATION_TYPE = [
  {
    label: '发送信息',
    value: '0',
  },
  {
    label: 'Ping',
    value: '1-1',
  },
  {
    label: 'Traceroute',
    value: '1-2',
  },
];
export const RECOVERY_OPERATION_TYPE = [
  {
    label: '发送信息',
    value: '0',
  },
  {
    label: '通知所有相关人员',
    value: '11',
  },
  {
    label: 'Ping',
    value: '1-1',
  },
  {
    label: 'Traceroute',
    value: '1-2',
  },
];
export const UPDATE_OPERATION_TYPE = [
  {
    label: '发送信息',
    value: '0',
  },
  {
    label: '通知所有相关人员',
    value: '12',
  },
  {
    label: 'Ping',
    value: '1-1',
  },
  {
    label: 'Traceroute',
    value: '1-2',
  },
];

// 组内用户的前端身份验证方法
export const GUI_ACCESS = [
  {
    label: '系统默认',
    value: 0,
  },
  {
    label: '使用内部认证',
    value: 1,
  },
  {
    label: '使用LDAP认证',
    value: 2,
  },
  {
    label: '禁止访问前端',
    value: 3,
  },
];

// 主机资产
export const HOST_ASSETS = [
  {
    label: '无',
    value: '0',
  },
  {
    label: '别名',
    value: '4',
  },
  {
    label: '资产标签',
    value: '11',
  },
  {
    label: '机箱',
    value: '28',
  },
  {
    label: '	联系人',
    value: '23',
  },
  {
    label: '合同号码',
    value: '32',
  },
  {
    label: '硬件退役日期',
    value: '47',
  },
  {
    label: '硬件维护到期日期',
    value: '46',
  },
  {
    label: '硬件安装日期',
    value: '45',
  },
  {
    label: '硬件购买日期',
    value: '44',
  },
  {
    label: '部署状态',
    value: '34',
  },
  {
    label: '硬件',
    value: '14',
  },
  {
    label: '硬件详细信息',
    value: '15',
  },
  {
    label: '主机子网掩码',
    value: '39',
  },
  {
    label: '主机网络',
    value: '38',
  },
  {
    label: '主机路由器',
    value: '40',
  },
  {
    label: '硬件架构',
    value: '30',
  },
  {
    label: '安装程序名称',
    value: '33',
  },
  {
    label: '位置',
    value: '24',
  },
  {
    label: '位置纬度',
    value: '25',
  },
  {
    label: '位置经度',
    value: '26',
  },
  {
    label: 'MAC 地址 A',
    value: '12',
  },
  {
    label: 'MAC地址B',
    value: '13',
  },
  {
    label: '模式',
    value: '29',
  },
  {
    label: '名称',
    value: '3',
  },
  {
    label: '备注',
    value: '27',
  },
  {
    label: 'OOB IP 地址',
    value: '41',
  },
  {
    label: 'OOB 主机子网掩码',
    value: '42',
  },
  {
    label: 'OOB 路由器',
    value: '43',
  },
  {
    label: '操作系统名称',
    value: '5',
  },
  {
    label: '详细的操作系统名称',
    value: '6',
  },
  {
    label: '	操作系统简称',
    value: '7',
  },
  {
    label: '主要 POC 手机号码',
    value: '61',
  },
  {
    label: '主要电子邮件',
    value: '58',
  },
  {
    label: '主要 POC 名称',
    value: '57',
  },
  {
    label: '主要 POC 注释',
    value: '63',
  },
  {
    label: '主要 POC 电话 A',
    value: '59',
  },
  {
    label: '主要 POC 电话 B',
    value: '60',
  },
  {
    label: '主要 POC 屏幕名称',
    value: '62',
  },
  {
    label: '辅助 POC 手机号码',
    value: '68',
  },
  {
    label: '辅助 POC 电子邮件',
    value: '65',
  },
  {
    label: '二级 POC 名称',
    value: '64',
  },
  {
    label: '次要 POC 注释',
    value: '70',
  },
  {
    label: '辅助 POC 电话 A',
    value: '66',
  },
  {
    label: '辅助 POC 电话 B',
    value: '67',
  },
  {
    label: '次要 POC 屏幕名称',
    value: '69',
  },
  {
    label: '序列号A',
    value: '8',
  },
  {
    label: '序列号B',
    value: '9',
  },
  {
    label: '站点地址A',
    value: '48',
  },
  {
    label: '站点地址B',
    value: '49',
  },
  {
    label: '站点地址C',
    value: '50',
  },
  {
    label: '站点城市',
    value: '51',
  },
  {
    label: '网站国家',
    value: '53',
  },
  {
    label: '站点注释',
    value: '56',
  },
  {
    label: '站点机架位置',
    value: '55',
  },
  {
    label: '站点状态',
    value: '52',
  },
  {
    label: '站点邮政编码',
    value: '54',
  },
  {
    label: '软件',
    value: '16',
  },
  {
    label: '软件应用程序 A',
    value: '18',
  },
  {
    label: '软件应用程序 B',
    value: '19',
  },
  {
    label: '软件应用程序 C',
    value: '20',
  },
  {
    label: '软件应用程序 D',
    value: '21',
  },
  {
    label: '软件应用程序 E',
    value: '22',
  },
  {
    label: '软件详细信息',
    value: '17',
  },
  {
    label: 'Tag',
    value: '10',
  },
  {
    label: '类型',
    value: '1',
  },
  {
    label: '类型详细信息',
    value: '2',
  },
  {
    label: 'URL A',
    value: '35',
  },
  {
    label: 'URL B',
    value: '36',
  },
  {
    label: 'URL C',
    value: '37',
  },
  {
    label: '厂家',
    value: '31',
  },
];

// 事件恢复生成模式

export const RECOVERY_MODE = [
  {
    label: '表达式',
    value: '0',
  },
  {
    label: '恢复表达式',
    value: '1',
  },
  {
    label: '无',
    value: '2',
  },
];
// 触发器是否能够生成多个问题事件
export const PROBLEM_EVENT_TYPE = [
  {
    label: '单个',
    value: '0',
  },
  {
    label: '多重',
    value: '1',
  },
];
// 事件恢复关联的模式
export const CORRELATION_MODE = [
  {
    label: '所有问题',
    value: '0',
  },
  {
    label: '与标签值匹配的所有问题',
    value: '1',
  },
];
// 允许手动关闭
export const MANUAL_CLOSE = [
  {
    label: '不允许',
    value: '0',
  },
  {
    label: '允许',
    value: '1',
  },
];
// 主机接口的可用性
export const AVAILABLE = [
  {
    label: '未知',
    value: '0',
    status: 'default',
  },
  {
    label: '可用',
    value: '1',
    status: 'success',
  },
  {
    label: '不可用',
    value: '2',
    status: 'error',
  },
];

// 告警类型
export const ALERT_TYPE = [
  {
    label: '消息',
    value: '0',
  },
  {
    label: '远程命令',
    value: '1',
  },
];

// 消息告警状态
export const ALERT_MSG_STATUS = [
  {
    label: '未发送',
    value: '0',
  },
  {
    label: '已送达',
    value: '1',
  },
  {
    label: '经多次重试后失败',
    value: '2',
  },
  {
    label: '告警管理员尚未处理的新告警',
    value: '3',
  },
];
export const ALERT_COMMAND_STATUS = [
  {
    label: '未运行',
    value: '0',
  },
  {
    label: '运行成功',
    value: '1',
  },
  {
    label: '经多次重试后失败',
    value: '2',
  },
  {
    label: '尝试在 rkmon agent 上运行命令但不可用',
    value: '3',
  },
];

export const ACTION = [
  { label: '关闭问题', value: 1 },
  { label: '确认事件', value: 2 },
  { label: '添加消息', value: 4 },
  { label: '更改严重性', value: 8 },
  { label: '取消确认事件', value: 16 },
];

// 主机可用性
export const HOST_USABILITY = [
  { label: '未知', value: '-1', status: 'Default' },
  { label: '不可用', value: '0', status: 'Error' },
  { label: '可用', value: '1', status: 'Success' },
];

// 主机类型
export const HOST_TYPE = [
  { value: 'Linux', label: 'Linux' },
  { value: 'Windows', label: 'Windows' },
  { value: 'Unix', label: 'Unix' },
];

// 上传 playbook 状态枚举值

export const UPLOAD_PLAYBOOK_STATUS = [
  { value: '-1', label: '失败', status: 'Error' },
  { value: '0', label: '上传解压中', status: 'Processing' },
  { value: '1', label: '成功', status: 'Success' },
];

// 运行方式
export const RUN_MODE = [
  { value: 0, label: '始终运行' },
  { value: 1, label: '成功' },
];

// 执行状态

export const EXECUTE_STATUS = [
  { value: 'READY', label: '准备就绪', status: 'Processing' },
  { value: 'RUNNING', label: '运行中', status: 'Processing' },
  // { value: 'FAIL', label: '失败', status: 'error' },
  { value: 'END', label: '成功', status: 'Success' },
];

// 密钥类型
export const KEY_TYPES = [
  { label: 'ecdsa', value: 'ECDSA', color: 'cyan' },
  { label: 'rsa', value: 'RSA', color: 'geekblue' },
  { label: 'dsa', value: 'DSA', color: 'gold' },
];

// 执行类型
export const EXECUTE_TYPE = [
  { value: 'SSH_KEY', label: 'SSH_KEY' },
  { value: 'PASSWORD', label: 'PASSWORD' },
];

// 主机密钥下发方式

export const KEY_DISTRIBUTION_METHOD = [
  { value: 'MANUAL', label: '手动下发' },
  { value: 'AUTO', label: '平台下发' },
];

// 主机及变量合并方式
export const HOST_MERGE_TYPE = [
  { value: 'OVERWRITE', label: '覆盖' },
  { value: 'MERGE', label: '合并' },
];

// 监控代理agent状态
export const HOST_AGENT_STATUS = [
  { label: '成功', value: 'Success', color: 'green' },
  { label: '失败', value: 'Fail', color: 'red' },
  { label: '安装中', value: 'Running', color: 'grey' },
];
