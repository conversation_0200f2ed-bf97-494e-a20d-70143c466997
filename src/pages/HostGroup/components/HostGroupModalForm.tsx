import { zabbixPost } from '@/services/zabbix';
import { requiredRule } from '@/utils/setting';
import {
  ModalForm,
  ModalFormProps,
  ProForm,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useRef } from 'react';

const HostGroupModalForm: React.FC<ModalFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  const isEdit = initialValues?.groupid;
  const formRef = useRef<ProFormInstance>();

  return (
    <ModalForm
      width="auto"
      title={isEdit ? '编辑监控对象组' : '新建监控对象组'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const msg = await zabbixPost({
          ...value,
          method: isEdit ? 'hostgroup.update' : 'hostgroup.create',
        });
        const success = msg.data;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
      autoFocusFirstInput
      initialValues={initialValues}
      modalProps={{
        destroyOnClose: true,
        centered: true,
      }}
    >
      {/* 不需要展示，只是为了form传值 */}
      <div className="rk-none">
        <ProFormText name="groupid" placeholder="请输入" />
      </div>
      <ProForm.Group>
        <ProFormText
          width="md"
          name="name"
          label="组名"
          rules={[requiredRule]}
          fieldProps={{
            maxLength: 50,
          }}
        />
      </ProForm.Group>
    </ModalForm>
  );
};

export default HostGroupModalForm;
