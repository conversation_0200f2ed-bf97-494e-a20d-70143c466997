import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { KEY_TYPES } from '@/enums';
import { hostSshKeyDeleteByIds, hostSshKeyPage } from '@/services/http/hostSshKey';
import { queryRkPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message, Modal, Space, Tag } from 'antd';
import { Key, useCallback, useRef, useState } from 'react';
import KeyDrawer from './components/KeyDrawer';

const KeyManagement = () => {
  const [selectedRows, setSelectedRows] = useState<API.HostSshKeyPageVO[]>([]);
  const actionRef = useRef<ActionType | undefined>();

  const onSuccess = useCallback(() => {
    actionRef.current?.reloadAndRest?.();
  }, []);

  // 删除
  const { run: deleteRecord } = useRequest((ids) => hostSshKeyDeleteByIds({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success('删除成功');
      onSuccess();
    },
    formatResult: (res) => res,
  });

  const handleDelete = useCallback((rows: API.HostSshKeyPageVO[]) => {
    const ids: Key[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.name!);
    });

    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除密钥“${names.join(',')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        deleteRecord(ids);
      },
    });
  }, []);

  // 表格
  const columns: ProColumns<API.HostSshKeyPageVO>[] = [
    {
      title: '密钥名称',
      dataIndex: 'name',
      render: (_, entity) => {
        return <KeyDrawer type={entity?.name} id={entity?.id} onSuccess={onSuccess} />;
      },
    },
    {
      title: '密钥类型',
      dataIndex: 'keyType',
      render: (_, record) => {
        const keysType = KEY_TYPES.find((item) => item.value === record.keyType);
        return <Tag color={keysType?.color}>{keysType?.label}</Tag>;
      },
    },

    {
      title: '操作',
      width: 250,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => (
        <Space>
          <a key="del" onClick={() => handleDelete([record])}>
            删除
          </a>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable
        {...defaultTableConfig}
        actionRef={actionRef}
        columns={columns}
        headerTitle="密钥列表"
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        toolbar={{
          actions: [<KeyDrawer key="create" onSuccess={onSuccess} />],
        }}
        request={async (params) => queryRkPagingTable(params, hostSshKeyPage)}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default KeyManagement;
