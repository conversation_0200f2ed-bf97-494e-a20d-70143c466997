import RkLine from '@/components/Charts/Line';
import RkPie from '@/components/Charts/Pie';
import RKCard from '@/components/RKCard';
import { ProTable } from '@ant-design/pro-components';
import { <PERSON><PERSON>, Col, ConfigProvider, Row, Select, Space, Tag, theme } from 'antd';
import classNames from 'classnames/bind';
import { middlewareMockData, pieData, recentTaskData, taskRangeEnum, taskTrendData } from './data';
import styles from './index.less';
const cx = classNames.bind(styles);

const index = () => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {},
        },
        algorithm: [theme.darkAlgorithm],
      }}
    >
      <div className={cx('container')}>
        <div className={cx('header')}>监控数据大屏</div>
        <div className={cx('content')}>
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <RKCard title="中间件告警">
                <Row gutter={[24, 24]}>
                  <Col span={8}>
                    <RkPie
                      radius={0.5}
                      label={{
                        type: 'spider',
                        content: '{name}：{value}\n比例：{percentage}',
                      }}
                      // 中间件
                      statisticColor="white"
                      data={pieData}
                      color={({ type }) => {
                        if (type === '紧急') return '#800020';
                        if (type === '严重') return '#C51E3A';
                        if (type === '中等') return '#E44D2E';
                        if (type === '一般') return '#FADA5E';
                        return '#FDBCB4';
                      }}
                      theme="custom-theme-dark"
                    />
                  </Col>
                  <Col span={16}>
                    <ProTable
                      rowKey="hostid"
                      search={false}
                      options={false}
                      size="small"
                      columns={[
                        {
                          title: '主机',
                          dataIndex: 'name',
                        },
                        {
                          title: '问题数量',
                          dataIndex: 'count',
                        },
                      ]}
                      dataSource={middlewareMockData}
                    />
                  </Col>
                </Row>
              </RKCard>
            </Col>
            <Col span={24}>
              <RKCard
                title="任务趋势图"
                extra={
                  <Select
                    options={[{ value: 'month', label: '最近一个月' }]}
                    value="month"
                  ></Select>
                }
              >
                <RkLine
                  theme="custom-theme-dark"
                  color={['#00F3FF', '#FF4D88']}
                  height={300}
                  smooth
                  xField="year"
                  yField="value"
                  seriesField="category"
                  data={taskTrendData}
                />
              </RKCard>
            </Col>
            <Col span={24}>
              <RKCard title="最近执行的任务">
                <ProTable
                  className="inner-table"
                  search={false}
                  options={{
                    reload: false,
                    setting: false,
                    density: false,
                  }}
                  pagination={{
                    defaultPageSize: 5,
                  }}
                  size="small"
                  columns={[
                    {
                      title: '名称',
                      dataIndex: 'missionName',
                      ellipsis: true,
                      width: 200,
                    },
                    {
                      title: '任务类型',
                      dataIndex: 'taskRange',
                      width: 80,
                      valueEnum: taskRangeEnum,
                      render: (text, record) => {
                        // @ts-ignore
                        return <Tag color={taskRangeEnum[record.taskRange].tagColor}>{text}</Tag>;
                      },
                    },
                    {
                      title: '开始时间',
                      dataIndex: 'startTime',
                      width: 180,
                    },
                    {
                      title: '结束时间',
                      dataIndex: 'endTime',
                      width: 180,
                    },
                    {
                      title: '执行结果',
                      width: 80,
                      dataIndex: 'execStatus',
                      valueEnum: {
                        执行中: { text: '执行中', status: 'Processing' },
                        执行失败: {
                          text: '失败',
                          status: 'Error',
                        },
                        执行成功: {
                          text: '成功',
                          status: 'Success',
                          disabled: true,
                        },
                      },
                    },
                    {
                      title: '执行人',
                      width: 110,
                      ellipsis: true,
                      dataIndex: 'execUserName',
                    },
                    {
                      title: '状态',
                      width: 100,
                      dataIndex: 'status',
                      valueEnum: {
                        unAudited: { text: '未审核', status: 'Default' },
                        audited: { text: '审核通过', status: 'Success' },
                        overdue: { text: '过期', status: 'Warning' },
                        refuse: { text: '驳回', status: 'Error' },
                        invalid: { text: '作废', color: '#ffbb96' },
                        cancel: { text: '撤回', color: '#ffbb96' },
                      },
                    },
                    {
                      title: '操作',
                      width: 100,
                      key: 'option',
                      valueType: 'option',
                      align: 'center',
                      render: () => (
                        <Space>
                          <Button type="link">详情</Button>
                        </Space>
                      ),
                    },
                  ]}
                  dataSource={recentTaskData}
                />
              </RKCard>
            </Col>
            <Col span={8}>
              <RKCard title="中间件风险分布">
                <RkPie
                  height={220}
                  radius={0.7}
                  innerRadius={0.7}
                  showPercentValue
                  innerTitle="中间件数"
                  tooltip={false}
                  label={{
                    type: 'spider',
                    content: '{name}：{value}\n比例：{percentage}',
                  }}
                  statisticColor="white"
                  data={[
                    { type: '高危', value: 37 },
                    { type: '中危', value: 50 },
                    { type: '低危', value: 70 },
                  ]}
                  color={({ type }) => {
                    if (type === '高危') return '#C51E3A';
                    if (type === '中危') return '#BF4F51';
                    if (type === '低危') return '#E44D2E';
                    return '#E44D2E';
                  }}
                  theme="custom-theme-dark"
                />
              </RKCard>
            </Col>
            <Col span={8}>
              <RKCard title="风险等级分布">
                <RkPie
                  height={220}
                  radius={0.7}
                  innerRadius={0.7}
                  showPercentValue
                  innerTitle="风险总数"
                  tooltip={false}
                  label={{
                    type: 'spider',
                    content: '{name}：{value}\n比例：{percentage}',
                  }}
                  statisticColor="white"
                  data={[
                    { type: '高危', value: 113 },
                    { type: '中危', value: 388 },
                    { type: '低危', value: 402 },
                  ]}
                  color={({ type }) => {
                    if (type === '高危') return '#C51E3A';
                    if (type === '中危') return '#BF4F51';
                    if (type === '低危') return '#E44D2E';
                    return '#E44D2E';
                  }}
                  theme="custom-theme-dark"
                />
              </RKCard>
            </Col>
            <Col span={8}>
              <RKCard title="运行状态">
                <RkPie
                  height={220}
                  color={({ type }) => {
                    if (type === '运行中') return '#00F3FF';
                    if (type === '停止') return '#FF4D88';
                    if (type === '不可达') return '#D8BFD8';
                    return '#D8BFD8';
                  }}
                  radius={0.7}
                  label={{
                    type: 'spider',
                    content: '{name}：{value}',
                  }}
                  // 中间件
                  statisticColor="white"
                  data={[
                    // 运行中 停止 不可达
                    {
                      type: '运行中',
                      value: 10,
                    },
                    {
                      type: '停止',
                      value: 20,
                    },
                    {
                      type: '不可达',
                      value: 30,
                    },
                  ]}
                  theme="custom-theme-dark"
                />
              </RKCard>
            </Col>
          </Row>
        </div>
      </div>
    </ConfigProvider>
  );
};

export default index;
