import Icon from '@/components/Icon';
import { getTopology, updateTopology } from '@/services/http/topology';
import { zabbixList } from '@/services/zabbix';
import { onSuccess } from '@/utils';
import { FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons/lib/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Cell, Graph } from '@antv/x6';
import { Dnd } from '@antv/x6-plugin-dnd';
import { Keyboard } from '@antv/x6-plugin-keyboard';
import { MiniMap } from '@antv/x6-plugin-minimap';
import { Selection } from '@antv/x6-plugin-selection';
import { Snapline } from '@antv/x6-plugin-snapline';
import { register } from '@antv/x6-react-shape';
import { Link, useLocation, useParams, useRequest } from '@umijs/max';
import { Col, Row, Tooltip } from 'antd';
import classNames from 'classnames/bind';
import { isMicroservice } from 'config/defaultSettings';
import { memo, useCallback, useEffect, useRef, useState } from 'react';
import EditorMinimap from './components/EditorMinimap';
import EditorToolbar from './components/EditorToobar';
import TopologyDetailPanel from './components/TopologyDetailPanel';
import TopologyItemPanel from './components/TopologyItemPanel';
import WebNetworkNode from './components/WebNetworkNode';
import styles from './index.less';
const cx = classNames.bind(styles);

// 常量定义
const NODE_SHAPE_WEB_NETWORK = 'web-network-node';
const NODE_SHAPE_UNKNOWN_DEVICE = 'unknown-device-node';
const NODE_HEIGHT = 60;
const NODE_WEIGHT = 100;

export type HostNode = RK_API.Host & {
  problem: boolean;
};
const NodeComponent1 = () => {
  return <Icon type="icon-unknown-device" className={styles.node} />;
};

register({
  shape: NODE_SHAPE_WEB_NETWORK,
  effect: ['data'],
  component: WebNetworkNode,
  ports: {
    groups: {
      center: {
        position: [48, 19],
        attrs: {
          circle: {
            r: 24,
            magnet: true,
            stroke: 'transparent',
            fill: 'transparent',
          },
        },
      },
      left: {
        attrs: {
          circle: {
            magnet: true,
            stroke: '#D9D9D9FF',
            r: 4,
          },
        },
      },
      right: {
        position: [70, 19],
        attrs: {
          circle: {
            magnet: true,
            stroke: '#D9D9D9FF',
            r: 4,
          },
        },
      },
      top: {
        position: [48, -6],
        attrs: {
          circle: {
            magnet: true,
            stroke: '#D9D9D9FF',
            r: 4,
          },
        },
      },
      bottom: {
        position: [48, 42],
        attrs: {
          circle: {
            magnet: true,
            stroke: '#D9D9D9FF',
            r: 4,
          },
        },
      },
    },
  },
});

register({
  shape: NODE_SHAPE_UNKNOWN_DEVICE,
  component: NodeComponent1,
});

// 创建并选中节点
const createAndSelectNode = (graph: Graph, data: API.MonitoringObjectVO) => {
  const node = graph.createNode({
    width: NODE_WEIGHT,
    height: NODE_HEIGHT,
    shape: NODE_SHAPE_WEB_NETWORK,
    ports: {
      items: [
        {
          id: `port_${data.hostId}_center`,
          group: 'center',
        },
        // {
        //   id: `port_${data.hostid}_right`,
        //   group: 'right',
        // },
        // {
        //   id: `port_${data.hostid}_top`,
        //   group: 'top',
        // },
        // {
        //   id: `port_${data.hostid}_bottom`,
        //   group: 'bottom',
        // },
      ],
    },

    data,
  });

  // 选中节点
  graph.isSelectionEmpty();
  graph.select(node);

  return node;
};

const Topology = () => {
  const [drawerVisit, setDrawerVisit] = useState(false);
  const selectedNodeRef = useRef<HostNode>();

  const { id = '' } = useParams();
  const { pathname } = useLocation();
  const isDetailsPage = pathname.includes('details');
  const isFullScreen = pathname.includes('full');

  const containerRef = useRef<HTMLDivElement>(null);
  const graphRef = useRef<Graph>();
  const dndRef = useRef<Dnd>();

  // const [selectedNode, setSelectNode] = useState<HostNode>();
  const [hostIds, setHostIds] = useState<string[]>([]);
  const [allProblemHostIds, setAllProblemHostIds] = useState<Array<string | undefined>>([]);
  // 查询和保存
  const { data: topologyInfo } = useRequest(() => getTopology({ id }));
  const { run: save } = useRequest((topologyJson) => updateTopology({ id }, { topologyJson }), {
    manual: true,
    onSuccess: onSuccess,
    formatResult: (res) => res,
  });

  // 获取trigger
  const { run: getTriggers } = useRequest(
    (hostids) =>
      zabbixList({
        method: 'trigger.get',
        hostids,
        monitored: '1',
        skipDependent: true, // 跳过处于问题状态且依赖于其他触发器的触发器
        selectHosts: 'extend',
        output: ['triggerid', 'value'],
      }),
    {
      manual: true,
      onSuccess: (res: RK_API.Trigger[]) => {
        const problemHost = res?.filter((item) => item.value === '1')?.map((item) => item.hosts);
        const ids = problemHost?.flatMap((item) => item).map((item) => item?.hostid);
        setAllProblemHostIds(ids);
      },
    },
  );

  // 更新问题节点

  const updateNodesHostIds = (hostIds: string[]) => {
    // Ensure graphRef has been initialized
    if (!graphRef.current) {
      console.warn('Graph is not initialized');
      return;
    }
    // 遍历图中的所有节点
    graphRef.current.getNodes().forEach((node) => {
      const nodeData = node.getData();
      const problemCount = hostIds?.filter((item) => item === nodeData?.hostid)?.length || 0;

      node.updateData({ ...nodeData, problem: hostIds.includes(nodeData.hostid), problemCount });
      graphRef.current?.updateBackground();
    });
  };

  useEffect(() => {
    updateNodesHostIds(allProblemHostIds as string[]);
  }, [allProblemHostIds]);

  const startDrag = (
    e: React.MouseEvent<HTMLDivElement, MouseEvent>,
    data: API.MonitoringObjectVO,
  ) => {
    const target = e.currentTarget;
    const type = target.getAttribute('data-type');
    if (!graphRef.current) {
      console.warn('Graph is not initialized');
      return;
    }

    const node =
      type === 'web-network'
        ? createAndSelectNode(graphRef.current, data)
        : graphRef.current.createNode({
            width: NODE_WEIGHT,
            height: NODE_HEIGHT,
            shape: NODE_SHAPE_UNKNOWN_DEVICE,
          });

    if (node) {
      dndRef.current?.start(node, e.nativeEvent as any);
    }
  };
  useEffect(() => {
    if (graphRef.current) {
      graphRef.current.dispose();
    }
    graphRef.current = new Graph({
      container: containerRef.current!,
      background: {
        color: '#F2F7FA',
      },
      width: 1000,
      autoResize: true,
      grid: {
        size: 20,
        visible: true,
      },
      panning: {
        enabled: true,
        eventTypes: ['leftMouseDown'],
      },
      interacting: {
        nodeMovable: !isDetailsPage,
      },
      mousewheel: {
        enabled: true,
        modifiers: ['ctrl', 'meta'],
        maxScale: 1.5,
        minScale: 0.5,
      },
      connecting: {
        allowBlank: false,
        allowEdge: false,
        allowMulti: false,
        allowNode: false,
        allowLoop: false,
        allowPort: true,
        snap: {
          radius: 20,
        }, // 自动吸附
        createEdge(this: Graph, { sourceCell }) {
          const { problem } = sourceCell.getData();
          const color = problem ? '#faad14' : '#89E1E1';
          return this.createEdge({
            shape: 'edge',
            // connector: { name: 'normal' },
            router: {
              name: 'orth',
              args: {
                padding: {
                  left: 50,
                },
              },
            },
            attrs: {
              line: {
                stroke: color,
                strokeWidth: 1,
                strokeDasharray: '5',
                // style: {
                //   animation: 'running-line 30s infinite linear',
                // },
                targetMarker: {
                  name: 'block',
                  size: 6,
                  fill: color,
                  stroke: color,
                },
              },
            },
          });
        },
      },
      highlighting: {
        // 连接桩可以被连接时在连接桩外围围渲染一个包围框
        magnetAvailable: {
          name: 'stroke',
          args: {
            attrs: {
              fill: '#fff',
              stroke: '#A4DEB1',
              strokeWidth: 8,
            },
          },
        },
        // 连接桩吸附连线时在连接桩外围围渲染一个包围框
        magnetAdsorbed: {
          name: 'stroke',
          args: {
            attrs: {
              fill: '#fff',
              stroke: '#31d0c6',
              strokeWidth: 8,
            },
          },
        },
      },
    });

    // 对齐线
    graphRef.current.use(
      new Snapline({
        enabled: true,
      }),
    );

    // 框选
    graphRef.current.use(
      new Selection({
        enabled: !isDetailsPage,
        multiple: !isDetailsPage,
        modifiers: 'shift',
        rubberband: true,
        showNodeSelectionBox: true,
      }),
    );
    // 快捷键
    graphRef.current.use(
      new Keyboard({
        enabled: !isDetailsPage,
      }),
    );

    // 删除节点
    graphRef.current.bindKey('backspace', () => {
      const cells = graphRef.current?.getSelectedCells() || [];
      const ids: string[] = [];
      cells.forEach((cell) => {
        if (cell.isNode()) {
          const { hostid = '' } = cell.getData() || {};
          if (hostid) ids.push(hostid);
        }

        graphRef.current?.removeCell(cell);
      });
      setHostIds((preState) => {
        return preState.filter((item) => !ids.includes(item));
      });
    });

    // 小地图

    graphRef.current.use(
      new MiniMap({
        container: document.getElementById('minimapContainer')!,
        width: 300,
        height: 250,
      }),
    );

    // dnd进行初始化（在graph之后初始化）
    dndRef.current = new Dnd({
      target: graphRef.current,
      scaled: false,
    });

    // 监听节点选中事件
    // graphRef.current.on('node:selected', ({ cell }) => {
    //   const nodeData = cell.getData();
    //   setSelectNode(nodeData);
    // });

    // 监听节点双击事件
    graphRef.current.on('node:dblclick', ({ cell }) => {
      const nodeData = cell.getData();
      selectedNodeRef.current = nodeData;
      setDrawerVisit(true);
    });

    // 监听画布点击事件
    // graphRef.current.on('blank:click', () => {
    //   setSelectNode(undefined);
    // });

    // 监听节点添加事件
    graphRef.current.on('node:added', () => {
      const nodes = graphRef.current?.getNodes();
      const ids: string[] =
        nodes?.map((item) => item.getData().hostid || item.getData().hostId) || [];
      setHostIds(ids);
      getTriggers(ids);
    });

    // 节点数据变化
    graphRef.current.on('node:change:data', ({ node }) => {
      const edges = graphRef.current?.getIncomingEdges(node);
      const { problem } = node.getData();
      const color = problem ? '#faad14' : '#89E1E1';
      edges?.forEach((edge) => {
        edge.attr('line/stroke', color);
        edge.attr('line/targetMarker/fill', color);
        edge.attr('line/targetMarker/stroke', color);
        edge.attr('line/strokeDasharray', 5);
        // edge.attr('line/style/animation', 'running-line 30s infinite linear');
      });
    });

    // 销毁时清理资源
    // return () => {
    //   graphRef.current?.dispose();
    // };
  }, []);

  // 初始化
  useEffect(() => {
    const nodes = topologyInfo?.topologyJson?.cells?.filter(
      (item: Cell.Properties) => item.shape === NODE_SHAPE_WEB_NETWORK,
    );
    const ids = nodes?.map((item: Cell.Properties) => item?.data?.hostid || item?.data?.hostId);
    // 获取问题列表
    setHostIds(ids);
    getTriggers(ids);
    if (topologyInfo?.topologyJson?.cells?.length) {
      graphRef.current?.fromJSON(topologyInfo?.topologyJson);
      graphRef.current?.centerContent(); // 居中显示
    }
  }, [topologyInfo]);

  const toolbarHandlerClick = useCallback((key: string) => {
    if (key === 'center') {
      graphRef.current?.centerContent();
    } else if (key === 'zoomIn') {
      graphRef.current?.zoom(0.2);
    } else if (key === 'zoomOut') {
      graphRef.current?.zoom(-0.2);
    } else if (key === 'actualSize') {
      graphRef.current?.zoomTo(1);
    } else if (key === 'save') {
      const data = graphRef.current?.toJSON();
      save(data);
    } else if (key === 'clean') {
      graphRef.current?.clearCells();
      setHostIds([]);
    }
  }, []);

  return (
    <PageContainer
      header={{
        title: false,
      }}
      // breadcrumb={
      //   {
      // items: [
      //   {
      //     title: '监控拓扑',
      //     href: 'monitor/topology',
      //   },
      //   {
      //     title: topologyInfo?.name,
      //   },
      // ],
      // }
      // }
      breadcrumb={{
        items: isMicroservice
          ? [
              {
                href: '/union-monitor-platform/monitor',
                title: '监控中心',
              },
              {
                href: '/union-monitor-platform/monitor/topology',
                title: '监控拓扑',
              },
              {
                title: topologyInfo?.name,
              },
            ]
          : [
              {
                href: '/monitor',
                title: '监控中心',
              },
              {
                href: '/monitor/topology',
                title: '监控拓扑',
              },
              {
                title: topologyInfo?.name,
              },
            ],
      }}
      breadcrumbRender={(props, defaultDom) => {
        console.log('🚗 🚗 🚗 ~ Topology ~ props:', props, defaultDom);
        return defaultDom;
      }}
      className={cx('pageContainer', {
        full_container: isFullScreen,
      })}
    >
      <div className={cx('screen_box')}>
        {isFullScreen ? (
          <Link to={`/monitor/topology/${isDetailsPage ? 'details' : 'edit'}/${id}`}>
            <Tooltip
              title="退出全屏"
              autoAdjustOverflow
              getPopupContainer={(triggerNode) => triggerNode}
            >
              <FullscreenExitOutlined className={styles.icon} />
            </Tooltip>
          </Link>
        ) : (
          <Link to={`/monitor/topology/${isDetailsPage ? 'details' : 'edit'}/full/${id}`}>
            <Tooltip
              title="全屏"
              autoAdjustOverflow
              getPopupContainer={(triggerNode) => triggerNode}
            >
              <FullscreenOutlined className={styles.icon} />
            </Tooltip>
          </Link>
        )}
      </div>
      <div className={cx('editor', { read_only: isDetailsPage })}>
        {!isDetailsPage && (
          <Row className={styles['editor-header']}>
            <Col span={24}>
              <EditorToolbar onClick={toolbarHandlerClick} />
            </Col>
          </Row>
        )}
        <div className={styles['editor-body']}>
          {!isDetailsPage && <TopologyItemPanel startDrag={startDrag} hostIds={hostIds} />}

          <div className={styles['container']}>
            <div ref={containerRef} style={{ width: '100%', height: '100%' }}></div>
          </div>
        </div>
        {!isDetailsPage && <EditorMinimap />}
      </div>
      {/* 详情面板 */}
      <TopologyDetailPanel
        open={drawerVisit}
        onOpenChange={setDrawerVisit}
        initialValues={selectedNodeRef.current}
      />
    </PageContainer>
  );
};

export default memo(Topology);
