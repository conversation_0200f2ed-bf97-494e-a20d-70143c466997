// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 统计 POST /api/v1/rump/user/group/count */
export async function userGroupCount(
  body: API.UserGroupGetReqeust,
  options?: { [key: string]: any },
) {
  return request<API.ResultInteger>('/api/v1/rump/user/group/count', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建 POST /api/v1/rump/user/group/create */
export async function userGroupCreate(
  body: API.UserGroupCreateRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultUserGroupIdDTO>('/api/v1/rump/user/group/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除 POST /api/v1/rump/user/group/delete */
export async function userGroupDelete(body: API.BatchIdRequest, options?: { [key: string]: any }) {
  return request<API.ResultUserGroupIdDTO>('/api/v1/rump/user/group/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询 POST /api/v1/rump/user/group/get */
export async function userGroupGet(
  body: API.UserGroupGetReqeust,
  options?: { [key: string]: any },
) {
  return request<API.ResultListUserGroupDTO>('/api/v1/rump/user/group/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改 POST /api/v1/rump/user/group/update */
export async function userGroupUpdate(
  body: API.UserGroupUpdateRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultUserGroupIdDTO>('/api/v1/rump/user/group/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
