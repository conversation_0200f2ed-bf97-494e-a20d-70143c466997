import {
  ActionType,
  PageContainer,
  ProColumns,
  ProConfigProvider,
  ProTable,
} from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import ImportButton from '@/components/ImportButton.tsx';
import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import useExportApi from '@/hooks/useExportApi';
import { deleteUsingPost } from '@/services/http/template';
import { zabbix } from '@/services/zabbix';
import { passParamsToPage, queryPagingTable, syncToUrl } from '@/utils';
import { hostGroups, template } from '@/utils/column';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { history, useRequest } from '@umijs/max';
import { Button, message, Modal, Space, Tag } from 'antd';
import { valueTypeMap } from '../HostMonitor/valueTypeMap';

const actions = [
  {
    key: 'export',
    label: '导出',
  },
];

const Template: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<RK_API.TemplateGetDTO[]>([]);

  // 删除
  const { run: deleteRecord } = useRequest((ids) => deleteUsingPost({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: RK_API.TemplateGetDTO[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.templateid!);
      names.push(item.name!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除模版“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 导出
  const { run: exportTemplate } = useExportApi();
  const onOperation = (key: string | number, selectedRows: RK_API.TemplateGetDTO[]) => {
    const ids = selectedRows.map((item) => item.templateid!);
    if (key === 'export') {
      exportTemplate({
        options: {
          templates: ids,
        },
        fileName: 'rkmon_templates',
        format: 'xml',
      });
    }
  };

  // 表格
  const columns: ProColumns<RK_API.TemplateGetDTO>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      width: 150,
      render: (dom, record) => {
        return (
          <a
            onClick={() => {
              history.push(`/monitor-config/template/edit/${record.templateid}`);
            }}
          >
            {record.name}
          </a>
        );
      },
      initialValue: queryParams.get('name'),
    },
    {
      title: '监控项',
      dataIndex: 'items',
      hideInSearch: true,
      render(dom, entity) {
        const { items = [] } = entity;
        return (
          <a
            onClick={() =>
              passParamsToPage('/monitor-config/template/monitor-item', {
                templateids: entity.templateid,
              })
            }
          >
            {items.length ? `${items.length}项` : '监控项'}
          </a>
        );
      },
    },
    {
      title: '触发器',
      dataIndex: 'triggers',
      hideInSearch: true,
      render(dom, entity) {
        const { triggers = [] } = entity;
        return <a>{triggers.length ? `${triggers.length}项` : '触发器'}</a>;
      },
    },
    // TODO
    // {
    //   title: '自动发现',
    //   dataIndex: 'discoveries',
    //   hideInSearch: true,
    //   render(dom, entity) {
    //     const { discoveries = [] } = entity;
    //     return <a>{discoveries.length ? `${discoveries.length}项` : '自动发现'}</a>;
    //   },
    // },

    {
      title: '标签',
      dataIndex: 'tags',
      width: 250,
      hideInSearch: true,
      render(dom, entity) {
        const { tags = [] } = entity;
        if (!tags.length) return <>-</>;
        return (tags as RK_API.TemplateTag[]).map((item, index) => (
          <Tag key={index}>
            {item?.tag}: {item?.value}
          </Tag>
        ));
      },
    },
    // 筛选项
    { ...hostGroups, initialValue: queryParams.getAll('groupids') },
    {
      ...template,
      title: '链接的模版',
      dataIndex: 'parentTemplateids',
      hideInTable: false,
      width: 100,
      render(dom, record) {
        const { parentTemplates = [] } = record;
        if (!parentTemplates.length) return '';
        return parentTemplates.map((item: RK_API.TemplateGetDTO, index: number) => (
          <React.Fragment key={item?.templateid}>
            {index > 0 && <a>,</a>}
            <Button
              type="link"
              onClick={() => {
                history.push(`/monitor-config/template/edit/${item?.templateid}`);
              }}
            >
              {item?.name}
            </Button>
          </React.Fragment>
        ));
      },
      initialValue: queryParams.getAll('parentTemplateids'),
    },
    {
      title: '标签',
      dataIndex: 'tags',
      hideInTable: true,
      colSize: 3,
      // @ts-ignore
      valueType: 'conditionSetter',
      search: {
        transform: (value) => ({
          tags: value?.conditionList || [],
          evaltype: value?.evaltype,
        }),
      },
    },
    {
      title: '操作',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (text, record) => {
        return (
          <Space>
            <a key="del" onClick={() => handleDelete([record])}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProConfigProvider valueTypeMap={valueTypeMap}>
        <ProTable<RK_API.TemplateGetDTO>
          {...defaultTableConfig}
          search={SearchOptionRender}
          onSubmit={syncToUrl}
          rowKey="templateid"
          actionRef={tableRef}
          rowSelection={{
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRows(selectedRows);
            },
          }}
          columns={columns}
          headerTitle="模版列表"
          toolbar={{
            actions: [
              <ImportButton
                key="import"
                params={{
                  format: 'xml',
                  rules: {
                    templates: {
                      createMissing: true,
                      updateExisting: true,
                    },
                  },
                }}
              />,
              <Button
                key="add"
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  history.push('/monitor-config/template/add');
                }}
              >
                新建
              </Button>,
            ],
          }}
          request={async (params) => {
            const { groupids = [], tags = [], evaltype, parentTemplateids = [], ...rest } = params;
            return queryPagingTable<RK_API.TemplateGetDTO>(
              {
                tags: tags.filter((item: RK_API.TemplateTag) => item.tag),
                evaltype,
                groupids: groupids?.length ? groupids : null,
                parentTemplateids: parentTemplateids?.length ? parentTemplateids : null,
                search: rest,
                selectItems: 'extend',
                selectTriggers: 'extend',
                selectParentTemplates: 'extend',
                selectTags: 'extend',
                selectDiscoveries: 'extend',
                sortfield: 'name',
                method: 'template.get',
              },
              zabbix,
            );
          }}
        />
      </ProConfigProvider>
      <OperateFooterToolbar
        selectedRows={selectedRows}
        onDelete={handleDelete}
        actions={actions}
        onOperation={onOperation}
      />
    </PageContainer>
  );
});

export default Template;
