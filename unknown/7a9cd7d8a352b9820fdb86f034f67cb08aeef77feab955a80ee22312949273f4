import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef } from 'react';

import { ALERT_COMMAND_STATUS, ALERT_MSG_STATUS } from '@/enums';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import { zabbix } from '@/services/zabbix';
import { queryPagingTable, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { Typography } from 'antd';
import dayjs from 'dayjs';

// 获取今天的日期
const today = dayjs();

const ActionLog: React.FC = withStorageToUrl(() => {
  const tableRef = useRef<ActionType | undefined>();

  // 表格
  const columns: ProColumns<any>[] = [
    // TODO 增加组件  设置initialValue
    // {
    //   title: '时间段',
    //   dataIndex: 'dateTime',
    //   hideInTable: true,
    // },
    {
      title: '时间',
      dataIndex: 'clock',
      renderText(text) {
        return dayjs.unix(text).format('YYYY-MM-DD HH:mm:ss');
      },
      hideInSearch: true,
    },
    {
      title: '类型',
      dataIndex: 'mediatypes',
      renderText(text) {
        return text?.at(0)?.name;
      },
      hideInSearch: true,
    },
    {
      title: '接收者',
      width: 220,
      dataIndex: 'sendto',
      hideInSearch: true,
    },
    {
      title: '主机',
      dataIndex: 'message',
      hideInTable: true,
    },
    {
      title: '消息',
      width: 400,
      dataIndex: 'subject',
      hideInSearch: true,
      render(dom, entity) {
        return (
          <>
            <Typography.Text strong>主题</Typography.Text>
            <div style={{ marginBottom: 10 }}>{dom}</div>
            <Typography.Text strong>消息</Typography.Text>
            <div
              style={{
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
              }}
              dangerouslySetInnerHTML={{ __html: entity.message }}
            ></div>
          </>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      hideInSearch: true,
      render(dom, entity) {
        const options = entity.alerttype === '0' ? ALERT_MSG_STATUS : ALERT_COMMAND_STATUS;
        const label = options.find((item) => item.value === entity.status)?.label;
        return label;
      },
    },
    {
      title: '错误',
      hideInSearch: true,
      dataIndex: 'error',
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<any>
        {...defaultTableConfig}
        search={SearchOptionRender}
        onSubmit={syncToUrl}
        rowKey="alertid"
        actionRef={tableRef}
        columns={columns}
        headerTitle="动作日志"
        request={async (params) => {
          const {
            name,
            message,
            groupids,
            evaltype,
            tags = [],
            severities = [],
            maintenance_status,
            withProblemsSuppressed,
          } = params;
          return queryPagingTable<RK_API.Host>(
            {
              search: {
                name,
                message,
              },

              maintenance_status,
              withProblemsSuppressed,
              severities: severities.length ? severities : null,
              groupids: groupids?.length ? groupids : null,
              evaltype,
              tags: tags.filter((item: RK_API.TemplateTag) => item.tag),
              time_from: today.startOf('day').unix(),
              time_till: today.endOf('day').unix(),
              selectMediatypes: 'extend',
              method: 'alert.get',
              sortfield: 'clock',
            },
            zabbix,
          );
        }}
      />
    </PageContainer>
  );
});

export default ActionLog;
