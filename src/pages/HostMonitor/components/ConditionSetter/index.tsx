import { TAG_CONDITION, TAG_EVAL_TYPE } from '@/enums';
import { getRandomId } from '@/utils';
import { DeleteOutlined } from '@ant-design/icons';
import { ProFieldFCRenderProps } from '@ant-design/pro-components';
import { Input, Radio, Select, Space } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';

type Item = {
  id: string;
  tag: string;
  operator: string;
  value: string;
};

const ConditionSetter: React.FC<ProFieldFCRenderProps> = (props) => {
  const [conditionList, setConditionList] = useState([
    {
      id: getRandomId(),
      tag: '',
      operator: '0',
      value: '',
    },
  ]);
  const [evaltype, setEvalType] = useState(0);

  const setConfig = (index: number, val: string, key: keyof Item) => {
    const list = [...conditionList];
    list[index][key] = val;
    setConditionList(list);
  };

  const addColumn = () => {
    const list = [
      ...conditionList,
      {
        id: getRandomId(),
        tag: '',
        operator: '0',
        value: '',
      },
    ];
    setConditionList(list);
  };

  const removeColumn = (id: string) => {
    const list = conditionList.filter((item) => item.id !== id);
    setConditionList(list);
  };

  useEffect(() => {
    props.fieldProps.onChange?.({
      conditionList,
      evaltype,
    });
  }, [evaltype, conditionList]);

  return (
    <div className={styles.container}>
      <Radio.Group
        name="evaltype"
        options={TAG_EVAL_TYPE}
        optionType="button"
        buttonStyle="solid"
        value={evaltype}
        className={styles.radio}
        onChange={(e) => {
          setEvalType(e.target.value);
        }}
      />
      {conditionList.map((item, index) => {
        const { tag, operator, value, id } = item;
        return (
          <div key={id} className={styles.col}>
            <Space>
              <Input
                width={180}
                value={tag}
                onChange={(event) => setConfig(index, event.target.value, 'tag')}
                placeholder="标记"
                allowClear
              />
              <Select
                placeholder="请选择条件"
                style={{ width: 180 }}
                defaultValue="0"
                value={operator}
                onChange={(value) => setConfig(index, value, 'operator')}
                options={TAG_CONDITION}
              ></Select>
              <Input
                width={180}
                value={value}
                onChange={(event) => setConfig(index, event.target.value, 'value')}
                placeholder="值"
                allowClear
              />
              <DeleteOutlined
                className={styles['remove-btn']}
                onClick={() => {
                  removeColumn(id);
                }}
              />
            </Space>
          </div>
        );
      })}
      <div className={styles['add-btn']} onClick={addColumn}>
        新建
      </div>
    </div>
  );
};

export default ConditionSetter;
