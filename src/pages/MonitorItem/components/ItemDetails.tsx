import RKCol from '@/components/RKCol';
import TagSetter from '@/components/TagsSetter';
import UnitInput from '@/components/UnitInput';
import { DATE_UNIT, HOST_ASSETS, MONITOR_DATA_TYPE, MONITOR_TYPE } from '@/enums';
import { zabbix } from '@/services/zabbix';
import { onSuccessAndGoBack, queryFormData } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { history, useParams, useRequest, useSearchParams } from '@umijs/max';
import { Divider, Row } from 'antd';
import React, { useRef, useState } from 'react';
import ItemKeysSetter from './ItemKeysSetter';

const ItemDetails: React.FC = () => {
  const formRef = useRef<ProFormInstance>();

  // 判断是否为编辑页面
  const { id } = useParams();

  const [getSearchArr] = useSearchParams();

  const isEditPage = !!id;

  const [modalVisit, setModalVisit] = useState(false);

  // 新建
  const { run: add, loading: addLoading } = useRequest(
    (value) => zabbix({ ...value, method: 'item.create' }),
    {
      manual: true,
      onSuccess: onSuccessAndGoBack,
      formatResult: (res) => res,
    },
  );
  // 修改
  const { run: update, loading: updateLoading } = useRequest(
    (value) => zabbix({ ...value, method: 'item.update' }),
    {
      manual: true,
      onSuccess: onSuccessAndGoBack,
      formatResult: (res) => res,
    },
  );

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      {/* <ProBreadcrumb
        itemRender={(route) => {
          const { breadcrumbName, linkPath } = route;
          console.log(
            '🚗 🚗 🚗 ~ file: ItemDetails.tsx:49 ~ breadcrumbName, linkPath:',
            breadcrumbName,
            linkPath,
          );
          return (
            <a
              onClick={() => {
                history.push(linkPath);
              }}
            >
              {breadcrumbName}
            </a>
          );
        }}
      /> */}

      <ProForm<RK_API.Item>
        formRef={formRef}
        initialValues={{
          history: '90d',
          delay: '1m',
          trends: '365d',
          inventory_link: '0',
          value_type: '3',
          hostid: getSearchArr.getAll('hostid').at(0),
        }}
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          onReset: () => {
            history.go(-1);
          },

          render: (props, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
          submitButtonProps: {
            loading: addLoading || updateLoading,
          },
        }}
        onFinish={async (values) => {
          if (isEditPage) {
            update(values);
          } else {
            add(values);
          }
        }}
        request={() =>
          queryFormData(
            {
              itemids: [id],
              selectTags: 'extend',
              method: 'item.get',
            },
            isEditPage,
            zabbix,
          )
        }
      >
        {/* 不需要展示，只是为了form传值 */}
        <div className="rk-none">
          <ProFormText name="itemid" />
          <ProFormText name="hostid" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText label="名称" name="name" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormSelect label="类型" name="type" options={MONITOR_TYPE} rules={[requiredRule]} />
          </RKCol>
          <RKCol lg={12} md={16} sm={24}>
            <ProFormText
              label={
                <span>
                  键值
                  <em className="rk-optional">（双击输入框，快速选择）</em>
                </span>
              }
              name="key_"
              fieldProps={{
                onDoubleClick: () => {
                  setModalVisit(true);
                },
              }}
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              label="数据类型"
              name="value_type"
              rules={[requiredRule]}
              options={MONITOR_DATA_TYPE}
            />
          </RKCol>
          <RKCol>
            <ProFormText label="单位" name="units" />
          </RKCol>
          <RKCol>
            <ProForm.Item label="更新间隔" tooltip="更新监控项的时间间隔" name="delay">
              <UnitInput options={DATE_UNIT} />
            </ProForm.Item>
          </RKCol>
          <RKCol>
            <ProForm.Item label="历史数据保留时长" name="history">
              <UnitInput options={DATE_UNIT} />
            </ProForm.Item>
          </RKCol>
          <RKCol>
            <ProForm.Item label="趋势存储时间" name="trends">
              <UnitInput options={DATE_UNIT} />
            </ProForm.Item>
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="inventory_link"
              label="填入主机资产纪录栏位"
              options={HOST_ASSETS}
              fieldProps={{
                showSearch: true,
              }}
            />
          </RKCol>
          <RKCol>
            <ProFormTextArea
              name="description"
              label="描述"
              fieldProps={{ autoSize: { minRows: 1, maxRows: 2 } }}
            />
          </RKCol>
          <RKCol>
            <ProFormSwitch
              label="启用"
              name="status"
              getValueFromEvent={(val) => (val ? '0 ' : '1')}
              getValueProps={(value) => ({ checked: value === '0' })}
            />
          </RKCol>
        </Row>

        <Divider />
        {/* 标记 */}
        <TagSetter />
      </ProForm>
      {/* 键值配置 */}
      <ItemKeysSetter
        open={modalVisit}
        onOpenChange={setModalVisit}
        onRowClick={(row) => {
          formRef.current?.setFieldValue('key_', row.key_);
          setModalVisit(false);
        }}
      />
    </PageContainer>
  );
};

export default ItemDetails;
