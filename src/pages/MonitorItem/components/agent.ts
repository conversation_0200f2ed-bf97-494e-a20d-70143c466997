export const agent = [
  {
    key_: 'agent.hostmetadata',
    description: '返回 HostMetadata 或 HostMetadataItem 参数的值，如果未定义则返回空字符串。',
    type: ['0', '7'],
  },
  {
    key_: 'agent.hostname',
    description: '客户端主机名，返回字符串。',
    type: ['0', '7'],
  },
  {
    key_: 'agent.ping',
    description: '客户端可达性检查，返回 nothing - 不可达；1 - 可达；',
    type: ['0', '7'],
  },
  {
    key_: 'agent.variant',
    description: '代理的变体，返回 1 - rkmon agent； 2 - rkmon agent 2；',
    type: ['0', '7'],
  },
  {
    key_: 'agent.version',
    description: 'rkmon客户端（agent）的版本，返回字符串。',
    type: ['0', '7'],
  },
  {
    key_: 'eventlog[name,<regexp>,<severity>,<source>,<eventid>,<maxlines>,<mode>]',
    description: '事件日志监控，返回日志。',
    type: ['7'],
  },
  {
    key_: 'kernel.maxfiles',
    description: '操作系统最大的文件打开数量，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'kernel.maxproc',
    description: '操作系统最大的进程数，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'kernel.openfiles',
    description: '当前打开文件句柄数，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'log.count[file,<regexp>,<encoding>,<maxproclines>,<mode>,<maxdelay>,<options>,<persistent_dir>]',
    description: '监控日志文件行数统计，返回整数。',
    type: ['7'],
  },
  {
    key_: 'log[file,<regexp>,<encoding>,<maxlines>,<mode>,<output>,<maxdelay>,<options>,<persistent_dir>]',
    description: '日志文件监控，返回日志。',
    type: ['7'],
  },
  {
    key_: 'logrt.count[file_regexp,<regexp>,<encoding>,<maxproclines>,<mode>,<maxdelay>,<options>,<persistent_dir>]',
    description: '日志文件监视中匹配行的计数与日志轮换支持，返回整型。',
    type: ['7'],
  },
  {
    key_: 'logrt[file_regexp,<regexp>,<encoding>,<maxlines>,<mode>,<output>,<maxdelay>,<options>,<persistent_dir>]',
    description: '日志文件监控与轮转服务，返回日志。',
    type: ['7'],
  },

  {
    key_: 'modbus.get[endpoint,<slaveid>,<function>,<address>,<count>,<type>,<endianness>,<offset>]',
    description: 'R读取Modbus数据， 返回JSON 对象。',
    type: ['0'],
  },
  {
    key_: 'mqtt.get[<broker_url>,topic]',
    description:
      'MQTT主题的值。返回数据的格式取决于主题内容。如果使用通配符，则以JSON形式返回主题值。',
    type: ['7'],
  },
  {
    key_: 'net.dns.record[<ip>,name,<type>,<timeout>,<count>,<protocol>]',
    description: '执行DNS查询，返回字符串信息。',
    type: ['0', '7'],
  },
  {
    key_: 'net.dns[<ip>,name,<type>,<timeout>,<count>,<protocol>]',
    description:
      '检查 DNS 服务是否开启，返回 0 - DNS 服务关闭（服务未响应或DNS解析失败）；1 - DNS 服务开启；',
    type: ['0', '7'],
  },
  {
    key_: 'net.if.collisions[if]',
    description: '网络冲突数量，返回整型。',
    type: ['0', '7'],
  },
  {
    key_: 'net.if.discovery',
    description: '网络接口列表，用于低级别的发现，返回JSON 对象。',
    type: ['0', '7'],
  },
  {
    key_: 'net.if.in[if,<mode>]',
    description: '网络接口上传流量统计，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'net.if.list',
    description: '网络接口列表（包括接口类型，状态，IPv4地址，说明），返回文本。',
    type: ['0', '7'],
  },
  {
    key_: 'net.if.out[if,<mode>]',
    description: '上行流量统计，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'net.if.total[if,<mode>]',
    description: '网络接口上传下载的流量总和，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'net.tcp.listen[port]',
    description: '检查 TCP 端口 是否处于侦听状态，返回 0 - 未侦听；1 - 正在侦听；',
    type: ['0', '7'],
  },
  {
    key_: 'net.tcp.port[<ip>,port]',
    description: '检查是否能建立 TCP 连接到指定端口。返回 0 - 不能连接；1 - 可以连接；',
    type: ['0', '7'],
  },
  {
    key_: 'icmpping[<target>,<packets>,<interval>,<size>,<timeout>]',
    description: '检查主机是否可以通过ICMP ping访问。0 - ICMP ping失败。1 - ICMP ping成功。',
    type: ['3'],
  },
  {
    key_: 'icmppingloss[<target>,<packets>,<interval>,<size>,<timeout>]',
    description: '返回ICMP ping包丢失的百分数。',
    type: ['3'],
  },
  {
    key_: 'icmppingsec[<target>,<packets>,<interval>,<size>,<timeout>,<mode>]',
    description: '返回ICMP ping的响应时间单位秒.例如：0.02',
    type: ['3'],
  },
  {
    key_: 'net.tcp.service.perf[service,<ip>,<port>]',
    description: '检查 TCP 服务的性能，当服务 down 时返回 0，否则返回连接服务花费的秒数',
    type: ['0', '7', '3'],
  },
  {
    key_: 'net.tcp.service[service,<ip>,<port>]',
    description: '检查服务是否运行并接受 TCP 连接。返回 0 - 服务关闭；1 - 服务运行；',
    type: ['0', '7', '3'],
  },
  {
    key_: 'net.tcp.socket.count[<laddr>,<lport>,<raddr>,<rport>,<state>]',
    description: '返回与参数匹配的TCP套接字数目，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'net.udp.listen[port]',
    description: '检查 UDP 端口 是否处于侦听状态。返回 0 - 未侦听；1 - 正在侦听；',
    type: ['0', '7'],
  },
  {
    key_: 'net.udp.service.perf[service,<ip>,<port>]',
    description: '检查 UDP 服务的性能，当服务 down 时返回 0，否则返回连接到服务花费的秒数。',
    type: ['0', '7', '3'],
  },
  {
    key_: 'net.udp.service[service,<ip>,<port>]',
    description: '检查服务是否运行并响应 UDP 请求。返回 0 - 服务关闭；1 - 服务运行；',
    type: ['0', '7', '3'],
  },
  {
    key_: 'vmware.cl.perfcounter[<url>,<id>,<path>,<instance>]',
    description:
      'VMware集群性能计数器 <url>  - VMware服务UR <id>  - VMware集群id <path>  - 性能计数器路径 <instance>  - 性能计数器实例',
    type: ['3'],
  },
  {
    key_: 'vmware.cluster.discovery[<url>]',
    description: '发现VMware集群 <url> - VMware服务URL, 返回JSON',
    type: ['3'],
  },
  {
    key_: 'vmware.cluster.status[<url>,<name>]',
    description: 'VMware集群状态, <url> - VMware 服务 URL, <name> - VMware 集群名称',
    type: ['3'],
  },
  {
    key_: 'vmware.datastore.discovery[<url>]',
    description: '发现VMware数据存储, <url> - VMware服务URL. 返回JSON',
    type: ['3'],
  },
  {
    key_: 'vmware.datastore.hv.list[<url>,<datastore>]',
    description: 'VMware数据存储管理程序列表, <url> - VMware服务URL, <datastore> - 数据存储名称',
    type: ['3'],
  },
  {
    key_: 'vmware.datastore.read[<url>,<datastore>,<mode>]',
    description:
      'VMware数据存储读取统计信息, <url> - VMware服务URL, <datastore> - 数据存储名称, <mode> - 平均值或最大值',
    type: ['3'],
  },
  {
    key_: 'vmware.datastore.size[<url>,<datastore>,<mode>]',
    description: 'VMware数据存储容量统计信息，以字节为单位，以百分比表示。 返回整形; 浮点百分比',
    type: ['3'],
  },
  {
    key_: 'vmware.datastore.write[<url>,<datastore>,<mode>]',
    description:
      'VMware数据存储写统计, <url> - VMware服务URL, <datastore> - 数据存储名称, <mode> - 平均值或最大值',
    type: ['3'],
  },
  {
    key_: 'vmware.dc.discovery[<url>]',
    description: 'VMware数据中心及其id，返回JSON。',
    type: ['3'],
  },
  {
    key_: 'vmware.eventlog[<url>,<mode>]',
    description:
      'VMware事件日志, <url> - VMware服务URL, <mode> - all (默认值), skip - 跳过旧数据的处理',
    type: ['3'],
  },
  {
    key_: 'vmware.fullname[<url>]',
    description: 'VMware 服务完整名, <url> - VMware 服务 URL',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.cluster.name[<url>,<uuid>]',
    description:
      '	VMware hypervisor 集群名,<url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.cpu.usage.perf[<url>,<uuid>]',
    description:
      '在间隔期间的CPU使用率百分比, <url> - VMware 服务 UR, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.cpu.usage[<url>,<uuid>]',
    description:
      '	VMware hypervisor 处理器使用率单位Hz, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.cpu.utilization[<url>,<uuid>]',
    description:
      '间隔期间的CPU使用率百分比取决于电源管理或HT,<url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.datacenter.name[<url>,<uuid>]',
    description:
      'VMware hypervisor数据中心名称, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名 ，返回字符串。',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.datastore.discovery[<url>,<uuid>]',
    description:
      '发现VMware管理程序数据存储, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名 ，返回字符串。ON',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.datastore.list[<url>,<uuid>]',
    description:
      'VMware hypervisor数据存储列表, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.datastore.multipath[<url>,<uuid>,<datastore>,<partitionid>]',
    description:
      '可用DS路径数, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名, <datastore> - 数据存储名称, <partitionid> - vmware.hv.datastore.discovery中物理设备的内部id',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.datastore.read[<url>,<uuid>,<datastore>,<mode>]',
    description:
      'VMware hypervisor 数据池读统计,<url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名, <datastore> - 数据池名 ,<mode> - latency',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.datastore.size[<url>,<uuid>,<datastore>,<mode>]',
    description: 'VMware数据存储容量统计信息，以字节为单位，以百分比表示。 返回整形; 浮点百分比。',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.datastore.write[<url>,<uuid>,<datastore>,<mode>]',
    description:
      'VMware hypervisor 数据池写统计,<url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名, <datastore> - 数据池名 ,<mode> - latency',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.discovery[<url>]',
    description: 'Discovery of VMware hypervisors, <url> - VMware service URL. Returns JSON',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.fullname[<url>,<uuid>]',
    description:
      'VMware hypervisor 名称, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.fullname[<url>,<uuid>]',
    description:
      'VMware hypervisor 名称, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.hw.cpu.freq[<url>,<uuid>]',
    description:
      'VMware hypervisor 处理器频率, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.hw.cpu.model[<url>,<uuid>]',
    description:
      'VMware hypervisor 处理器型号, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.hw.cpu.num[<url>,<uuid>]',
    description:
      'VMware hypervisor的处理器内核数量,<url>-VMware服务URL,<uuid>-VMware hypervisor主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.hw.cpu.threads[<url>,<uuid>]',
    description:
      'VMware hypervisor的处理器线程数量,<url>-VMware服务URL,<uuid>-VMware hypervisor主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.hw.memory[<url>,<uuid>]',
    description:
      'VMware hypervisor 总内存大小, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.hw.model[<url>,<uuid>]',
    description:
      'VMware hypervisor 型号; <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.hw.uuid[<url>,<uuid>]',
    description:
      'VMware hypervisor BIOS UUID, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.hw.vendor[<url>,<uuid>]',
    description:
      'VMware hypervisor 供应商名, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.maintenance[<url>,<uuid>]',
    description:
      'VVMware hypervisor maintenance status, <url> - VMware service URL, <uuid> - VMware hypervisor host name. Returns 0 - not in maintenance; 1 - in maintenance',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.memory.size.ballooned[<url>,<uuid>]',
    description: 'VMware 虚拟机澎涨内存大小, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.memory.used[<url>,<uuid>]',
    description:
      'VMware hypervisor 已使用内存大小, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.network.in[<url>,<uuid>,<mode>]',
    description:
      'VMware hypervisor 网络进流量统计, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名, <mode> - bps',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.network.out[<url>,<uuid>,<mode>]',
    description:
      '	VMware hypervisor 网络输出流量统计, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名, <mode> - bps',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.perfcounter[<url>,<uuid>,<path>,<instance>]',
    description:
      '	VMware hypervisor性能计数器,<url>-VMware服务URL,<uuid>-VMware hypervisor主机名,<path>-性能计数器路径,<instance>-性能计数器实例',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.power[<url>,<uuid>,<max>]',
    description:
      'Power usage , <url> - VMware service URL, <uuid> - VMware hypervisor host name, <max> - Maximum allowed power usage',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.sensor.health.state[<url>,<uuid>]',
    description:
      'VMware hypervisor 运行状况汇总传感器, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名. 返回 0 - 灰色; 1 - 绿色; 2 - 黄色; 3 - 红色',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.sensors.get[<url>,<uuid>]',
    description:
      'VMware hypervisor 硬件厂商状态传感器, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名，返回JSON。',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.status[<url>,<uuid>]',
    description:
      'VMware hypervisor 状态, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.uptime[<url>,<uuid>]',
    description:
      'VMware hypervisor 在线时间, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.version[<url>,<uuid>]',
    description:
      'VMware hypervisor 版本, <url> - VMware 服务 URL, <uuid> - VMware hypervisor 主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.hv.vm.num[<url>,<uuid>]',
    description:
      'VMware hypervisor管理的虚拟机数量，<url>-VMware服务URL，<uuid>- VMware hypervisor主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.version[<url>]',
    description: 'VMware 服务版本, <url> - VMware 服务 URL',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.cluster.name[<url>,<uuid>]',
    description: 'VMware 虚拟机名, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.cpu.latency[<url>,<uuid>]',
    description:
      '	Percent of time the virtual machine is unable to run because it is contending for access to the physical CPU(s), <url> - VMware service URL, <uuid> - VMware virtual machine host name',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.cpu.num[<url>,<uuid>]',
    description: 'VMware 虚拟机的处理器数量,<url>-VMware服务URL,<uuid>-VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.cpu.readiness[<url>,<uuid>,<instance>]',
    description:
      'Percentage of time that the virtual machine was ready, but could not get scheduled to run on the physical CPU, <url> - VMware service URL, <uuid> - VMware virtual machine host name, <instance> - CPU instance',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.cpu.ready[<url>,<uuid>]',
    description: 'VMware虚拟机处理器准备时间ms，<url> - VMware服务URL，<uuid> - VMware虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.cpu.swapwait[<url>,<uuid>,<instance>]',
    description:
      'CPU time spent waiting for swap-in, <url> - VMware service URL, <uuid> - VMware virtual machine host name, <instance> - CPU instance',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.cpu.usage.perf[<url>,<uuid>]',
    description:
      '	在间隔期间的CPU使用率百分比, <url> - VMware 服务 URL, <uuid> - VMware virtual虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.cpu.usage[<url>,<uuid>]',
    description:
      'VMware 虚拟机处理器使用率，单位Hz, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.datacenter.name[<url>,<uuid>]',
    description:
      'VMware虚拟机数据中心名称, <url> - VMware服务URL, <uuid> - VMware虚拟机主机名. 返回字符串',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.discovery[<url>]',
    description: '发现VMware虚拟机, <url> - VMware服务URL，返回JSON',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.guest.memory.size.swapped[<url>,<uuid>]',
    description:
      '交换到交换空间的来宾物理内存量, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.guest.osuptime[<url>,<uuid>]',
    description:
      '自上次操作系统启动以来经过的总时间，以秒为单位, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.hv.name[<url>,<uuid>]',
    description: 'VMware 虚拟机hypervisor 名,<url> - VMware 服务 URL,<uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.memory.size.ballooned[<url>,<uuid>]',
    description: 'VMware 虚拟机澎涨内存大小, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.memory.size.compressed[<url>,<uuid>]',
    description: 'VMware虚拟机压缩内存大小, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.memory.size.consumed[<url>,<uuid>]',
    description:
      '用于备份客户物理内存页的主机物理内存量, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.memory.size.private[<url>,<uuid>]',
    description: 'VMware 虚拟机私有内存大小, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.memory.size.shared[<url>,<uuid>]',
    description: 'VMware 虚拟机共享内存大小, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.memory.size.swapped[<url>,<uuid>]',
    description: 'VMware 虚拟机交换分区大小, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.memory.size.usage.guest[<url>,<uuid>]',
    description: 'VMware 虚拟机内存使用率, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.memory.size.usage.host[<url>,<uuid>]',
    description:
      'VMware 虚拟机宿主机内存使用率, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.memory.size[<url>,<uuid>]',
    description: 'VMware 虚拟机总内存大小, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.memory.usage[<url>,<uuid>]',
    description: '已消耗的主机物理内存百分比, <url> - VMware 服务 URL, <uuid> - 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.net.if.discovery[<url>,<uuid>]',
    description:
      '发现VMware虚拟机网络接口, <url> - VMware 服务 URL, <uuid> - VMware虚拟机主机名，返回JSON',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.net.if.in[<url>,<uuid>,<instance>,<mode>]',
    description:
      'VMware 虚拟机网络接口进入流量统计, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名, <instance> - 网络接口名称, <mode> - bps/pps - bytes/packets per second',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.net.if.out[<url>,<uuid>,<instance>,<mode>]',
    description:
      '虚拟机网络接口流出流量统计, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名, <instance> - 网络接口名称, <mode> - bps/pps - bytes/packets per second',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.net.if.usage[<url>,<uuid>,<instance>]',
    description:
      '间隔期间的网络利用率(发送速率和接收速率的组合), <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名, <instance> - 网络接口实例',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.perfcounter[<url>,<uuid>,<path>,<instance>]',
    description:
      'VMware虚拟机性能计数器,<url>-VMware服务URL,<uuid>-VMware 虚拟机主机名,<path>-性能计数器路径,<instance>-性能计数器实例',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.powerstate[<url>,<uuid>]',
    description: 'VMware 虚拟机电源状态, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.storage.committed[<url>,<uuid>]',
    description:
      '	VMware 虚拟机已经使用的存储空间, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机名',
    type: ['3'],
  },
  {
    key_: '',
    description:
      '收集时间间隔内未完成的虚拟磁盘读请求数的平均值, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名, <instance> - 磁盘设备实例',
    type: ['3'],
  },
  {
    key_: '',
    description:
      '从虚拟磁盘读取所花费的平均时间, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名, <instance> - 磁盘设备实例',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.storage.totalwritelatency[<url>,<uuid>,<instance>]',
    description:
      '写入虚拟磁盘所需的平均时间, <url> - VMware 服务 URL, <uuid> - VMware虚拟机主机名, <instance> - 磁盘设备实例',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.storage.uncommitted[<url>,<uuid>]',
    description:
      '	VMware 虚拟机的未提交存储空间(uncommitted storage space), <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.storage.unshared[<url>,<uuid>]',
    description:
      'VMware 虚拟机非共享存储空间, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.storage.writeoio[<url>,<uuid>,<instance>]',
    description:
      '在收集时间间隔内未完成的虚拟磁盘写请求的平均数目, <url> - VMware 虚拟机 URL, <uuid> - VMware 虚拟机主机, <instance> - 磁盘设备实例',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.uptime[<url>,<uuid>]',
    description: 'VMware 虚拟机在线时间, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.vfs.dev.discovery[<url>,<uuid>]',
    description:
      '发现VMware虚拟机磁盘设备, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名，返回JSON。',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.vfs.dev.read[<url>,<uuid>,<instance>,<mode>]',
    description:
      'VMware 虚拟机磁盘设备读统计, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名, <instance> - 磁盘设备, <mode> - bps/ops - bytes/operations per second',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.vfs.dev.write[<url>,<uuid>,<instance>,<mode>]',
    description:
      'VMware 虚拟机磁盘设备写统计, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名, <instance> - 磁盘设备, <mode> - bps/ops - bytes/operations per second',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.vfs.fs.discovery[<url>,<uuid>]',
    description:
      '发现VMware虚拟机文件系统, <url> - VMware 服务 URL, <uuid> - VMware virtual 虚拟机主机名.返回JSON',
    type: ['3'],
  },
  {
    key_: 'vmware.vm.vfs.fs.size[<url>,<uuid>,<fsname>,<mode>]',
    description:
      'VMware 虚拟机文件系统统计, <url> - VMware 服务 URL, <uuid> - VMware 虚拟机主机名, <fsname> - 文件系统名, <mode> - total/free/used/pfree/pused',
    type: ['3'],
  },

  {
    key_: 'net.udp.socket.count[<laddr>,<lport>,<raddr>,<rport>,<state>]',
    description: '返回与参数匹配的UDP套接字的个数，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'perf_counter[counter,<interval>]',
    description: '所有Windows计数器值。返回 整形、浮点、字符串、文本。',
    type: ['0', '7'],
  },
  {
    key_: 'perf_counter_en[counter,<interval>]',
    description: '所有Windows性能计数器的英文值。返回整数、浮点数、字符串或文本(取决于请求)',
    type: ['0', '7'],
  },
  {
    key_: 'perf_instance.discovery[object]',
    description: 'Windows性能计数器的对象实例列表，返回JSON。',
    type: ['0', '7'],
  },
  {
    key_: 'perf_instance_en.discovery[object]',
    description: '使用英文对象名称发现的Windows性能计数器的对象实例列表，返回JSON。',
    type: ['0', '7'],
  },
  {
    key_: 'proc.cpu.util[<name>,<user>,<type>,<cmdline>,<mode>,<zone>]',
    description: '进程CPU利用率百分比，返回浮点数。',
    type: ['0', '7'],
  },
  {
    key_: 'proc.mem[<name>,<user>,<mode>,<cmdline>,<memtype>]',
    description: '进程内存，以字节为单位，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'proc.num[<name>,<user>,<state>,<cmdline>,<zone>]',
    description: '进程数，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'proc_info[process,<attribute>,<type>]',
    description: '具体处理的各个信息，返回浮点数。',
    type: ['0', '7'],
  },
  {
    key_: 'sensor[device,sensor,<mode>]',
    description: '硬件传感器读数，返回浮点型。',
    type: ['0', '7'],
  },
  {
    key_: 'service.info[service,<param>]',
    description:
      '有关服务的信息。返回整数字段表示状态，启动状态；字符串 - 以字段作为显示名，路径，用户; 文本 - 使用字段作为描述； 状态码：0 - 运行，1 - 暂停，2 - 启动挂起，3 - 暂停挂起，4 - 继续挂起，5 - 停止挂起，6 - 停止，7 - 未知，255 - 没有这个服务；启动码：0 - 自动，1 - 自动延迟，2 - 手动, 3 - 禁用，4 - 未知',
    type: ['0', '7'],
  },
  {
    key_: 'services[<type>,<state>,<exclude>]',
    description: '列表服务，返回0表示空，如果是列表则是每行一个内容。',
    type: ['0', '7'],
  },
  {
    key_: 'system.boottime',
    description: '系统启动时间，返回时间戳。',
    type: ['0', '7'],
  },
  {
    key_: 'system.cpu.discovery',
    description: '检测到的CPU/CPUs的内核列表，返回JSON对象。',
    type: ['0', '7'],
  },
  {
    key_: 'system.cpu.intr',
    description: '设备的中断数，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'system.cpu.load[<cpu>,<mode>]',
    description: 'CPU 负载，返回浮点数。',
    type: ['0', '7'],
  },
  {
    key_: 'system.cpu.num[<type>]',
    description: 'CPU 数量，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'system.cpu.switches',
    description: '上下文的数量进行切换。它返回一个整数值。',
    type: ['0', '7'],
  },
  {
    key_: 'system.cpu.util[<cpu>,<type>,<mode>,<logical_or_physical>]',
    description: 'CPU 利用率百分比，返回浮点数。',
    type: ['0', '7'],
  },
  {
    key_: 'system.hostname[<type>,<transform>]',
    description: '系统主机名，返回字符串。',
    type: ['0', '7'],
  },
  {
    key_: 'system.hw.chassis[<info>]',
    description: '机架信息，返回字符串。',
    type: ['0', '7'],
  },
  {
    key_: 'system.hw.cpu[<cpu>,<info>]',
    description: 'CPU 信息，返回字符串或整数。',
    type: ['0', '7'],
  },
  {
    key_: 'system.hw.devices[<type>]',
    description: 'PCI或者USB设备列表，返回文本。',
    type: ['0', '7'],
  },
  {
    key_: 'system.hw.macaddr[<interface>,<format>]',
    description: 'MAC地址，返回字符串。',
    type: ['0', '7'],
  },
  {
    key_: 'system.localtime[<type>]',
    description: '系统时间，返回整型 - utc；返回字符串 - local；',
    type: ['0', '7'],
  },
  {
    key_: 'system.run[command,<mode>]',
    description:
      '即在主机上指定的命令的执行。返回命令执行结果的文本值。如果指定NOWAIT的模式，这将返回执行命令的结果1。',
    type: ['0'],
  },
  {
    key_: 'system.stat[resource,<type>]',
    description: '系统统计数据。返回整数值或者浮点值。',
    type: ['0', '7'],
  },
  {
    key_: 'system.sw.arch',
    description: '软件架构信息，返回字符串。',
    type: ['0', '7'],
  },
  {
    key_: 'system.sw.os[<info>]',
    description: '操作系统信息，返回字符串。',
    type: ['0', '7'],
  },
  {
    key_: 'system.sw.packages[<package>,<manager>,<format>]',
    description: '安装包列表，返回文本。',
    type: ['0', '7'],
  },
  {
    key_: 'system.swap.in[<device>,<type>]',
    description: '在交换分区(swap)（从设备到内存）统计数据，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'system.swap.out[<device>,<type>]',
    description: '交换分区（从内存到设备）的统计数据，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'system.swap.size[<device>,<type>]',
    description:
      '交换区空间大小，字节数或总量的百分比。返回整型 - 字节数的类型；浮点型 - 百分比的类型；',
    type: ['0', '7'],
  },
  {
    key_: 'system.uname',
    description: '识别系统，返回字符串。',
    type: ['0', '7'],
  },
  {
    key_: 'system.uptime',
    description: '系统启动时间，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'system.users.num',
    description: '已登录的用户数量，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.dev.discovery',
    description: '块设备和类型的列表，返回JSON类型。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.dev.read[<device>,<type>,<mode>]',
    description:
      '磁盘读取数据。类型是sectors, operations, bytes;返回整数，类型是 sps, ops, bps则返回浮点。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.dev.write[<device>,<type>,<mode>]',
    description:
      '磁盘写入数据。类型是sectors, operations, bytes;返回整数，类型是 sps, ops, bps则返回浮点。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.dir.count[dir,<regex_incl>,<regex_excl>,<types_incl>,<types_excl>,<max_depth>,<min_size>,<max_size>,<min_age>,<max_age>,<regex_excl_dir>]',
    description: '目录递归的条目数量，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.dir.get[dir,<regex_incl>,<regex_excl>,<types_incl>,<types_excl>,<max_depth>,<min_size>,<max_size>,<min_age>,<max_age>,<regex_excl_dir>]',
    description: '目录项列表，。返回JSON对象。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.dir.size[dir,<regex_incl>,<regex_excl>,<mode>,<max_depth>,<regex_excl_dir>]',
    description: '目录大小（bytes），返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.file.cksum[file,<mode>]',
    description:
      '文件校验和，由UNIX校验和算法计算。对于crc32(默认)返回整数，对于md5, sha256返回字符串',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.file.contents[file,<encoding>]',
    description: '搜索文件内容，返回文本。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.file.exists[file,<types_incl>,<types_excl>]',
    description: '检查文件是否存在，返回0 - 未找到；1 - 指定类型的文件存在；',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.file.get[file]',
    description: '返回有关文件的信息，返回JSON对象。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.file.md5sum[file]',
    description: '文件的MD5校验，返回字符串（该文件的MD5哈希值）。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.file.owner[file,<ownertype>,<resulttype>]',
    description: '检索文件的所有者，返回字符串。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.file.permissions[file]',
    description: '返回一个 4 位字符串，其中包含具有 Unix 权限的八进制数。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.file.regexp[file,regexp,<encoding>,<start line>,<end line>,<output>]',
    description: '查找文件中的字符串，返回内容是被匹配内容的整行字符串，或者其他可选参数。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.file.regmatch[file,regexp,<encoding>,<start line>,<end line>]',
    description: '查找文件中的字符串，如果有则返回1，没有则返回0。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.file.size[file,<mode>]',
    description: '文件大小，以字节(默认)或换行为单位，返回整数。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.file.time[file,<mode>]',
    description: '文件事件信息，返回的是时间戳整数。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.fs.discovery',
    description: '挂载的文件系统及其类型列表，返回JSON。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.fs.get',
    description: '挂载的文件系统列表、它们的类型、磁盘空间和inode统计信息，返回JSON。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.fs.inode[fs,<mode>]',
    description: '数或inode的百分比，返回数字，如果是浮点则是百分比。',
    type: ['0', '7'],
  },
  {
    key_: 'vfs.fs.size[fs,<mode>]',
    description: '磁盘容量，如果返回的是字节则是整数，如果返回的是百分比则是浮点。',
    type: ['0', '7'],
  },
  {
    key_: 'vm.memory.size[<mode>]',
    description: '从字节或总百分比的内存大小。它返回一个整数值，如果字节，只要百分比返回浮点值。',
    type: ['0', '7'],
  },
  {
    key_: 'vm.vmemory.size[<type>]',
    description: '虚拟空间大小（以字节计）或百分比（总计）。 返回整型字节; 浮点百分比',
    type: ['0', '7'],
  },
  {
    key_: 'web.page.get[host,<path>,<port>]',
    description: '获取网页，返回信息为网页源码或者TXT。',
    type: ['0', '7'],
  },
  {
    key_: 'web.page.perf[host,<path>,<port>]',
    description: '全网页加载时间（秒），返回浮点型。',
    type: ['0', '7'],
  },
  {
    key_: 'web.page.regexp[host,<path>,<port>,regexp,<length>,<output>]',
    description: '查找网页中的字符串，返回内容是被匹配内容的整行字符串，或者其他可选参数。',
    type: ['0', '7'],
  },
  {
    key_: 'wmi.get[<namespace>,<query>]',
    description: '执行 WMI 查询返回第一个对象。返回整形、浮点、字符串或者文本内容。',
    type: ['0', '7'],
  },
  {
    key_: 'wmi.getall[<namespace>,<query>]',
    description: '执行WMI查询并返回带有所有选定对象的JSON文档。',
    type: ['0'],
  },
  {
    key_: 'zabbix.stats[<ip>,<port>,queue,<from>,<to>]',
    description: '返回队列中在 Zabbix 服务器或远程代理上延迟的监控项数，返回JSON 对象。',
    type: ['0', '7'],
  },
  {
    key_: 'zabbix.stats[<ip>,<port>]',
    description: '远程返回一组 Zabbix 服务器或代理内部指标。',
    type: ['0', '7'],
  },
  {
    key_: 'zabbix[boottime]',
    description: 'rkmon服务器的启动时间，Unix时间截。',
    type: ['5'],
  },
  {
    key_: 'zabbix[history]',
    description: '数据库中HISTORY表中值的条数。',
    type: ['5'],
  },
  {
    key_: 'zabbix[history_log]',
    description: '数据库中HISTORY_LOG表中值的条数.',
    type: ['5'],
  },
  {
    key_: 'zabbix[history_str]',
    description: '数据库中 HISTORY_STR表中值的条数。',
    type: ['5'],
  },
  {
    key_: 'zabbix[history_text]',
    description: '数据库中 HISTORY_TEXT表中值的条数',
    type: ['5'],
  },
  {
    key_: 'zabbix[history_uint]',
    description: '数据库中 HISTORY_UINT 表中值的条数。',
    type: ['5'],
  },
  {
    key_: 'zabbix[host,,items]',
    description: '主机启用监控项的数量',
    type: ['5'],
  },
  {
    key_: 'zabbix[host,,items_unsupported]',
    description: '主机中不迟滞的监控项数量。',
    type: ['5'],
  },
  {
    key_: 'zabbix[host,,maintenance]',
    description: '返回主机当前的维护状态。',
    type: ['5'],
  },
  {
    key_: 'zabbix[host,<type>,available]',
    description:
      '返回主机的普通类型检查的可用性。这个项目的值反映到主机列表的可用性图标上。有效的类型有:agent,snmp,ipmi,jmx',
    type: ['5'],
  },
  {
    key_: 'zabbix[host,discovery,interfaces]',
    description:
      '	Returns a JSON array describing the host network interfaces configured in rkmon. Can be used for LLD.',
    type: ['5'],
  },
  {
    key_: 'zabbix[hosts]',
    description: '已监控主机的数量',
    type: ['5'],
  },
  {
    key_: 'zabbix[items]',
    description: 'rkmon 数据库中的监控项数量',
    type: ['5'],
  },
  {
    key_: 'zabbix[items_unsupported]',
    description: 'rkmon 数据库中不支持的监控项数量。',
    type: ['5'],
  },
  {
    key_: 'zabbix[java,,<param>]',
    description: '返回与rkmon Java应用网关相关联的信息。可用的参数有：ping,version.',
    type: ['5'],
  },
  {
    key_: 'zabbix[lld_queue]',
    description: 'Count of values enqueued in the low-level discovery processing queue.',
    type: ['5'],
  },
  {
    key_: 'zabbix[preprocessing_queue]',
    description: 'Count of values enqueued in the preprocessing queue.',
    type: ['5'],
  },
  {
    key_: 'zabbix[process,<type>,<mode>,<state>]',
    description:
      '乘以一个特定的 rkmon 进程或一组进程（由 <type> 和 <mode> 标识）花费在 <state> 中的百分比。',
    type: ['5'],
  },
  {
    key_: 'zabbix[proxy,<name>,<param>]',
    description:
      'Time of proxy last access. Name - proxy name. Valid params are: lastaccess - Unix timestamp, delay - seconds.',
    type: ['5'],
  },
  {
    key_: 'zabbix[proxy_history]',
    description: '系统代理程序历史数据中未发送给服务端的监控项数量',
    type: ['5'],
  },
  {
    key_: 'zabbix[queue,<from>,<to>]',
    description: 'Number of items in the queue which are delayed by from to seconds, inclusive。',
    type: ['5'],
  },
  {
    key_: 'zabbix[rcache,<cache>,<mode>]',
    description: '配置缓存统计.缓存—缓冲(模式：空闲的百分数，总计，已使用，未使用',
    type: ['5'],
  },
  {
    key_: 'zabbix[requiredperformance]',
    description: 'rkmon服务器所需求的性能,每秒钟处理的新值数.',
    type: ['5'],
  },
  {
    key_: 'zabbix[stats,<ip>,<port>,queue,<from>,<to>]',
    description:
      'Number of items in the queue which are delayed in rkmon server or proxy by "from" till "to" seconds, inclusive.',
    type: ['5'],
  },
  {
    key_: 'zabbix[stats,<ip>,<port>]',
    description: 'Returns a JSON object containing rkmon server or proxy internal metrics.',
    type: ['5'],
  },
  {
    key_: 'zabbix[tcache, cache, <parameter>]',
    description:
      'Trend function cache statistics. Valid parameters are: all, hits, phits, misses, pmisses, items, pitems and requests.',
    type: ['5'],
  },
  {
    key_: 'zabbix[trends]',
    description: '数据库中TRENDS表中值的条数.',
    type: ['5'],
  },
  {
    key_: 'zabbix[trends_uint]',
    description: '数据库中TRENDS_UINT表中值的条数.',
    type: ['5'],
  },
  {
    key_: 'zabbix[triggers]',
    description: 'rkmon 数据库中的触发器数量',
    type: ['5'],
  },
  {
    key_: 'zabbix[uptime]',
    description: 'rkmon服务器进程的在线时间单位秒',
    type: ['5'],
  },
  {
    key_: 'zabbix[vcache,buffer,<mode>]',
    description: '数值缓存统计。合法的mode参数有：total, free, pfree, used and pused.',
    type: ['5'],
  },
  {
    key_: 'zabbix[vcache,cache,<parameter>]',
    description: '数值缓存无效。合法的参数有:requests, hits and misses.',
    type: ['5'],
  },
  {
    key_: 'zabbix[version]',
    description: 'Version of rkmon server or proxy',
    type: ['5'],
  },
  {
    key_: 'zabbix[vmware,buffer,<mode>]',
    description: 'VMware缓存统计.可用的mode有:total, free, pfree, used and pused.',
    type: ['5'],
  },
  {
    key_: 'zabbix[wcache,<cache>,<mode>]',
    description:
      '	Statistics and availability of rkmon write cache. Cache - one of values (modes: all, float, uint, str, log, text, not supported), history (modes: pfree, free, total, used, pused), index (modes: pfree, free, total, used, pused), trend (modes: pfree, free, total, used, pused).',
    type: ['5'],
  },
  {
    key_: 'ipmi.get',
    description: 'IPMI sensor IDs and other sensor-related parameters. Returns JSON.',
    type: ['12'],
  },
  {
    key_: 'snmptrap.fallback',
    description: '采集所有SNMP traps规则以外的snmptrap[]',
    type: ['17'],
  },
  {
    key_: 'snmptrap[<regex>]',
    description: '	采集所有符合规则的 SNMP 信息，如果没有规则则会采集所有信息。',
    type: ['17'],
  },
  {
    key_: 'db.odbc.discovery[<unique short description>,<dsn>,<connection string>]	',
    description: 'Transform SQL query result into a JSON array for low-level discovery.',
    type: ['11'],
  },
  {
    key_: 'db.odbc.get[<unique short description>,<dsn>,<connection string>]',
    description: 'Transform SQL query result into a JSON array.',
    type: ['11'],
  },
  {
    key_: 'db.odbc.select[<unique short description>,<dsn>,<connection string>]',
    description: '返回SQL查询结果的第一行的第一列中。',
    type: ['11'],
  },
  {
    key_: 'jmx.discovery[<discovery mode>,<object name>,<unique short description>]',
    description:
      'Return a JSON array with LLD macros describing the MBean objects or their attributes. Can be used for LLD.',
    type: ['16'],
  },
  {
    key_: 'jmx.get[<discovery mode>,<object name>,<unique short description>]',
    description:
      'Return a JSON array with MBean objects or their attributes. Compared to jmx.discovery it does not define LLD macros. Can be used for LLD.',
    type: ['16'],
  },
  {
    key_: 'jmx[object_name,attribute_name,<unique short description>]',
    description: '返回值为MBean对象的属性。',
    type: ['16'],
  },
];
