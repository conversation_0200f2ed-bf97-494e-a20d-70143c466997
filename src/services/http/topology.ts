// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增 POST /api/v1/rump/topology */
export async function createTopology(
  body: API.TopologyCURequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/topology', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除 DELETE /api/v1/rump/topology */
export async function deleteTopology(body: API.BatchIdRequest, options?: { [key: string]: any }) {
  return request<any>('/api/v1/rump/topology', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 详情 GET /api/v1/rump/topology/${param0} */
export async function getTopology(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getTopologyParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultTopologyVO>(`/api/v1/rump/topology/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改 PUT /api/v1/rump/topology/${param0} */
export async function updateTopology(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateTopologyParams,
  body: API.TopologyCURequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/api/v1/rump/topology/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/v1/rump/topology/default */
export async function getDefaultTopology(options?: { [key: string]: any }) {
  return request<API.ResultTopologyVO>('/api/v1/rump/topology/default', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/v1/rump/topology/default/save */
export async function saveDefaultTopology(
  body: API.TopologyCURequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/topology/default/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页 POST /api/v1/rump/topology/page */
export async function pageTopology(
  body: API.TopologyPageRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultPageVOTopologyPageVO>('/api/v1/rump/topology/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
