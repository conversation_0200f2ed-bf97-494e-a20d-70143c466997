import { MONITOR_TYPE } from '@/enums';
import { defaultTableConfig } from '@/utils/setting';
import {
  DrawerForm,
  ModalFormProps,
  ProColumns,
  ProFormDependency,
  ProFormSelect,
  ProTable,
} from '@ant-design/pro-components';
import React from 'react';
import { agent } from './agent';

const TYPE_OPTIONS = MONITOR_TYPE.filter((item) =>
  ['0', '7', '3', '5', '12', '17', '11', '16'].includes(item.value),
);

type ItemProps = {
  key_: string;
  description?: string;
};

const ItemKeysSetter: React.FC<
  ModalFormProps & {
    onRowClick?: (row: ItemProps) => void;
  }
> = ({ open, onOpenChange, onRowClick }) => {
  // 表格
  const columns: ProColumns<ItemProps>[] = [
    {
      title: '键值',
      dataIndex: 'key_',
      width: 200,
      render(dom, entity) {
        return <a onClick={() => onRowClick?.(entity)}>{dom}</a>;
      },
    },
    {
      title: '注释',
      width: 200,
      dataIndex: 'description',
    },
  ];
  return (
    <DrawerForm
      width={800}
      title="标准检测器"
      open={open}
      onOpenChange={onOpenChange}
      autoFocusFirstInput
      submitter={false}
      initialValues={{
        type: '0',
      }}
    >
      <ProFormSelect width="md" name="type" label="类型" options={TYPE_OPTIONS} />
      <ProFormDependency name={['type']}>
        {({ type }) => {
          return (
            <ProTable<ItemProps>
              {...defaultTableConfig}
              rowKey="key_"
              options={false}
              dataSource={agent.filter((item) => item.type.includes(type))}
              className="inner-table"
              search={false}
              columns={columns}
            />
          );
        }}
      </ProFormDependency>
    </DrawerForm>
  );
};

export default ItemKeysSetter;
