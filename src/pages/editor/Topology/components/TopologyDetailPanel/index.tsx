import { STATUS } from '@/enums';
import DataCard from '@/pages/AnalysisPage/components/DataCard';
import DataTable from '@/pages/AnalysisPage/components/DataTable';
import { option2enum, passParamsToPage } from '@/utils';
import { DrawerForm, DrawerFormProps, ProDescriptions } from '@ant-design/pro-components';
import { FC } from 'react';
import styles from './index.less';

const TopologyDetailPanel: FC<DrawerFormProps> = ({ initialValues, ...props }) => {
  return (
    <DrawerForm width={800} submitter={false} {...props}>
      <div className={styles.panel}>
        <ProDescriptions title="监控对象信息" column={1} size="small">
          <ProDescriptions.Item label="名称">
            <a
              onClick={() => {
                passParamsToPage('/monitor/list', {
                  hostids: initialValues?.hostid,
                });
              }}
            >
              {initialValues?.name}
            </a>
          </ProDescriptions.Item>
          <ProDescriptions.Item label="状态" valueEnum={option2enum(STATUS)}>
            {initialValues?.status}
          </ProDescriptions.Item>
          {initialValues?.problem && (
            <ProDescriptions.Item label="告警事件详情" valueEnum={option2enum(STATUS)}>
              <a
                onClick={() => {
                  passParamsToPage('/alarm/event', {
                    hostids: initialValues?.hostid,
                  });
                }}
              >
                去处理
              </a>
            </ProDescriptions.Item>
          )}
        </ProDescriptions>
        {/* 告警列表 */}
        <DataTable data={{ hostIds: [initialValues?.hostid] }} hideHostColumns />
        {/* 使用率 */}
        <DataCard
          data={{ hostIds: [initialValues?.hostid], id: initialValues?.hostid }}
          span={24}
          type="list"
        />
      </div>
    </DrawerForm>
  );
};

export default TopologyDetailPanel;
