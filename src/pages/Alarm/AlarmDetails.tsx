import { SEVERITIES } from '@/enums';
import { alarmFindById } from '@/services/http/alarm';
import { formatSecondsToString } from '@/utils';
import {
  FooterToolbar,
  PageContainer,
  ProCard,
  ProColumns,
  ProDescriptions,
  ProForm,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { history, useParams, useRequest } from '@umijs/max';
import { Badge, Button, DatePicker, Space, Tabs, Tag, Tooltip } from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';

const { RangePicker } = DatePicker;

const AlarmDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const formRef = useRef<ProFormInstance>();
  const [activeTab, setActiveTab] = useState('1');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

  // 获取告警详情数据
  const { data: alarmDetailResponse, loading } = useRequest(
    () => {
      if (!id) {
        return Promise.reject(new Error('告警ID不能为空'));
      }
      return alarmFindById({ id });
    },
    {
      refreshDeps: [id],
      formatResult: (res) => res,
    },
  );

  // 从接口数据中提取告警信息
  const alarmInfo: API.AlarmVO | undefined = (alarmDetailResponse as API.ResultAlarmVO)?.data;

  // 计算告警时长
  const calculateDuration = () => {
    if (!alarmInfo?.time) return '-';
    const startTime = dayjs(alarmInfo.time);
    const endTime = alarmInfo.recoverTime ? dayjs(alarmInfo.recoverTime) : dayjs();
    const diffSeconds = endTime.diff(startTime, 'second');
    return formatSecondsToString(diffSeconds);
  };

  // 处理后的告警数据
  const alarmData = {
    content: alarmInfo?.message || '-',
    level: alarmInfo?.level || '1',
    tags: [] as Array<{ tag: string; value: string }>, // 暂时为空，如果后续有标签数据可以在这里处理
    alarmTime: alarmInfo?.time ? dayjs(alarmInfo.time).format('YYYY-MM-DD HH:mm:ss') : '-',
    alarmObject: alarmInfo?.objects?.[0]?.objectName || '-',
    alarmObjectId: alarmInfo?.objects?.[0]?.objectId || '',
    duration: calculateDuration(),
    confirmStatus: alarmInfo?.confirm ? '已确认' : '未确认',
    recoveryTime: alarmInfo?.recoverTime
      ? dayjs(alarmInfo.recoverTime).format('YYYY-MM-DD HH:mm:ss')
      : '-',
  };

  // 指标数据 - 暂时使用静态数据，后续需要通过监控对象接口获取相关指标信息
  const metricsData = [
    {
      id: '1',
      name: '磁盘使用率',
      itemId: 'item123',
      lastMonitorTime: '2024-01-15 16:45:30',
      monitorData: '92.5%',
      dataChange: '+2.3%',
    },
    {
      id: '2',
      name: 'CPU使用率',
      itemId: 'item124',
      lastMonitorTime: '2024-01-15 16:45:28',
      monitorData: '75.2%',
      dataChange: '+5.1%',
    },
  ];

  // 告警动作记录数据 - 基于接口数据
  const actionRecordsData =
    alarmInfo?.actionHistory?.map((action: API.AlarmActionVO, index: number) => ({
      id: action.id || `action-${index}`,
      confirmTime: action.actionTime ? dayjs(action.actionTime).format('YYYY-MM-DD HH:mm:ss') : '-',
      confirmUser: action.operator || '-',
      confirmMessage: action.actionMessage || '-',
      confirmAction:
        action.actionType === 'Confirm'
          ? '确认问题'
          : action.actionType === 'Level_Upgrade'
          ? '等级升级'
          : action.actionType === 'Level_Downgrade'
          ? '等级降级'
          : action.actionType === 'Close'
          ? '关闭告警'
          : action.actionType || '-',
    })) || [];

  // 告警通知数据 - 基于接口数据
  const notificationData =
    alarmInfo?.notificationHistory?.map((notification: API.AlarmNotificationVO, index: number) => ({
      id: notification.id || `notification-${index}`,
      time: notification.sendTime
        ? dayjs(notification.sendTime).format('YYYY-MM-DD HH:mm:ss')
        : '-',
      type:
        notification.channel === 'Email'
          ? '邮件'
          : notification.channel === 'Sms'
          ? '短信'
          : notification.channel || '-',
      sendMethod:
        notification.channel === 'Email' ? 'SMTP' : notification.channel === 'Sms' ? 'SMS' : '-',
      receiver: notification.recipient || '-',
      sendResult: notification.success
        ? '发送成功'
        : `发送失败${notification.errorMessage ? `: ${notification.errorMessage}` : ''}`,
      triggerRule:
        notification.triggerAction === 'Raised'
          ? '告警触发'
          : notification.triggerAction === 'Recovered'
          ? '告警恢复'
          : notification.triggerAction === 'Confirm'
          ? '告警确认'
          : notification.triggerAction === 'Closed'
          ? '告警关闭'
          : notification.triggerAction === 'Level_Upgrade'
          ? '等级升级'
          : notification.triggerAction === 'Level_Downgrade'
          ? '等级降级'
          : notification.triggerAction === 'Updated'
          ? '告警更新'
          : notification.triggerAction || '-',
    })) || [];

  // 告警历史数据 - 基于当前告警信息
  const historyData = alarmInfo
    ? [
        {
          id: alarmInfo.id || '1',
          alarmTime: alarmData.alarmTime,
          level: alarmData.level,
          eventId: alarmInfo.id || '-',
          alarmName: alarmData.content,
          duration: alarmData.duration,
          confirmed: alarmData.confirmStatus,
          recoveryStatus: alarmInfo.recovered ? '已恢复' : '未恢复',
          recoveryTime: alarmData.recoveryTime,
          tags: alarmData.tags,
        },
      ]
    : [];

  const metricsColumns: ProColumns[] = [
    {
      title: '指标名称',
      dataIndex: 'name',
      render: (text, record) => (
        <a
          onClick={() => {
            history.push(
              `/monitor-config/host/monitor-item/${alarmData.alarmObjectId}/view/${record.itemId}`,
            );
          }}
        >
          {text}
        </a>
      ),
    },
    {
      title: '最新监测时间',
      dataIndex: 'lastMonitorTime',
    },
    {
      title: '监控数据',
      dataIndex: 'monitorData',
    },
    {
      title: '数据变化量',
      dataIndex: 'dataChange',
      render: (text: any) => (
        <span style={{ color: String(text)?.startsWith('+') ? '#ff4d4f' : '#52c41a' }}>{text}</span>
      ),
    },
  ];

  const actionRecordsColumns: ProColumns[] = [
    {
      title: '确认时间',
      dataIndex: 'confirmTime',
    },
    {
      title: '确认人',
      dataIndex: 'confirmUser',
    },
    {
      title: '确认信息',
      dataIndex: 'confirmMessage',
    },
    {
      title: '确认动作',
      dataIndex: 'confirmAction',
    },
  ];

  const notificationColumns: ProColumns[] = [
    {
      title: '时间',
      dataIndex: 'time',
    },
    {
      title: '类型',
      dataIndex: 'type',
    },
    {
      title: '发送方式',
      dataIndex: 'sendMethod',
    },
    {
      title: '接收人',
      dataIndex: 'receiver',
    },
    {
      title: '发送结果',
      dataIndex: 'sendResult',
      render: (text) => <Badge status={text === '发送成功' ? 'success' : 'error'} text={text} />,
    },
    {
      title: '触发规则',
      dataIndex: 'triggerRule',
    },
  ];

  const historyColumns: ProColumns[] = [
    {
      title: '告警时间',
      dataIndex: 'alarmTime',
    },
    {
      title: '告警等级',
      dataIndex: 'level',
      render: (text) => {
        const severityObj = SEVERITIES.find((item) => item.value === text);
        return severityObj ? (
          <Tooltip title={severityObj.label}>
            <Tag color={severityObj.tagColor}>{severityObj.label}</Tag>
          </Tooltip>
        ) : (
          text
        );
      },
    },
    {
      title: 'ID',
      dataIndex: 'eventId',
    },
    {
      title: '告警名称',
      dataIndex: 'alarmName',
    },
    {
      title: '告警时长',
      dataIndex: 'duration',
    },
    {
      title: '确认问题',
      dataIndex: 'confirmed',
    },
    {
      title: '恢复状态',
      dataIndex: 'recoveryStatus',
      render: (text) => (
        <span>
          <Badge text={text} status={text === '已恢复' ? 'success' : 'error'} />
        </span>
      ),
    },
    {
      title: '恢复时间',
      dataIndex: 'recoveryTime',
    },
    {
      title: '标签',
      dataIndex: 'tags',
      render: (tags: any) => (
        <>
          {Array.isArray(tags) &&
            tags?.map((item: any, index: number) => (
              <Tag key={index}>
                {item.tag}: {item.value}
              </Tag>
            ))}
        </>
      ),
    },
    {
      title: '操作',
      width: 70,
      key: 'action',
      render: () => (
        <Button type="link" size="small">
          确认
        </Button>
      ),
    },
  ];

  const severityObj = SEVERITIES.find((item) => item.value === alarmData.level);

  return (
    <PageContainer header={{ title: false }}>
      <ProForm
        formRef={formRef}
        submitter={{
          searchConfig: {
            submitText: '确认告警',
            resetText: '关闭告警',
          },
          onReset: () => {
            // 关闭告警逻辑
            console.log('关闭告警');
          },
          render: (_, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
        }}
        onFinish={async () => {
          // 确认告警逻辑
          console.log('确认告警');
          return true;
        }}
      >
        <ProCard title="告警信息" style={{ borderRadius: 0 }} loading={loading}>
          <ProDescriptions column={3}>
            <ProDescriptions.Item label="告警内容">{alarmData.content}</ProDescriptions.Item>
            <ProDescriptions.Item label="告警等级">
              {severityObj ? (
                <div style={{ color: severityObj.tagColor }}>{severityObj.label}</div>
              ) : (
                alarmData.level
              )}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="标签">
              {alarmData.tags.map((item, index) => (
                <Tag key={index}>
                  {item.tag}: {item.value}
                </Tag>
              ))}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="告警时间">{alarmData.alarmTime}</ProDescriptions.Item>
            <ProDescriptions.Item label="告警对象">
              <a
                onClick={() => {
                  history.push(`/monitor-config/host/details/${alarmData.alarmObjectId}`);
                }}
              >
                {alarmData.alarmObject}
              </a>
            </ProDescriptions.Item>
            <ProDescriptions.Item label="告警时长">{alarmData.duration}</ProDescriptions.Item>
            <ProDescriptions.Item label="确认问题状态">
              {alarmData.confirmStatus}
            </ProDescriptions.Item>
            <ProDescriptions.Item label="恢复时间">{alarmData.recoveryTime}</ProDescriptions.Item>
          </ProDescriptions>
        </ProCard>

        <ProCard title="监控指标" style={{ borderRadius: 0 }} loading={loading}>
          <ProTable
            className="inner-table"
            columns={metricsColumns}
            dataSource={metricsData}
            rowKey="id"
            search={false}
            pagination={false}
            options={false}
            size="small"
          />
        </ProCard>

        <ProCard style={{ borderRadius: 0 }} loading={loading}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={[
              {
                key: '1',
                label: '告警动作记录',
                children: (
                  <ProTable
                    className="inner-table"
                    columns={actionRecordsColumns}
                    dataSource={actionRecordsData}
                    rowKey="id"
                    search={false}
                    pagination={false}
                    options={false}
                    size="small"
                  />
                ),
              },
              {
                key: '2',
                label: '告警通知',
                children: (
                  <ProTable
                    className="inner-table"
                    columns={notificationColumns}
                    dataSource={notificationData}
                    rowKey="id"
                    search={false}
                    pagination={false}
                    options={false}
                    size="small"
                  />
                ),
              },
              {
                key: '3',
                label: '告警历史',
                children: (
                  <div>
                    <div style={{ marginBottom: 16 }}>
                      <Space>
                        <span>时间范围：</span>
                        <RangePicker
                          showTime
                          value={dateRange}
                          onChange={(dates) =>
                            setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null)
                          }
                          format="YYYY-MM-DD HH:mm:ss"
                        />
                      </Space>
                    </div>
                    <ProTable
                      className="inner-table"
                      columns={historyColumns}
                      dataSource={historyData}
                      rowKey="id"
                      search={false}
                      pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                      }}
                      options={false}
                      size="small"
                    />
                  </div>
                ),
              },
              {
                key: '4',
                label: '处理建议',
                children: (
                  <div>
                    <div style={{ marginBottom: 24 }}>
                      <h4>AI智能诊断</h4>
                      <p style={{ whiteSpace: 'pre-wrap' }}>
                        {alarmInfo?.suggestion || '暂无处理建议'}
                      </p>
                    </div>
                  </div>
                ),
              },
            ]}
          />
        </ProCard>
      </ProForm>
    </PageContainer>
  );
};

export default AlarmDetails;
