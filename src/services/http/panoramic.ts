// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新建全景组 POST /api/v1/rump/panoramic/group */
export async function createPanoramicGroup(
  body: API.CUPanoramicGroupRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/panoramic/group', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除全景组 DELETE /api/v1/rump/panoramic/group */
export async function deletePanoramicGroup(
  body: API.BatchIdRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/panoramic/group', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新全景组 PUT /api/v1/rump/panoramic/group/${param0} */
export async function updatePanoramicGroup(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updatePanoramicGroupParams,
  body: API.CUPanoramicGroupRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/api/v1/rump/panoramic/group/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 全景组分页 POST /api/v1/rump/panoramic/group/page */
export async function panoramicGroupPage(
  body: API.PanoramicGroupPageRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultPageVOPanoramicGroupPageVO>('/api/v1/rump/panoramic/group/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 全景组分页携带问题信息 POST /api/v1/rump/panoramic/group/problem/page */
export async function panoramicGroupProblemPage(
  body: API.PanoramicGroupPageRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultPageVOPanoramicGroupProblemPageVO>(
    '/api/v1/rump/panoramic/group/problem/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}
