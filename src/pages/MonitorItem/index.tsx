import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { SEVERITIES, STATUS } from '@/enums';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import { zabbix, zabbixDelete, zabbixPost } from '@/services/zabbix';
import { getTagData, option2enum, passParamsToPage, queryPagingTable, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProConfigProvider,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { Link, useParams, useRequest } from '@umijs/max';
import { message, Modal, Space, Switch, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import { valueTypeMap } from '../HostMonitor/valueTypeMap';

const MonitorItem: React.FC = withStorageToUrl(({ queryParams }) => {
  const { hostId } = useParams();
  const tableRef = useRef<ActionType | undefined>();
  const formRef = useRef<ProFormInstance>();
  const [selectedRows, setSelectedRows] = useState<RK_API.Item[]>([]);

  // 获取主机
  const { data: host } = useRequest(
    () =>
      zabbix({
        output: 'extend',
        sortfield: ['name'],
        method: 'host.get',
        hostids: [hostId],
        preservekeys: true,
      }),
    {
      ready: !!hostId,
    },
  );

  // 返回所有 监控项的标签
  const { data: hostTags } = useRequest(
    () => zabbix({ method: 'item.get', output: ['tags'], selectTags: 'extend', hostids: [hostId] }),
    {
      formatResult: (res) => {
        if (res.code === 200) {
          return getTagData(res.data);
        }
      },
    },
  );

  // 删除
  const { run: deleteRecord } = useRequest((ids) => zabbixDelete({ ids, method: 'item.delete' }), {
    manual: true,
    onSuccess: (res) => {
      if (!res.data) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const handleDelete = async (rows: RK_API.Item[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.itemid!);
      names.push(item.name!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除监控项“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 更新状态
  const { run: updateStatus, fetches: updateStatusFetches } = useRequest(
    (params) =>
      zabbixPost({
        ...params,
        method: 'item.update',
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code !== 200) return;
        message.success('操作成功');
        tableRef.current?.reloadAndRest?.();
      },
      formatResult: (res) => res,
      fetchKey: (params) => params.itemid,
    },
  );

  // 表格
  const columns: ProColumns<RK_API.Item>[] = [
    {
      title: '指标名称',
      width: 200,
      dataIndex: 'name',
      render(dom, entity) {
        return (
          <Link to={`/monitor-config/host/monitor-item/${hostId}/view/${entity?.itemid}`}>
            {dom}
          </Link>
        );
      },
      initialValue: queryParams.get('name'),
    },
    // { ...hostGroups, initialValue: queryParams.getAll('groupids') },
    // {
    //   ...template,
    //   initialValue: queryParams.getAll('templateids'),
    //   hideInSearch: !formTemplate,
    // },
    // {
    //   ...host,
    //   initialValue: queryParams.getAll('hostids'),
    //   hideInSearch: formTemplate,
    // },

    {
      title: '键值',
      width: 100,
      dataIndex: 'key_',
      hideInSearch: true,
    },
    {
      title: '单位',
      width: 100,
      dataIndex: 'units',
      hideInSearch: true,
    },
    {
      title: '更新间隔',
      width: 80,
      dataIndex: 'delay',
      hideInSearch: true,
    },

    {
      title: '历史记录',
      width: 80,
      dataIndex: 'history',
      hideInSearch: true,
    },
    {
      title: '趋势',
      width: 80,
      dataIndex: 'trends',
      hideInSearch: true,
    },
    // {
    //   title: '类型',
    //   dataIndex: 'type',
    //   valueType: 'select',
    //   width: 120,
    //   valueEnum: option2enum(MONITOR_TYPE),
    //   initialValue: queryParams.get('type'),
    // },
    // {
    //   title: '信息类型',
    //   dataIndex: 'value_type',
    //   valueType: 'select',
    //   valueEnum: option2enum(MONITOR_DATA_TYPE),
    //   hideInTable: true,
    //   initialValue: queryParams.get('value_type'),
    // },
    {
      title: '标签',
      dataIndex: 'rk_tags',
      width: 200,

      // @ts-ignore
      valueType: 'tagsSelectSetter',
      fieldProps: {
        tags: hostTags,
      },
      render(_, entity) {
        const { tags = [] } = entity;
        if (!tags.length) return <>-</>;
        return (tags as RK_API.TemplateTag[]).map((item, index) => (
          <Tag key={index}>
            {item?.tag}: {item?.value}
          </Tag>
        ));
      },
    },
    {
      title: '状态',
      width: 100,
      dataIndex: 'status',
      valueEnum: option2enum(STATUS),
      hideInTable: true,
      initialValue: queryParams.get('status'),
    },

    {
      title: '阈值规则',
      dataIndex: 'triggers',
      hideInSearch: true,
      width: 200,
      render(dom, entity) {
        const { triggers = [] as RK_API.Trigger[] } = entity;

        return triggers.map((trigger, index) => {
          const severityObj = SEVERITIES.find((item) => trigger.priority === item.value);

          return (
            <div key={index}>
              <Tag color={severityObj?.tagColor}>{severityObj?.label}</Tag>
              {trigger?.description}
            </div>
          );
        });
      },
    },
    {
      title: '状态',
      width: 100,
      dataIndex: 'status',
      ellipsis: true,
      hideInSearch: true,
      render(_, entity) {
        const checked = entity.status === '0';

        return (
          <Switch
            defaultChecked={checked}
            loading={updateStatusFetches?.[entity.itemid!]?.loading}
            onChange={() => {
              updateStatus({
                itemid: entity.itemid,
                status: checked ? 1 : 0,
              });
            }}
          />
        );
      },
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (text, record) => {
        return (
          <Space>
            <a
              onClick={() =>
                passParamsToPage(
                  `/monitor-config/host/monitor-item/${hostId}/setting/${record.itemid}`,
                  {
                    item: record.itemid,
                  },
                )
              }
            >
              阈值设置
            </a>
            <a key="del" onClick={() => handleDelete([record])}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProConfigProvider valueTypeMap={valueTypeMap}>
        <ProTable<RK_API.Item>
          {...defaultTableConfig}
          search={SearchOptionRender}
          onSubmit={syncToUrl}
          rowSelection={{
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRows(selectedRows);
            },
          }}
          rowKey="itemid"
          formRef={formRef}
          actionRef={tableRef}
          columns={columns}
          headerTitle={host?.[hostId!] && `${host?.[hostId!]?.name}指标配置`}
          request={async (params) => {
            const { evaltype, name, templateids = [], groupids = [], rk_tags, ...rest } = params;

            return queryPagingTable(
              {
                evaltype,
                tags: rk_tags,
                templateids: templateids.length ? templateids : null,
                groupids: groupids.length ? groupids : null,
                hostids: [hostId],
                sortfield: 'name',
                search: {
                  name,
                },
                filter: rest,
                selectTriggers: 'extend',
                selectTags: 'extend',
                method: 'item.get',
              },
              zabbix,
            );
          }}
        />
      </ProConfigProvider>
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
});

export default MonitorItem;
