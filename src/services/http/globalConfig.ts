// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 此处后端没有提供注释 GET /api/v1/rump/global/config/all */
export async function findGlobalConfig(options?: { [key: string]: any }) {
  return request<API.ResultGlobalConfigVO>('/api/v1/rump/global/config/all', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /api/v1/rump/global/config/update */
export async function updateGlobalConfig(
  body: API.GlobalConfigRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/global/config/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
