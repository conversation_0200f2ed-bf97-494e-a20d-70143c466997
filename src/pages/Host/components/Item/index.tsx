import { zabbixDelete } from '@/services/zabbix';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { message, Modal, Space, Tooltip, Typography } from 'antd';
import styles from './index.less';
const { Text } = Typography;
const Item = (props: Record<string, any>) => {
  // 删除主机组
  const { run: deleteRecord } = useRequest(
    (id) => zabbixDelete({ ids: [id], method: 'hostgroup.delete' }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code !== 200) return;
        message.success('删除成功');
        props?.onFinish?.();
      },
      formatResult: (res) => res,
    },
  );

  const handleDelete = async (row: RK_API.HostGroup) => {
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除对象群组“${row?.name}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(row?.groupid);
      },
    });
  };

  return (
    <div className={styles.item}>
      <Text ellipsis className={styles.label}>
        {props.name}
      </Text>
      <Space size={10} className={styles.action}>
        <Tooltip title="编辑">
          <EditOutlined
            className={styles.icon}
            onClick={(e) => {
              e.stopPropagation();
              props?.onEdit?.();
            }}
          />
        </Tooltip>
        <Tooltip title="删除">
          <DeleteOutlined
            className={styles.icon}
            onClick={(e) => {
              e.stopPropagation();
              handleDelete(props);
            }}
          />
        </Tooltip>
      </Space>
    </div>
  );
};

export default Item;
