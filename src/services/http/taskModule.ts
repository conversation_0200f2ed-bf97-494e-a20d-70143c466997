// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 此处后端没有提供注释 PUT /api/v1/rump/task/module/${param0}/update */
export async function taskModuleUpdateById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.taskModuleUpdateByIdParams,
  body: API.TaskModuleCURequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/api/v1/rump/task/module/${param0}/update`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/v1/rump/task/module/batch/delete/ */
export async function batchDeleteTaskModule(
  body: API.BatchIdRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/task/module/batch/delete/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/v1/rump/task/module/byId/${param0} */
export async function taskModuleFindById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.taskModuleFindByIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultTaskModuleVO>(`/api/v1/rump/task/module/byId/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/v1/rump/task/module/change/history/${param0} */
export async function taskModuleChangeHistory(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.taskModuleChangeHistoryParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultListTaskModuleChangeHistoryVO>(
    `/api/v1/rump/task/module/change/history/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 POST /api/v1/rump/task/module/create */
export async function taskModuleCreate(
  body: API.TaskModuleCURequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/task/module/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/v1/rump/task/module/page */
export async function taskModulePage(
  body: API.TaskModulePageRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultPageVOTaskModulePageVO>('/api/v1/rump/task/module/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
