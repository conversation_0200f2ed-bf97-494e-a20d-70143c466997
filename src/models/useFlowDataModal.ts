import { Edge, Node } from '@antv/x6';
import { useCallback, useState } from 'react';
const initialData = {
  nodes: [
    {
      id: 'start',
      shape: 'job-node',
      x: 24,
      y: 200,
      width: 60,
      height: 60,
      label: '开始',
      attrs: {
        body: {
          fill: '#b7eb8f',
          stroke: '#73d13d',
        },
      },
      ports: {
        items: [
          {
            id: 'port_start_2',
            group: 'right',
          },
        ],
      },
    },
  ],
  edges: [],
};

export type DataProps = {
  nodes: Node.Metadata[];
  edges: Edge.Metadata[];
};

type CellProps = Array<Node.Metadata | Edge.Metadata>;

export default function useFlowDataModal() {
  const [data, setData] = useState<DataProps>(initialData);

  const restFlowData = useCallback(() => {
    setData({ ...initialData });
  }, []);

  const updateFlowData = useCallback((arr: CellProps) => {
    if (!arr.length) {
      restFlowData();
      return;
    }
    const result: DataProps = {
      edges: [],
      nodes: [],
    };
    for (const item of arr) {
      if (item.shape === 'edge') {
        result.edges.push(item);
      } else {
        result.nodes.push(item);
      }
    }
    setData(result);
    return result;
  }, []);

  return {
    flowData: data,
    updateFlowData,
    restFlowData,
  };
}
