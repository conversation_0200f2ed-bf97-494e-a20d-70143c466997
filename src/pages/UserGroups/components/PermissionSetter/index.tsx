import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { PERMISSION_LEVEL } from '@/enums';
import { zabbix } from '@/services/zabbix';
import {
  ActionType,
  EditableFormInstance,
  EditableProTable,
  ProColumns,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button } from 'antd';
import React, { memo, useEffect, useRef, useState } from 'react';
import AssociatedPermission from '../AssociatedPermission';
import styles from './index.less';

const actionBtns = PERMISSION_LEVEL.map((item) => ({
  key: item.value,
  label: item.label,
}));

const PermissionSetter: React.FC<{
  value?: RK_API.Auth[];
  onChange?: (value: RK_API.Auth[]) => void;
}> = ({ value, onChange }) => {
  const editorFormRef = useRef<EditableFormInstance<RK_API.Auth>>();

  const tableRef = useRef<ActionType | undefined>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>();

  // 对象群组
  const { data: hostGroupList = [], loading } = useRequest(() =>
    zabbix({
      method: 'hostgroup.get',
      sortfield: ['name'],
    }),
  );

  useEffect(() => {
    setEditableRowKeys(value?.map((item) => item.id!));
  }, [value]);
  const [userModalVisit, setUserModalVisit] = useState(false);

  const [selectedRows, setSelectedRows] = useState<RK_API.Auth[]>([]);

  const columns: ProColumns<RK_API.Auth>[] = [
    {
      title: '对象群组名称',
      dataIndex: 'id',
      width: 200,
      valueType: 'select',
      fieldProps: {
        fieldNames: {
          label: 'name',
          value: 'groupid',
        },
        options: hostGroupList,
      },
      editable: false,
    },
    {
      title: '权限',
      width: 300,
      dataIndex: 'permission',
      valueType: 'radioButton',
      fieldProps: () => {
        return {
          options: PERMISSION_LEVEL,
          allowClear: false,
          buttonStyle: 'solid',
        };
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 100,
    },
  ];

  return (
    <>
      <EditableProTable<RK_API.Auth>
        editableFormRef={editorFormRef}
        pagination={{
          defaultPageSize: 10,
        }}
        actionRef={tableRef}
        className={styles.editable}
        headerTitle="权限"
        rowKey="id"
        recordCreatorProps={false}
        loading={false}
        columns={columns}
        value={value}
        toolbar={{
          actions: [
            <Button
              key="primary"
              type="primary"
              onClick={() => {
                setUserModalVisit(true);
              }}
            >
              主机群组
            </Button>,
          ],
        }}
        editable={{
          type: 'multiple',
          editableKeys,
          actionRender: (row) => {
            return [
              <a
                key="delete"
                onClick={() => {
                  onChange?.(value?.filter((item) => item.id !== row.id) || []);
                }}
              >
                删除
              </a>,
            ];
          },
          onChange: setEditableRowKeys,
          onValuesChange: (record, recordList) => {
            onChange?.(recordList);
          },
        }}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {/* 权限 */}
      <AssociatedPermission
        open={userModalVisit}
        value={value}
        onOk={(_, selectList) => {
          setUserModalVisit(false);

          onChange?.(selectList || []);
        }}
        onCancel={() => {
          setUserModalVisit(false);
        }}
        options={hostGroupList.map((item: RK_API.HostGroup) => ({ ...item, id: item.groupid }))}
        loading={loading}
      />
      {/* 多选操作栏 */}
      <OperateFooterToolbar
        selectedRows={selectedRows}
        actions={actionBtns}
        onOperation={(key, selectedRows, selectedKeys) => {
          const updatedValue =
            value?.map((item) => {
              if (selectedKeys.includes(item.id!)) {
                return {
                  ...item,
                  permission: key as number, // 将权限更改为拒绝
                };
              }
              return item;
            }) || [];
          updatedValue.forEach((item) => {
            editorFormRef.current?.setFieldsValue({
              [item.id!]: item,
            });
          });
          tableRef.current?.reloadAndRest?.();
        }}
      />
    </>
  );
};

export default memo(PermissionSetter);
