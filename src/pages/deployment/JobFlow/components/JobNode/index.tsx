import { RUN_MODE } from '@/enums';
import { cancelBubble } from '@/utils';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  PlusCircleOutlined,
  ToolFilled,
  WarningFilled,
} from '@ant-design/icons';
import { Graph, Node, StringExt } from '@antv/x6';
import classNames from 'classnames/bind';
import React, { SyntheticEvent } from 'react';
import styles from './index.less';
const cx = classNames.bind(styles);

// 节点位置信息
interface Position {
  x: number;
  y: number;
}

/**
 * 根据起点初始下游节点的位置信息
 * @param node 起始节点
 * @param graph
 * @returns
 */
const getDownstreamNodePosition = (node: Node, graph: Graph, dy = 100) => {
  const dx = node.size().width + 100;
  // 找出画布中以该起始节点为起点的相关边的终点id集合
  const downstreamNodeIdList: string[] = [];
  graph.getEdges().forEach((edge) => {
    const originEdge = edge.toJSON();
    if (originEdge.source.cell === node.id) {
      downstreamNodeIdList.push(originEdge.target.cell);
    }
  });
  // 获取起点的位置信息
  const position = node.getPosition();
  let minX = Infinity;
  let maxY = -Infinity;
  graph.getNodes().forEach((graphNode) => {
    if (downstreamNodeIdList.indexOf(graphNode.id) > -1) {
      const nodePosition = graphNode.getPosition();
      // 找到所有节点中最左侧的节点的x坐标
      if (nodePosition.x < minX) {
        minX = nodePosition.x;
      }
      // 找到所有节点中最x下方的节点的y坐标
      if (nodePosition.y > maxY) {
        maxY = nodePosition.y;
      }
    }
  });

  return {
    x: minX !== Infinity ? minX : position.x + dx,
    y: maxY !== -Infinity ? maxY + dy : position.y,
  };
};

/**
 * 创建节点并添加到画布
 * @param type 节点类型
 * @param graph
 * @param position 节点位置
 * @returns
 */
export const createNode = (graph: Graph, runMode: string, position?: Position) => {
  const id = StringExt.uuid();
  const node = {
    id,
    shape: 'job-node',
    x: position?.x,
    y: position?.y,
    width: 200,
    height: 60,
    ports: {
      items: [
        {
          id: `port_${id}_1`,
          group: 'left',
        },
        {
          id: `port_${id}_2`,
          group: 'right',
        },
      ],
    },
    data: {
      runMode,
    },
  };
  const newNode = graph.addNode(node);
  return newNode;
};

/**
 * 创建边
 * @param source
 * @param target
 * @param graph
 */
const createEdge = (source: string, target: string, runMode: string, graph: Graph) => {
  const edge = {
    id: StringExt.uuid(),
    source: { cell: source, port: `port_${source}_2` },
    target: { cell: target, port: `port_${target}_1` },
    labels: [
      {
        attrs: {
          label: {
            // @ts-ignore
            text: source !== 'start' ? runMode : '',
          },
        },
      },
    ],
    attrs: {
      line: {
        stroke: '#d9d9d9',
        strokeWidth: 2,
        targetMarker: {
          name: 'block',
          size: 8,
          fill: '#13c2c2',
          stroke: '#13c2c2',
        },
      },
      rect: {
        ref: 'label',
        fill: '#f5f5f5',
      },
    },
    connector: { name: 'smooth' },
    zIndex: -1,
  };
  if (graph) {
    graph.addEdge(edge);
  }
};

const JobNode: React.FC<{ node: Node }> = ({ node }) => {
  const { data, id } = node;
  // 创建下游的节点和边
  const createDownstream = () => {
    const { graph } = node.model || {};
    if (graph) {
      // 获取下游节点的初始位置信息
      const position = getDownstreamNodePosition(node, graph);
      // 创建下游节点
      const sourceData = node.getData();
      const runMode = RUN_MODE.find((item) => item.value === sourceData?.runMode)?.label || '';
      const newNode = createNode(graph, sourceData?.runMode, position);
      const source = node.id;
      const target = newNode.id;
      // 创建该节点出发到下游节点的边
      createEdge(source, target, runMode, graph);
    }
  };

  // 点击添加下游+号
  const clickAdd = (e: SyntheticEvent) => {
    cancelBubble(e);
    createDownstream();
  };

  const StatusIcon = () => {
    const { execStatus, readOnly } = data || {};
    if (readOnly) {
      switch (execStatus) {
        case '执行成功':
          return <CheckCircleOutlined className={cx('success')} />;
        case '执行失败':
          return <CloseCircleOutlined className={cx('fail')} />;
        case '执行中':
          return <LoadingOutlined className={cx('loading')} />;
        default:
          return null;
      }
    }
    return null;
  };

  const renderNode = () => {
    if (id === 'start')
      return (
        <div className={cx('start-node-container')}>
          <div className={cx('inner')}>
            开始
            {!data?.readOnly && <PlusCircleOutlined className={cx('icon')} onClick={clickAdd} />}
          </div>
        </div>
      );
    return (
      <div className={cx('job-node-container')}>
        <ToolFilled className={cx('icon')} />
        <div className={cx('info')}>
          <span className={cx('title')}>{data?.taskName}</span>
          <span className={cx('descriptions')}>{data?.descriptions}</span>
        </div>

        {!data?.taskId && <WarningFilled className={cx('error')} />}
        <StatusIcon />
        {!data?.readOnly && (
          <PlusCircleOutlined className={cx('icon', 'hover-icon')} onClick={clickAdd} />
        )}
      </div>
    );
  };
  return <>{renderNode()}</>;
};

export default JobNode;
