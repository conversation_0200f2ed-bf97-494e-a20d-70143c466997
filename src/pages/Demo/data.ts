// 任务趋势图数据
// taskTrendData.ts
export const taskTrendData = [
  { year: '2025-04-23', value: 2, category: '成功' },
  { year: '2025-04-23', value: 1, category: '失败' },
  { year: '2025-04-24', value: 3, category: '成功' },
  { year: '2025-04-24', value: 0, category: '失败' },
  { year: '2025-04-25', value: 4, category: '成功' },
  { year: '2025-04-25', value: 2, category: '失败' },
  { year: '2025-04-26', value: 1, category: '成功' },
  { year: '2025-04-26', value: 0, category: '失败' },
  { year: '2025-04-27', value: 3, category: '成功' },
  { year: '2025-04-27', value: 1, category: '失败' },
  { year: '2025-04-28', value: 5, category: '成功' },
  { year: '2025-04-28', value: 0, category: '失败' },
  { year: '2025-04-29', value: 4, category: '成功' },
  { year: '2025-04-29', value: 3, category: '失败' },
  { year: '2025-04-30', value: 6, category: '成功' },
  { year: '2025-04-30', value: 0, category: '失败' },
  { year: '2025-05-01', value: 3, category: '成功' },
  { year: '2025-05-01', value: 1, category: '失败' },
  { year: '2025-05-02', value: 2, category: '成功' },
  { year: '2025-05-02', value: 0, category: '失败' },
  { year: '2025-05-03', value: 5, category: '成功' },
  { year: '2025-05-03', value: 2, category: '失败' },
  { year: '2025-05-04', value: 4, category: '成功' },
  { year: '2025-05-04', value: 0, category: '失败' },
  { year: '2025-05-05', value: 7, category: '成功' },
  { year: '2025-05-05', value: 1, category: '失败' },
  { year: '2025-05-06', value: 5, category: '成功' },
  { year: '2025-05-06', value: 0, category: '失败' },
  { year: '2025-05-07', value: 6, category: '成功' },
  { year: '2025-05-07', value: 3, category: '失败' },
  { year: '2025-05-08', value: 3, category: '成功' },
  { year: '2025-05-08', value: 0, category: '失败' },
  { year: '2025-05-09', value: 8, category: '成功' },
  { year: '2025-05-09', value: 2, category: '失败' },
  { year: '2025-05-10', value: 4, category: '成功' },
  { year: '2025-05-10', value: 1, category: '失败' },
  { year: '2025-05-11', value: 5, category: '成功' },
  { year: '2025-05-11', value: 0, category: '失败' },
  { year: '2025-05-12', value: 6, category: '成功' },
  { year: '2025-05-12', value: 1, category: '失败' },
  { year: '2025-05-13', value: 3, category: '成功' },
  { year: '2025-05-13', value: 0, category: '失败' },
  { year: '2025-05-14', value: 7, category: '成功' },
  { year: '2025-05-14', value: 2, category: '失败' },
  { year: '2025-05-15', value: 5, category: '成功' },
  { year: '2025-05-15', value: 0, category: '失败' },
  { year: '2025-05-16', value: 6, category: '成功' },
  { year: '2025-05-16', value: 1, category: '失败' },
  { year: '2025-05-17', value: 4, category: '成功' },
  { year: '2025-05-17', value: 0, category: '失败' },
  { year: '2025-05-18', value: 8, category: '成功' },
  { year: '2025-05-18', value: 3, category: '失败' },
  { year: '2025-05-19', value: 5, category: '成功' },
  { year: '2025-05-19', value: 0, category: '失败' },
  { year: '2025-05-20', value: 7, category: '成功' },
  { year: '2025-05-20', value: 2, category: '失败' },
  { year: '2025-05-21', value: 6, category: '成功' },
  { year: '2025-05-21', value: 1, category: '失败' },
  { year: '2025-05-22', value: 9, category: '成功' },
  { year: '2025-05-22', value: 0, category: '失败' },
  { year: '2025-05-23', value: 7, category: '成功' },
  { year: '2025-05-23', value: 1, category: '失败' },
];

export const pieData = [
  { type: '紧急', value: 2 },
  { type: '严重', value: 3 },
  { type: '中等', value: 11 },
  { type: '一般', value: 7 },
];

// 中间件告警数据
export const middlewareMockData = [
  { hostid: '10482', name: '生产环境.weblogic.**************.ServerA', count: 3 },
  { hostid: '10273', name: '5A外网门户.wls.**************.7001.AdminServer', count: 2 },
  { hostid: '10484', name: '开发环境.jboss.*************.AppServer', count: 1 },
  { hostid: '10483', name: '生产环境.weblogic.**************.ServerB', count: 0 },
  { hostid: '10480', name: '测试.wls.***************.8001.ms1', count: 2 },
  { hostid: '10274', name: '5A外网门户.wls.**************.8001.ms1', count: 1 },
  { hostid: '10481', name: '测试.wls.***************.8001.AdminServer', count: 3 },
  { hostid: '10485', name: '测试环境.tomcat.*************.WebApp', count: 0 },
];

// 最近执行的任务
export const recentTaskData = [
  {
    id: 40215,
    startTime: '2025-05-08 05:52:39',
    endTime: '2025-05-08 05:52:45',
    missionId: 835,
    missionName: 'oracle backup2025-05-08 05:52:27',
    execUserId: 1,
    execUserName: 'admin',
    execStatus: '执行失败',
    execInfo:
      '\nPLAY [exec script] *************************************************************\n\nTASK [rklink.oracle.backup : upload script] ************************************\nchanged: [**************]\n\nTASK [rklink.oracle.backup : exec expdp_full_backup.sh script] *****************\nfatal: [**************]: FAILED! => {\n    "changed": true, \n    "cmd": "source ~/.bash_profile;sh expdp_full_backup.sh ", \n    "delta": "0:00:00.722667", \n    "end": "2025-05-08 13:52:31.134003", \n    "rc": 1, \n    "start": "2025-05-08 13:52:30.411336"\n}\n\nSTDOUT:\n\n开始备份实例: asdf , 全库导出...\n备份失败,请检查日志: /backup/20250508135231_asdf_FULL_export.log\n\n\nSTDERR:\n\n\nExport: Release ********.0 - Production on Thu May 8 13:52:31 2025\n\nCopyright (c) 1982, 2011, Oracle and/or its affiliates.  All rights reserved.\n\nUDE-01034: operation generated ORACLE error 1034\nORA-01034: ORACLE not available\nORA-27101: shared memory realm does not exist\nLinux-x86_64 Error: 2: No such file or directory\nUDE-00003: all allowable logon attempts failed\n\n\nMSG:\n\nnon-zero return code\n\nPLAY RECAP *********************************************************************\n**************             : ok=1    changed=1    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   \n',
    taskRange: 'task',
    status: 'audited',
    coverId: null,
    streamLogId: null,
  },
  {
    id: 40214,
    startTime: '2025-05-08 05:46:33',
    endTime: '2025-05-08 05:48:12',
    missionId: 834,
    missionName: 'oracle backup2025-05-08 05:16:47',
    execUserId: 1,
    execUserName: 'admin',
    execStatus: '执行成功',
    execInfo:
      '\nPLAY [exec script] *************************************************************\n\nTASK [rklink.oracle.backup : upload script] ************************************\nchanged: [**************]\n\nTASK [rklink.oracle.backup : exec expdp_full_backup.sh script] *****************\nchanged: [**************]\n\nTASK [rklink.oracle.backup : debug] ********************************************\nok: [**************] => {\n    "result.stdout_lines": [\n        "开始备份实例: vjs1 , 全库导出...", \n        "备份完成: /backup/20250508134625_vjs1_FULL_export_*.dmp"\n    ]\n}\n\nTASK [rklink.oracle.backup : debug] ********************************************\nok: [**************] => {\n    "result.rc": "0"\n}\n\nPLAY RECAP *********************************************************************\n**************             : ok=4    changed=2    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   \n',
    taskRange: 'task',
    status: 'audited',
    coverId: null,
    streamLogId: null,
  },
  {
    id: 40213,
    startTime: '2025-05-08 05:44:13',
    endTime: '2025-05-08 05:45:48',
    missionId: 834,
    missionName: 'oracle backup2025-05-08 05:16:47',
    execUserId: 1,
    execUserName: 'admin',
    execStatus: '执行成功',
    execInfo:
      '\nPLAY [exec script] *************************************************************\n\nTASK [rklink.oracle.backup : upload script] ************************************\nchanged: [**************]\n\nTASK [rklink.oracle.backup : exec expdp_full_backup.sh script] *****************\nchanged: [**************]\n\nTASK [rklink.oracle.backup : debug] ********************************************\nok: [**************] => {\n    "result.stdout_lines": [\n        "开始备份实例: vjs1 , 全库导出...", \n        "备份完成: /backup/20250508134405_vjs1_FULL_export.dmp"\n    ]\n}\n\nTASK [rklink.oracle.backup : debug] ********************************************\nok: [**************] => {\n    "result.rc": "0"\n}\n\nPLAY RECAP *********************************************************************\n**************             : ok=4    changed=2    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   \n',
    taskRange: 'task',
    status: 'audited',
    coverId: null,
    streamLogId: null,
  },
  {
    id: 40212,
    startTime: '2025-05-08 05:39:51',
    endTime: '2025-05-08 05:41:33',
    missionId: 834,
    missionName: 'oracle backup2025-05-08 05:16:47',
    execUserId: 1,
    execUserName: 'admin',
    execStatus: '执行成功',
    execInfo:
      '\nPLAY [exec script] *************************************************************\n\nTASK [rklink.oracle.backup : upload script] ************************************\nchanged: [**************]\n\nTASK [rklink.oracle.backup : exec expdp_full_backup.sh script] *****************\nchanged: [**************]\n\nTASK [rklink.oracle.backup : debug] ********************************************\nok: [**************] => {\n    "result.stdout_lines": [\n        "开始备份实例: vjs1 , 全库导出...", \n        "备份完成: /backup/20250508133943_vjs1_FULL_export_.dmp"\n    ]\n}\n\nTASK [rklink.oracle.backup : debug] ********************************************\nok: [**************] => {\n    "result.rc": "0"\n}\n\nPLAY RECAP *********************************************************************\n**************             : ok=4    changed=2    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   \n',
    taskRange: 'task',
    status: 'audited',
    coverId: null,
    streamLogId: null,
  },
  {
    id: 40211,
    startTime: '2025-05-08 05:33:50',
    endTime: '2025-05-08 05:35:24',
    missionId: 834,
    missionName: '主机连通性测试2025-03-13 08:27:04',
    execUserId: 1,
    execUserName: 'admin',
    execStatus: '执行成功',
    execInfo:
      '\nPLAY [exec script] *************************************************************\n\nTASK [rklink.oracle.backup : upload script] ************************************\nchanged: [**************]\n\nTASK [rklink.oracle.backup : exec expdp_full_backup.sh script] *****************\nchanged: [**************]\n\nTASK [rklink.oracle.backup : debug] ********************************************\nok: [**************] => {\n    "result.stdout_lines": [\n        "开始备份实例: vjs1 , 全库导出...", \n        "备份完成: /backup/20250508133342_vjs1_FULL_export_.dmp"\n    ]\n}\n\nTASK [rklink.oracle.backup : debug] ********************************************\nok: [**************] => {\n    "result.rc": "0"\n}\n\nPLAY RECAP *********************************************************************\n**************             : ok=4    changed=2    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   \n',
    taskRange: 'stream',
    status: 'audited',
    coverId: null,
    streamLogId: null,
  },
];

export const statusEnum = {
  unAudited: { text: '未审核', status: 'Default' },
  audited: { text: '审核通过', status: 'Success' },
  overdue: { text: '过期', status: 'Warning' },
  refuse: { text: '驳回', status: 'Error' },
  invalid: { text: '作废', color: '#ffbb96' },
  cancel: { text: '撤回', color: '#ffbb96' },
};

export const taskRangeEnum = {
  stream: { text: '作业流', tagColor: 'cyan' },
  task: { text: '作业', tagColor: 'green' },
  plan: { text: '计划', tagColor: 'volcano' },
};
