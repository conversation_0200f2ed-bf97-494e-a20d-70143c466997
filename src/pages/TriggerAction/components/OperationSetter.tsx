import BaseContext from '@/Context/BaseContext';
import { OPERATION_TYPE } from '@/enums';
import { zabbix } from '@/services/zabbix';
import { getRandomId } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  ProColumns,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, Space, Typography } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import React, { memo, useCallback, useContext, useRef, useState } from 'react';

const pattern = /^(?:0|60|[1-5]\d{1,5}|[6-9]\d{1,4}|[1-5]\d{5}|6[0-4]\d{4}|604[0-7]\d{2}|604800)$/;

type OperationSetterProps = {
  value?: RK_API.Operation[];
  onChange?: (value: RK_API.Operation[]) => void;
};
const { Text } = Typography;

const OperationSetter: React.FC<OperationSetterProps> = ({ value = [], onChange }) => {
  const drawerFormRef = useRef<ProFormInstance>();
  const [open, setOpen] = useState(false);
  const initialValuesRef = useRef<RK_API.Operation | undefined>();

  const {
    userList = [],
    userLoading,
    userGroupList,
    userGroupLoading,
    hostGroupList = [],
    hostGroupLoading,
    hostList = [],
    hostLoading,
  } = useContext(BaseContext);

  // 媒介类型
  const { data: mediaTypeList = [], loading: mediaTypeLoading } = useRequest(() =>
    zabbix({ method: 'mediatype.get' }),
  );

  const renderDetails = useCallback(
    (_: any, record: RK_API.Operation) => {
      const {
        operationtype,
        opmessage_grp = [],
        opmessage_usr = [],
        opmessage,
        opcommand,
        opcommand_hst = [],
        opcommand_grp = [],
      } = record;

      // 发送信息
      if (operationtype === '0') {
        const arr = [...opmessage_usr, ...opmessage_grp];
        const mediaType = [{ mediatypeid: '0', name: '所有介质' }, ...mediaTypeList].find(
          (item) => item.mediatypeid === opmessage.mediatypeid,
        )?.name;
        return (
          <Space direction="vertical" size={2}>
            {arr.map((item, index) => {
              const str = item.userid ? '用户' : '用户组';
              const receiver = item.userid
                ? userList?.find((user) => user.userid === item?.userid)?.username
                : userGroupList?.find((group) => group.usrgrpid === item?.usrgrpid)?.name;
              return (
                <div key={index}>
                  <Text strong>
                    通过{mediaType}发送信息给{str}：
                  </Text>
                  <Text>{receiver}</Text>
                </div>
              );
            })}
          </Space>
        );
      }
      // 脚本信息
      if (operationtype === '1') {
        const opera = opcommand?.scriptid === '1' ? '运行“Ping”脚本' : '运行“Traceroute”脚本';
        const arr = [...opcommand_hst, ...opcommand_grp];
        return (
          <Space direction="vertical" size={2}>
            {arr.map((item, index) => {
              const receiver = item.hostid
                ? item?.hostid === '0'
                  ? '在当前主机上'
                  : `在主机${hostList?.find((host) => host.hostid === item?.hostid)?.name}`
                : `在对象群组${
                    hostGroupList?.find((group) => group.groupid === item?.groupid)?.name
                  }`;
              return (
                <div key={index}>
                  <Text>{receiver}</Text>
                  <Text strong>{opera}</Text>
                </div>
              );
            })}
          </Space>
        );
      }
      return '';
    },
    [userLoading, userGroupLoading, hostLoading, hostGroupLoading, mediaTypeLoading],
  );

  const onFinish = async (val: RK_API.Operation) => {
    const isItemExists = value.some((item) => item.operationid === val.operationid);

    const dataSource = isItemExists
      ? value.map((item) => (item.operationid === val.operationid ? val : item))
      : [...value, val];

    onChange?.(dataSource);

    return true;
  };

  // 表格
  const columns: ProColumns<RK_API.Operation>[] = [
    {
      title: '步骤',
      dataIndex: 'esc_step_from',
      width: 100,
      renderText(text, record) {
        const end = record.esc_step_to;
        return `${text} ${end <= text ? '' : `- ${end}`} `;
      },
    },
    {
      title: '细节',
      dataIndex: 'details',
      render: renderDetails,
    },
    // {
    //   title: '开始于',
    //   dataIndex: 'details',
    // },
    {
      title: '持续时间',
      dataIndex: 'esc_period',
      renderText(text) {
        return text === '0' ? '默认' : text;
      },
    },
    {
      title: '操作',
      width: 100,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (_, record) => {
        return (
          <Space>
            <a
              onClick={() => {
                setOpen(true);
                initialValuesRef.current = record;
              }}
            >
              编辑
            </a>
            <a
              onClick={() => {
                const arr = value.filter((item) => item.operationid !== record.operationid);
                onChange?.(arr);
              }}
            >
              移除
            </a>
          </Space>
        );
      },
    },
  ];
  // 操作类型 数据处理 operationtype
  const transformOperationType = useCallback((val: string) => {
    const arr = val.split('-');
    const type = arr?.[0];
    const scriptid = arr?.[1] || '';
    return {
      operationtype: type,
      opcommand: scriptid
        ? {
            scriptid: scriptid,
          }
        : undefined,
    };
  }, []);

  const getOperationType = (val: string) => {
    const opcommand = drawerFormRef.current?.getFieldValue('opcommand');
    if (val === '1') {
      return `${val}-${opcommand?.scriptid}`;
    }
    return val;
  };

  return (
    <>
      <ProTable<RK_API.Operation>
        {...defaultTableConfig}
        search={false}
        options={false}
        className="inner-table"
        rowKey="operationid"
        columns={columns}
        headerTitle="操作"
        dataSource={value}
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              onClick={() => {
                setOpen(true);
                initialValuesRef.current = undefined;
              }}
            >
              新建
            </Button>,
          ],
        }}
      />
      <DrawerForm
        width={460}
        initialValues={{
          operationid: getRandomId(),
          evaltype: '0',
          operationtype: '0',
          esc_step_from: '1',
          esc_step_to: '1',
          esc_period: '0',
          opmessage: {
            default_msg: '1', // 使用 媒介类型中的数据。
            mediatypeid: '0',
          },
          opcommand_hst: [
            {
              hostid: '0',
            },
          ],
          ...initialValuesRef.current,
        }}
        title="操作细节"
        open={open}
        onOpenChange={setOpen}
        autoFocusFirstInput
        formRef={drawerFormRef}
        drawerProps={{
          destroyOnClose: true,
        }}
        onFinish={onFinish}
      >
        <div className="rk-none ">
          <ProFormText name={'operationid'} />
          <ProFormText name={'evaltype'} />
        </div>
        {/* TODO 类型跟文档有差异 
          https://www.zabbix.com/documentation/6.0/zh/manual/api/reference/action/object#%E5%8A%A8%E4%BD%9C%E6%93%8D%E4%BD%9C 
        */}
        <ProFormSelect
          name="operationtype"
          label="操作类型"
          rules={[requiredRule]}
          fieldProps={{
            allowClear: false,
          }}
          options={OPERATION_TYPE}
          convertValue={getOperationType}
          getValueFromEvent={getOperationType}
          transform={transformOperationType}
        />
        <ProFormDigit
          label="开始步骤"
          name="esc_step_from"
          fieldProps={{
            precision: 0,
          }}
          min={1}
          rules={[requiredRule]}
        />

        <ProFormDigit
          label="结束步骤"
          tooltip="0代表无穷大"
          name="esc_step_to"
          fieldProps={{
            precision: 0,
          }}
          min={0}
          rules={[requiredRule]}
        />
        <ProFormDigit
          label="步骤持续时间"
          name="esc_period"
          fieldProps={{
            precision: 0,
          }}
          min={0}
          max={604800}
          rules={[{ pattern, message: '步骤持续时间范围0,60-604800' }]}
          addonAfter="秒"
          transform={(value: number, namePath) => ({
            [namePath]: `${value}`,
          })}
        />

        <ProFormDependency name={['operationtype']}>
          {({ operationtype }) => {
            // 0 发送信息
            return (
              operationtype === '0' && (
                <>
                  <ProFormSelect
                    label="用户组"
                    name="opmessage_grp"
                    options={userGroupList as DefaultOptionType[]}
                    fieldProps={{
                      showSearch: true,
                      mode: 'multiple',
                      loading: userGroupLoading,
                      fieldNames: {
                        value: 'usrgrpid',
                        label: 'name',
                      },
                    }}
                    getValueFromEvent={(value = []) =>
                      value.map((item: string) => ({ usrgrpid: item }))
                    }
                    convertValue={(value = []) =>
                      value.map((item: RK_API.UsrgrpsItem) => item?.usrgrpid)
                    }
                    transform={(value: RK_API.UsrgrpsItem[], namePath) => ({
                      [namePath]: value.map(({ usrgrpid }) => ({ usrgrpid })),
                    })}
                  />
                  <ProFormSelect
                    label="用户"
                    name="opmessage_usr"
                    options={userList as DefaultOptionType[]}
                    fieldProps={{
                      showSearch: true,
                      mode: 'multiple',
                      loading: userLoading,
                      fieldNames: {
                        value: 'userid',
                        label: 'username',
                      },
                    }}
                    rules={[
                      ({ getFieldValue }) => ({
                        validator: (_, value) => {
                          const op_message_grp = getFieldValue('opmessage_grp');
                          if (!value && !op_message_grp) {
                            return Promise.reject(new Error('用户和用户组至少选择一个'));
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                    getValueFromEvent={(value = []) =>
                      value.map((item: string) => ({ userid: item }))
                    }
                    convertValue={(value = []) => value.map((item: RK_API.User) => item?.userid)}
                    transform={(value: RK_API.User[], namePath) => ({
                      [namePath]: value.map(({ userid }) => ({ userid })),
                    })}
                  />
                  <ProFormSelect
                    label="仅送到"
                    name={['opmessage', 'mediatypeid']}
                    options={[{ mediatypeid: '0', name: '所有介质' }, ...mediaTypeList]}
                    fieldProps={{
                      fieldNames: {
                        value: 'mediatypeid',
                        label: 'name',
                      },
                      loading: mediaTypeLoading,
                    }}
                  />
                  <ProFormSwitch
                    label="自定义消息"
                    name={['opmessage', 'default_msg']}
                    getValueFromEvent={(val) => (val ? '0' : '1')}
                    getValueProps={(value) => ({ checked: value === '0' })}
                  />
                  <ProFormDependency name={['opmessage', 'default_msg']}>
                    {({ opmessage }) => {
                      return opmessage.default_msg === '1' ? (
                        <></>
                      ) : (
                        <>
                          <ProFormText name={['opmessage', 'subject']} label="主题" />
                          <ProFormTextArea
                            name={['opmessage', 'message']}
                            label="消息"
                            fieldProps={{ autoSize: { minRows: 3, maxRows: 3 } }}
                          />
                        </>
                      );
                    }}
                  </ProFormDependency>
                </>
              )
            );
          }}
        </ProFormDependency>
        <ProFormDependency name={['operationtype']}>
          {({ operationtype }) => {
            // 1 全局脚本
            return (
              operationtype === '1' && (
                <>
                  <ProFormSelect
                    label="对象群组"
                    name="opcommand_grp"
                    options={hostGroupList as DefaultOptionType[]}
                    fieldProps={{
                      showSearch: true,
                      mode: 'multiple',
                      fieldNames: {
                        value: 'groupid',
                        label: 'name',
                      },
                      loading: hostGroupLoading,
                    }}
                    getValueFromEvent={(value = []) =>
                      value.map((item: string) => ({ groupid: item }))
                    }
                    convertValue={(value = []) =>
                      value.map((item: RK_API.HostGroup) => item?.groupid)
                    }
                    transform={(value: RK_API.HostGroup[], namePath) => ({
                      [namePath]: value.map(({ groupid }) => ({ groupid })),
                    })}
                  />
                  <ProFormSelect
                    label="主机"
                    name="opcommand_hst"
                    options={
                      [{ hostid: '0', name: '本机' }, ...hostList] as unknown as DefaultOptionType[]
                    }
                    fieldProps={{
                      showSearch: true,
                      mode: 'multiple',
                      loading: hostLoading,
                      fieldNames: {
                        value: 'hostid',
                        label: 'name',
                      },
                    }}
                    getValueFromEvent={(value = []) =>
                      value.map((item: string) => ({ hostid: item }))
                    }
                    convertValue={(value = []) => value.map((item: RK_API.Host) => item?.hostid)}
                    transform={(value: RK_API.Host[], namePath) => ({
                      [namePath]: value.map(({ hostid }) => ({ hostid })),
                    })}
                    rules={[
                      ({ getFieldValue }) => ({
                        validator: (_, value) => {
                          const opcommand_grp = getFieldValue('opcommand_grp');
                          if (!value && !opcommand_grp) {
                            return Promise.reject(new Error('主机和对象群组至少选择一个'));
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                  />
                </>
              )
            );
          }}
        </ProFormDependency>
        <ProFormSwitch
          label={
            <span>
              触发动作的操作条件
              <em className="rk-optional">（事件是否被确认）</em>
            </span>
          }
          name="opconditions"
          getValueFromEvent={(val) => [
            {
              opconditionid: '1',
              operationid: '54',
              conditiontype: '14',
              operator: '0',
              value: val ? '1' : '0',
            },
          ]}
          getValueProps={(value) => ({ checked: value?.[0]?.value === '1' })}
        />
      </DrawerForm>
    </>
  );
};
export default memo(OperationSetter);
