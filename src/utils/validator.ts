// 手机号校验
export const phoneReg = /^1[3-9]\d{9}$/;

// 密码
export const passwordReg = /(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,}/;

// 名称
export const nameReg = /[a-zA-Z\u4e00-\u9fa5]/;

// ip
export const IPReg =
  /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;

// port
export const PortReg = /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/;

// 字母数字、空格、点、破折号和下划线
export const name1Reg = /^[a-zA-Z0-9\s.\-_]+$/;
