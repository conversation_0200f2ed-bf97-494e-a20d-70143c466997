import { InputNumber, Select } from 'antd';
import { useMemo, useState } from 'react';
import styles from './index.less';

export type UnitInputProps = {
  value?: string;
  onChange?: (value: string) => void;
  options?: Record<string, any>[];
};

const UnitInput: React.FC<UnitInputProps> = ({ value, options, onChange }) => {
  const num = useMemo(() => value?.replace(/[^\d]/gi, '') || null, [value]);
  const [unit, setUnit] = useState(value?.replace(/\d/gi, ''));
  const handleChange = (val: string | null) => {
    onChange?.(`${val}${unit}`);
  };
  const addonAfter = (
    <Select
      allowClear={false}
      options={options}
      value={unit}
      onChange={(val) => {
        setUnit(val);
        onChange?.(`${num}${unit}`);
      }}
    ></Select>
  );
  return (
    <div className={styles['unit-select']}>
      <InputNumber
        className={styles.input}
        precision={0}
        value={num}
        onChange={handleChange}
        addonAfter={addonAfter}
      />
    </div>
  );
};

export default UnitInput;
