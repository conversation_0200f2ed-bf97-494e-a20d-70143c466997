import BaseContext from '@/Context/BaseContext';
import { CONDITION_OPERATOR, CONDITION_TYPE, SEVERITIES } from '@/enums';
import { periodTooltips } from '@/pages/Users/<USER>/MediaSetter';
import { getOptionLabel } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  ProColumns,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { Button } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import { memo, useCallback, useContext, useRef, useState } from 'react';

const generateId = (arr: Record<string, any>[] = []) => {
  const [lastRecord] = arr.slice(-1);
  let lastId = 0;
  if (lastRecord?.formulaid) {
    lastId =
      lastRecord.formulaid.length === 1
        ? lastRecord.formulaid.charCodeAt(0) - 64
        : lastRecord.formulaid[0].charCodeAt(0) -
          65 +
          26 +
          lastRecord.formulaid[1].charCodeAt(0) -
          64;
  }

  if (lastId < 26) {
    return String.fromCharCode(65 + lastId); // 65 corresponds to 'A' in ASCII
  } else {
    const firstCharIndex = Math.floor(lastId / 26) - 1;
    const secondCharIndex = lastId % 26;
    const firstChar = String.fromCharCode(65 + firstCharIndex);
    const secondChar = String.fromCharCode(65 + secondCharIndex);
    return firstChar + secondChar;
  }
};

type MappingProps = {
  [key: string]: typeof CONDITION_OPERATOR;
};

type Condition = RK_API.Condition;

type ConditionSetterProps = {
  value?: Condition[];
  onChange?: (value: Condition[]) => void;
};

// 条件mapping
const operatorMapping: MappingProps = {
  '3': CONDITION_OPERATOR.filter((item) => ['2', '3'].includes(item.value)), // 触发器名称
  '2': CONDITION_OPERATOR.filter((item) => ['0', '1'].includes(item.value)), // 触发器
  '4': CONDITION_OPERATOR.filter((item) => ['0', '1', '5', '6'].includes(item.value)), // 触发器严重等级
  '1': CONDITION_OPERATOR.filter((item) => ['0', '1'].includes(item.value)), // 主机
  '0': CONDITION_OPERATOR.filter((item) => ['0', '1'].includes(item.value)), // 对象群组
  '16': CONDITION_OPERATOR.filter((item) => ['10', '11'].includes(item.value)), // 问题被抑制
  '25': CONDITION_OPERATOR.filter((item) => ['0', '1', '2', '3'].includes(item.value)), // 事件标签
  '26': CONDITION_OPERATOR.filter((item) => ['0', '1', '2', '3'].includes(item.value)), // 事件标签值
  '13': CONDITION_OPERATOR.filter((item) => ['0', '1'].includes(item.value)), // 主机监控模板
  '6': CONDITION_OPERATOR.filter((item) => ['4', '7'].includes(item.value)), // 时间段
};

const ConditionSetter: React.FC<ConditionSetterProps> = ({ value = [], onChange }) => {
  const drawerFormRef = useRef<ProFormInstance>();
  const [open, setOpen] = useState(false);
  const {
    hostGroupList = [],
    hostGroupLoading,
    hostList = [],
    hostLoading,
    triggerList = [],
    triggerLoading,
    templateList = [],
    templateLoading,
  } = useContext(BaseContext);
  const conditiontypeChange = useCallback(() => {
    drawerFormRef.current?.setFieldsValue({ operator: null, value: undefined });
  }, []);

  const onFinish = async (val: Condition) => {
    const dataSource = [...value, val];
    onChange?.(dataSource);
    return true;
  };

  const renderDetails = (_: Condition, record: Condition) => {
    const { conditiontype, operator, value, value2 } = record;
    const conditiontypeText = getOptionLabel(CONDITION_TYPE, conditiontype);
    const operatorText = getOptionLabel(CONDITION_OPERATOR, operator);
    // 问题被抑制
    if (conditiontype === '16') return operator === '10' ? '问题被抑制' : '问题没有被抑制';
    // 触发器严重等级
    if (conditiontype === '4')
      return `${conditiontypeText}${operatorText}${getOptionLabel(SEVERITIES, value)}`;
    if (value2) return `${conditiontypeText}${value2}${operatorText} ${value}`;
    // 对象群组
    if (conditiontype === '0') {
      const hostGroup = hostGroupList.find(
        (item: RK_API.HostGroup) => item.groupid === value,
      )?.name;

      return `${conditiontypeText}${operatorText}${hostGroup}`;
    }
    // 主机
    if (conditiontype === '1') {
      const host = hostList.find((item: RK_API.Host) => item.hostid === value)?.name;
      return `${conditiontypeText}${operatorText}${host}`;
    }
    // 触发器
    if (conditiontype === '2') {
      const trigger = triggerList.find(
        (item: RK_API.Trigger) => item.triggerid === value,
      )?.description;

      return `${conditiontypeText}${operatorText}${trigger}`;
    }
    // 模版
    if (conditiontype === '13') {
      const template = templateList.find(
        (item: RK_API.Template) => item.templateid === value,
      )?.name;
      return `${conditiontypeText}${operatorText}${template}`;
    }
    return `${conditiontypeText}${operatorText}${value}`;
  };

  // 表格
  const columns: ProColumns<RK_API.Condition>[] = [
    {
      title: '标签',
      dataIndex: 'formulaid',
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'details',
      renderText: renderDetails,
    },
    {
      title: '操作',
      width: 80,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (_, record) => {
        return (
          <a
            onClick={() => {
              const arr = value.filter((item) => item.formulaid !== record.formulaid);
              onChange?.(arr);
            }}
          >
            移除
          </a>
        );
      },
    },
  ];

  return (
    <>
      <ProTable<RK_API.Condition>
        {...defaultTableConfig}
        search={false}
        options={false}
        className="inner-table"
        rowKey="formulaid"
        columns={columns}
        headerTitle="条件"
        dataSource={value}
        toolbar={{
          actions: [
            <Button key="add" type="primary" onClick={() => setOpen(true)}>
              新建
            </Button>,
          ],
        }}
      />
      <DrawerForm
        width={460}
        title="新的触发条件"
        open={open}
        onOpenChange={setOpen}
        autoFocusFirstInput
        formRef={drawerFormRef}
        drawerProps={{
          destroyOnClose: true,
        }}
        onFinish={onFinish}
      >
        <div className="rk-none ">
          <ProFormText name={'formulaid'} initialValue={generateId(value)} />
        </div>
        <ProFormSelect
          name="conditiontype"
          label="类型"
          rules={[requiredRule]}
          fieldProps={{
            allowClear: false,
            onChange: conditiontypeChange,
          }}
          options={CONDITION_TYPE}
        />
        <ProFormSelect
          name="operator"
          dependencies={['conditiontype']}
          label="条件运算符"
          rules={[requiredRule]}
          request={async ({ conditiontype }) => {
            return operatorMapping?.[conditiontype] || [];
          }}
        />
        {/* 动作过滤条件类型===触发器名称 */}
        <ProFormDependency name={['conditiontype']}>
          {({ conditiontype }) => {
            return (
              conditiontype === '3' && (
                <ProFormText name="value" label="值" rules={[requiredRule]} />
              )
            );
          }}
        </ProFormDependency>
        {/* 动作过滤条件类型===触发器、主机、对象群组、模版 */}
        <ProFormDependency name={['conditiontype']}>
          {({ conditiontype }) => {
            // 对象群组
            if (conditiontype === '0') {
              return (
                <ProFormSelect
                  name="value"
                  label={CONDITION_TYPE.find((item) => item.value === conditiontype)?.label}
                  rules={[requiredRule]}
                  fieldProps={{
                    showSearch: true,
                    fieldNames: {
                      label: 'name',
                      value: 'groupid',
                    },
                    loading: hostGroupLoading,
                  }}
                  options={hostGroupList as DefaultOptionType[]}
                />
              );
            }
            // 主机
            if (conditiontype === '1') {
              return (
                <ProFormSelect
                  name="value"
                  label={CONDITION_TYPE.find((item) => item.value === conditiontype)?.label}
                  rules={[requiredRule]}
                  fieldProps={{
                    showSearch: true,
                    fieldNames: {
                      label: 'name',
                      value: 'hostid',
                    },
                    loading: hostLoading,
                  }}
                  options={hostList as unknown as DefaultOptionType[]}
                />
              );
            }
            // 触发器
            if (conditiontype === '2') {
              return (
                <ProFormSelect
                  name="value"
                  label={CONDITION_TYPE.find((item) => item.value === conditiontype)?.label}
                  rules={[requiredRule]}
                  fieldProps={{
                    showSearch: true,
                    fieldNames: {
                      label: 'description',
                      value: 'triggerid',
                    },
                    loading: triggerLoading,
                  }}
                  options={triggerList as unknown as DefaultOptionType[]}
                />
              );
            }
            // 模版
            if (conditiontype === '13') {
              return (
                <ProFormSelect
                  name="value"
                  label={CONDITION_TYPE.find((item) => item.value === conditiontype)?.label}
                  rules={[requiredRule]}
                  fieldProps={{
                    showSearch: true,
                    fieldNames: {
                      label: 'name',
                      value: 'templateid',
                    },
                    loading: templateLoading,
                  }}
                  options={templateList as DefaultOptionType[]}
                />
              );
            }
          }}
        </ProFormDependency>
        {/*  动作过滤条件类型===触发器严重等级 */}
        <ProFormDependency name={['conditiontype']}>
          {({ conditiontype }) => {
            return (
              conditiontype === '4' && (
                <ProFormSelect
                  name="value"
                  label="严重性"
                  options={SEVERITIES}
                  rules={[requiredRule]}
                />
              )
            );
          }}
        </ProFormDependency>
        {/*  动作过滤条件类型===标签*/}
        <ProFormDependency name={['conditiontype']}>
          {({ conditiontype }) => {
            return (
              conditiontype === '25' && (
                <ProFormText name="value" label="标签" rules={[requiredRule]} />
              )
            );
          }}
        </ProFormDependency>
        {/*  动作过滤条件类型===事件标签值 */}
        <ProFormDependency name={['conditiontype']}>
          {({ conditiontype }) => {
            return (
              conditiontype === '26' && (
                <>
                  <ProFormText name="value2" label="标签" rules={[requiredRule]} />
                  <ProFormText name="value" label="ֵ值" rules={[requiredRule]} />
                </>
              )
            );
          }}
        </ProFormDependency>
        {/*  动作过滤条件类型===时间段 */}
        <ProFormDependency name={['conditiontype']}>
          {({ conditiontype }) => {
            return (
              conditiontype === '6' && (
                <ProFormText
                  name="value"
                  label="ֵ值"
                  rules={[requiredRule]}
                  initialValue="1-7,00:00-24:00"
                  tooltip={periodTooltips}
                />
              )
            );
          }}
        </ProFormDependency>
      </DrawerForm>
    </>
  );
};
export default memo(ConditionSetter);
