.pageContainer {
  position: relative;
  display: flex;
  flex-direction: column;

  &.full_container {
    height: calc(100vh - 65px) !important;
    overflow: hidden;
  }
  :global {
    .ant-pro-grid-content {
      flex: 1;
      box-sizing: border-box;
      padding: 24px 24px 0 24px;
      padding: 0;
      .ant-pro-grid-content-children {
        height: 100%;
        .ant-pro-page-container-children-container {
          height: 100%;
          padding: 0;
        }
      }
    }
  }
  .screen_box {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 1000;
  }
}

.editor {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  &-header {
    height: 48px;
  }
  &-body {
    position: relative;
    flex: 1;
    // height: calc(100% - 48px);
    .item,
    .container,
    .sidebar {
      height: 100%;
    }
    .sidebar {
      display: flex;
      flex-direction: column;
    }
  }

  .node {
    font-size: 36px;
  }
  :global {
    .x6-graph-svg-viewport {
      &:hover {
        .x6-port {
          display: block;
        }
      }
    }

    .x6-port {
      display: none;
    }

    .x6-edge-selected path:nth-child(2) {
      animation: running-line 30s infinite linear;
      // stroke: var(--primary-color);
      stroke-width: 2px !important;
    }
    // 框选样式
    .x6-widget-selection-inner {
      padding: 24px;
      background-color: rgb(230 244 255 / 50%);
      border-color: var(--primary-color);
      box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12),
        0 9px 28px 8px rgba(0, 0, 0, 0.05);
    }
    // node 选中样式
    .x6-widget-selection-box {
      border: none;
      box-shadow: none;
    }
  }
  &.read_only {
    .editor-body {
      height: 100%;
    }
    :global {
      .x6-graph-svg-viewport {
        &:hover {
          .x6-port {
            display: none;
          }
        }
      }

      .x6-port {
        display: none;
      }
    }
  }
}
