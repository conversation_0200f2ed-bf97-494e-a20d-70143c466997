import { createContext } from 'react';

type BaseContextProps = {
  userList?: RK_API.UserInfoDTO[];
  userLoading?: boolean;
  userGroupList?: RK_API.UserGroupDTO[];
  userGroupLoading?: boolean;
  hostList?: RK_API.Host[];
  hostLoading?: boolean;
  hostGroupList?: RK_API.HostGroup[];
  hostGroupLoading?: boolean;
  triggerList?: RK_API.Trigger[];
  triggerLoading?: boolean;
  templateList?: RK_API.Template[];
  templateLoading?: boolean;
  atomicTask?: API.AtomicTaskVO[];
  atomicTaskLoading?: boolean;
  objectList?: Record<string, any>[];
  objectListLoading?: boolean;
};

const BaseContext = createContext<BaseContextProps>({});

export default BaseContext;
