import { zabbixList } from '@/services/zabbix';
import { useRequest } from '@umijs/max';

export function useUserList() {
  const { data, loading } = useRequest(() =>
    zabbixList({
      method: 'user.get',
      selectRole: 'extend',
      selectUsrgrps: 'extend',
      sortfield: ['username'], // 排序
      getAccess: true, // 添加关于用户权限附加信息
    }),
  );

  return {
    userList: data as API.UserInfoDTO[],
    loading,
  };
}
