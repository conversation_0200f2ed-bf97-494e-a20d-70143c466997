import { RUN_MODE } from '@/enums';
import { Graph, Node } from '@antv/x6';
import { Keyboard } from '@antv/x6-plugin-keyboard';
import { Selection } from '@antv/x6-plugin-selection';
import { Portal, register } from '@antv/x6-react-shape';
import classNames from 'classnames/bind';
import React, { memo, useEffect, useRef } from 'react';
import FlowToolbar from '../FlowToolbar';
import JobNode from '../JobNode';
import styles from './index.less';
const cx = classNames.bind(styles);

// 初始数据
export const initialData = {
  nodes: [
    {
      id: 'start',
      shape: 'job-node',
      x: 24,
      y: 200,
      width: 60,
      height: 60,
      label: '开始',
      attrs: {
        body: {
          fill: '#b7eb8f',
          stroke: '#73d13d',
        },
      },
      ports: {
        items: [
          {
            id: 'port_start_2',
            group: 'right',
          },
        ],
      },
    },
  ],
  edges: [],
};

const GRID_SIZE = 20;
const X6ReactPortalProvider = Portal.getProvider();
register({
  shape: 'job-node',
  effect: ['data'],
  component: JobNode,
  ports: {
    groups: {
      left: {
        position: 'left',
        attrs: {
          circle: {
            magnet: true,
            stroke: '#8f8f8f',
            r: 5,
          },
        },
      },
      right: {
        position: 'right',
        attrs: {
          circle: {
            magnet: true,
            stroke: '#8f8f8f',
            r: 5,
          },
        },
      },
    },
  },
});

type FlowProps = {
  onNodeDBLClick?: (node: Node) => void;
  onNodeClick?: (node: Node) => void;
  readOnly?: boolean;
  center?: boolean;
  onMount?: (graph: Graph) => void;
  hideToolbar?: boolean;
  selectedDisabled?: boolean;
};

const Flow: React.FC<FlowProps> = ({
  onNodeDBLClick,
  onNodeClick,
  readOnly,
  onMount,
  center,
  hideToolbar,
  selectedDisabled,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const graphRef = useRef<Graph>();

  useEffect(() => {
    const container = containerRef.current;
    if (!container) {
      return;
    }
    const graph = new Graph({
      container,
      panning: {
        enabled: true,
        eventTypes: ['leftMouseDown'],
      },
      interacting: {
        nodeMovable: !readOnly,
      },
      grid: {
        size: GRID_SIZE,
        visible: true,
      },
      connecting: {
        allowEdge: false,
        allowLoop: false,
        allowNode: false,
        allowBlank: false,
        allowMulti: false,
        highlight: true,
        createEdge(this, { sourceCell }) {
          const data: Record<string, any> = sourceCell.getData();
          return this.createEdge({
            attrs: {
              line: {
                stroke: '#d9d9d9',
                strokeWidth: 2,
                targetMarker: {
                  name: 'block',
                  size: 8,
                  fill: '#13c2c2',
                  stroke: '#13c2c2',
                },
              },
              rect: {
                ref: 'label',
                fill: '#f5f5f5',
              },
            },
            connector: { name: 'smooth' },
            labels: [
              {
                attrs: {
                  label: {
                    text: RUN_MODE.find((item) => item.value === data?.runMode)?.label || '',
                  },
                },
              },
            ],
          });
        },
      },
      background: {
        color: '#f5f5f5',
      },
      mousewheel: {
        enabled: true,
        modifiers: ['ctrl', 'meta'],
        maxScale: 1.5,
        minScale: 0.5,
      },
      autoResize: true,
      highlighting: {
        // 连接桩可以被连接时在连接桩外围围渲染一个包围框
        magnetAvailable: {
          name: 'stroke',
          args: {
            attrs: {
              fill: '#fff',
              stroke: '#A4DEB1',
              strokeWidth: 8,
            },
          },
        },
        // 连接桩吸附连线时在连接桩外围围渲染一个包围框
        magnetAdsorbed: {
          name: 'stroke',
          args: {
            attrs: {
              fill: '#fff',
              stroke: '#31d0c6',
              strokeWidth: 8,
            },
          },
        },
      },
    });
    const selection = new Selection({
      enabled: !selectedDisabled,
      multiple: !readOnly,
    });
    const keyboard = new Keyboard({
      enabled: !readOnly,
    });

    // 使用插件
    graph.use(keyboard);
    graph.use(selection);

    // 删除节点
    graph.bindKey('backspace', () => {
      const cells = graph?.getSelectedCells() || [];
      cells.forEach((cell) => {
        if (cell.id === 'start') return;
        graph.removeCell(cell);
      });
    });

    graph.on('node:dblclick', ({ node }) => {
      if (node.id !== 'start') {
        onNodeDBLClick?.(node);
      }
    });

    graph.on('node:click', ({ node }) => {
      onNodeClick?.(node);
    });

    // 节点数据变化
    // graph.on('node:change:data', ({ node }) => {
    // });

    onMount?.(graph);
    graphRef.current = graph;

    return () => graph.dispose();
  }, []);

  useEffect(() => {
    graphRef.current?.fromJSON(initialData);
    if (center) graphRef.current?.centerContent();

    if (readOnly) {
      setTimeout(() => {
        const node = graphRef.current?.getNodes()[1];
        graphRef.current?.select(node || []);
      }, 500);
    }
  }, []);

  return (
    <div className={cx('flow-editor')}>
      {!hideToolbar && (
        <FlowToolbar
          onClick={(key) => {
            if (key === 'center') {
              graphRef.current?.centerContent();
            } else if (key === 'zoomIn') {
              graphRef.current?.zoom(0.2);
            } else if (key === 'zoomOut') {
              graphRef.current?.zoom(-0.2);
            }
          }}
        />
      )}

      <div className={cx('container')} ref={containerRef} />
      <X6ReactPortalProvider />
    </div>
  );
};

export default memo(Flow);
