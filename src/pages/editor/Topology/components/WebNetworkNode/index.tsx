import Icon from '@/components/Icon';
import { Node } from '@antv/x6';
import { Badge, Typography } from 'antd';
import classNames from 'classnames/bind';
import { FC } from 'react';
import styles from './index.less';

const cx = classNames.bind(styles);

const { Paragraph } = Typography;

export const DeviceTypeMap = {
  application: '应用',
  'load-balancer': '负载均衡设备',
  firewall: '防火墙',
  'network-device': '网络设备',
  host: '主机',
  middleware: '中间件',
  database: '数据库',
  storage: '存储',
  switch: '交换机',
  'network-server': '未知类型',
};

const WebNetworkNode: FC<{
  node: Node<Node.Properties>;
}> = ({ node }) => {
  const data = node?.getData() || {};

  // 获取设备类型
  const deviceType = data?.hostDeviceIcon || data?.valuesByTag?.at(0) || 'network-server';

  return (
    // <Popover
    //   content={<TopologyDetailPanel nodeData={data} />}
    //   trigger="click"
    //   placement="right"
    //   // getPopupContainer={(target) => target.parentElement || document.body}
    // >
    <div className={styles.node}>
      <Badge count={data?.problemCount || 0} offset={[8, 4]}>
        <Icon
          type={`icon-${deviceType}`}
          className={cx('icon', {
            gray: data?.status !== '0',
          })}
        />
      </Badge>
      <Paragraph
        className={styles.text}
        ellipsis={{
          rows: 2,
        }}
        type={data?.problem ? 'warning' : 'secondary'}
      >
        {data?.name}
      </Paragraph>
    </div>
    // </Popover>
  );
};

export default WebNetworkNode;
