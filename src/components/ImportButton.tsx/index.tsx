import { zabbixPost } from '@/services/zabbix';
import { UploadOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { Button, message, Upload, UploadProps } from 'antd';

const ImportButton: React.FC<{
  params: Record<string, any>;
}> = ({ params }) => {
  const { loading, run } = useRequest(zabbixPost, {
    manual: true, // 手动触发请求
    onSuccess: (res) => {
      if (res.code === 200) {
        message.success('上传并调用接口成功');
      }
    },
    formatResult: (res) => res,
  });
  const handleUpload: UploadProps['onChange'] = async ({ file }) => {
    try {
      if (file.status === 'done') {
        run({
          method: 'configuration.import',
          source: await file.originFileObj?.text(),
          ...params,
        });
      }
    } catch (error) {
      message.error('上传并调用接口出错');
    }
  };

  return (
    <Upload onChange={handleUpload} showUploadList={false}>
      <Button icon={<UploadOutlined />} type="primary" loading={loading}>
        导入
      </Button>
    </Upload>
  );
};

export default ImportButton;
