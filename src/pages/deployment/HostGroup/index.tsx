import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import { hostGroupDeleteByIds, hostGroupPage } from '@/services/http/hostGroup';
import { queryRkPagingTable, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
// import { delGroupUsingDELETE, searchGroupUsingGET } from '@/services/http/groupController';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import React, { Key, useRef, useState } from 'react';

const HostGroup: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.HostGroupPageVO[]>([]);

  const { run: deleteRecord } = useRequest((ids) => hostGroupDeleteByIds({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  const handleDelete = async (rows: API.HostGroupPageVO[]) => {
    const ids: Key[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.name!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除主机组“${names.join(',')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.HostGroupPageVO>[] = [
    {
      title: '组名',
      dataIndex: 'name',
      initialValue: queryParams.get('name'),
      render: (dom, record) => {
        return (
          <a
            onClick={() => {
              history.push(`/deployment/host-group/edit/${record.id}`);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      initialValue: queryParams.get('description'),
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <>
            <a key="del" onClick={() => handleDelete([record])}>
              删除
            </a>
          </>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.HostGroupPageVO>
        {...defaultTableConfig}
        actionRef={tableRef}
        search={SearchOptionRender}
        onSubmit={syncToUrl}
        columns={columns}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        headerTitle="主机组列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                history.push({
                  pathname: `/deployment/host-group/add`,
                });
              }}
            >
              新建主机组
            </Button>,
          ],
        }}
        request={async (params) => queryRkPagingTable(params, hostGroupPage)}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
});

export default HostGroup;
