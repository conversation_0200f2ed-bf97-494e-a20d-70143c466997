import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { STATUS } from '@/enums';
import { option2enum, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { useLocation } from '@umijs/max';
import { message, Modal, Space, Switch, Tag } from 'antd';

// 模拟数据
const mockData = [
  {
    id: '1',
    name: 'CPU使用率',
    key: 'cpu_usage',
    unit: '%',
    updateInterval: '1m',
    historyRecord: '90d',
    trendStorage: '365d',
    tags: ['系统', '性能'],
    thresholdRules: '2',
    status: '0',
  },
  {
    id: '2',
    name: '内存使用率',
    key: 'memory_usage',
    unit: '%',
    updateInterval: '1m',
    historyRecord: '90d',
    trendStorage: '365d',
    tags: ['系统', '性能'],
    thresholdRules: '3',
    status: '0',
  },
  {
    id: '3',
    name: '磁盘使用率',
    key: 'disk_usage',
    unit: '%',
    updateInterval: '5m',
    historyRecord: '90d',
    trendStorage: '365d',
    tags: ['系统', '存储'],
    thresholdRules: '2',
    status: '0',
  },
  {
    id: '4',
    name: '网络流入速率',
    key: 'net_in_rate',
    unit: 'Kbps',
    updateInterval: '1m',
    historyRecord: '90d',
    trendStorage: '365d',
    tags: ['网络'],
    thresholdRules: '1',
    status: '0',
  },
  {
    id: '5',
    name: '网络流出速率',
    key: 'net_out_rate',
    unit: 'Kbps',
    updateInterval: '1m',
    historyRecord: '90d',
    trendStorage: '365d',
    tags: ['网络'],
    thresholdRules: '1',
    status: '0',
  },
  {
    id: '6',
    name: '进程数',
    key: 'process_count',
    unit: '',
    updateInterval: '5m',
    historyRecord: '90d',
    trendStorage: '365d',
    tags: ['系统'],
    thresholdRules: '1',
    status: '1',
  },
  {
    id: '7',
    name: 'TCP连接数',
    key: 'tcp_connections',
    unit: '',
    updateInterval: '5m',
    historyRecord: '90d',
    trendStorage: '365d',
    tags: ['网络'],
    thresholdRules: '1',
    status: '0',
  },
  {
    id: '8',
    name: '系统负载',
    key: 'system_load',
    unit: '',
    updateInterval: '1m',
    historyRecord: '90d',
    trendStorage: '365d',
    tags: ['系统', '性能'],
    thresholdRules: '2',
    status: '0',
  },
];

// 模拟标签数据
const tagOptions = [
  { label: '系统', value: '系统' },
  { label: '性能', value: '性能' },
  { label: '网络', value: '网络' },
  { label: '存储', value: '存储' },
  { label: '安全', value: '安全' },
];

const MetricsConfig: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  // const { id } = useParams<{ id: string }>();

  const { search } = useLocation();
  const params = new URLSearchParams(search);
  const name = params.get('name') || '';

  // 模拟删除操作
  const handleDelete = async (rows: any[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id);
      names.push(item.name);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除指标"${names.join('、')}"吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        // 实际项目中应该调用API删除数据
        message.success('删除成功');
        tableRef.current?.reloadAndRest?.();
      },
    });
  };

  const updateStatus = async () => {
    message.success('状态更新成功');
    tableRef.current?.reloadAndRest?.();
  };

  const viewThreshold = (record: any) => {
    // 跳转到阈值设置页面
    window.location.href = `/monitor-config/host/metrics-config/threshold/${
      record.id
    }?metricName=${encodeURIComponent(record.name)}`;
  };

  // 查看指标配置详情
  const viewMetricConfig = (record: any) => {
    const { id } = record;
    window.location.href = `/monitor-config/host/metrics-config/view/${id}`;
  };

  // 表格列定义
  const columns: ProColumns<any>[] = [
    {
      title: '监控指标名称',
      dataIndex: 'name',
      width: 150,
      render: (text, record) => <a onClick={() => viewMetricConfig(record)}>{text}</a>,
    },
    {
      title: '键值',
      dataIndex: 'key',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      width: 80,
      hideInSearch: true,
    },
    {
      title: '更新间隔',
      dataIndex: 'updateInterval',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '历史纪录',
      dataIndex: 'historyRecord',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '趋势存储',
      dataIndex: 'trendStorage',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '标签',
      dataIndex: 'tags',
      width: 150,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        options: tagOptions,
      },
      render: (_, record) => (
        <>
          {record.tags.map((tag: string) => (
            <Tag key={tag}>{tag}</Tag>
          ))}
        </>
      ),
    },
    {
      title: '阈值规则',
      dataIndex: 'thresholdRules',
      width: 240,
      hideInSearch: true,
      render: (text) => `${text}条`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueType: 'select',
      valueEnum: option2enum(STATUS),
      render: (_, record) => (
        <Switch
          checked={record.status === '0'}
          onChange={() => {
            updateStatus();
          }}
        />
      ),
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space>
            <a key="view" onClick={() => viewThreshold(record)}>
              查看阈值
            </a>
            <a key="del" onClick={() => handleDelete([record])}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer header={{ title: false }}>
      <ProTable<any>
        {...defaultTableConfig}
        search={{ ...SearchOptionRender, labelWidth: 100 }}
        onSubmit={syncToUrl}
        rowKey="id"
        actionRef={tableRef}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columns={columns}
        headerTitle={`${name} 指标配置`}
        request={async (params) => {
          const { name, tags, status } = params;

          let filteredData = [...mockData];

          if (name) {
            filteredData = filteredData.filter((item) =>
              item.name.toLowerCase().includes(name.toLowerCase()),
            );
          }

          if (tags && tags.length > 0) {
            filteredData = filteredData.filter((item) =>
              tags.some((tag: string) => item.tags.includes(tag)),
            );
          }

          if (status) {
            filteredData = filteredData.filter((item) => item.status === status);
          }

          return {
            data: filteredData,
            success: true,
            total: filteredData.length,
          };
        }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default MetricsConfig;
