// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 批量保存 POST /api/v1/rump/monitoring-object/batch/create */
export async function batchCreate(
  body: API.MonitorObjectBatchCreateRequest[],
  options?: { [key: string]: any },
) {
  return request<API.ResultVoid>('/api/v1/rump/monitoring-object/batch/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 模板下载 GET /api/v1/rump/monitoring-object/download/template/${param0} */
export async function getMonitorExcelTemplate(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMonitorExcelTemplateParams,
  options?: { [key: string]: any },
) {
  const { subTypeId: param0, ...queryParams } = params;
  return request<any>(`/api/v1/rump/monitoring-object/download/template/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** excel解析 POST /api/v1/rump/monitoring-object/excel/parse */
export async function monitoringObjectExcelParse(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.monitoringObjectExcelParseParams,
  body: {},
  options?: { [key: string]: any },
) {
  return request<API.ResultListMonitorObjectExcelVO>('/api/v1/rump/monitoring-object/excel/parse', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/v1/rump/monitoring-object/group/all */
export async function getAllMonitoringObjectGroup(options?: { [key: string]: any }) {
  return request<API.ResultListMonitoringObjectGroupVO>(
    '/api/v1/rump/monitoring-object/group/all',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 PUT /api/v1/rump/monitoring-object/icon/group/${param0} */
export async function updateGroupIcon(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateGroupIconParams,
  body: API.MonitoringIconRequest,
  options?: { [key: string]: any },
) {
  const { groupId: param0, ...queryParams } = params;
  return request<any>(`/api/v1/rump/monitoring-object/icon/group/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /api/v1/rump/monitoring-object/icon/object/${param0} */
export async function updateObjectIcon(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateObjectIconParams,
  body: API.MonitoringIconRequest,
  options?: { [key: string]: any },
) {
  const { objectId: param0, ...queryParams } = params;
  return request<any>(`/api/v1/rump/monitoring-object/icon/object/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 获取指定监控对象子类型 GET /api/v1/rump/monitoring-object/type/${param0} */
export async function getMonitoringObjectType(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMonitoringObjectTypeParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultMonitorObjectTypeVO>(`/api/v1/rump/monitoring-object/type/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取所有监控对象类型 GET /api/v1/rump/monitoring-object/type/all */
export async function getAllMonitoringObjectType(options?: { [key: string]: any }) {
  return request<API.ResultListMonitorObjectTypeVO>('/api/v1/rump/monitoring-object/type/all', {
    method: 'GET',
    ...(options || {}),
  });
}
