import { STATUS } from '@/enums';
import { createPanoramicGroup, updatePanoramicGroup } from '@/services/http/panoramic';
import { zabbix, zabbixList } from '@/services/zabbix';
import { option2enum, queryOptions, queryPagingTable } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import {
  DrawerForm,
  DrawerFormProps,
  EditableProTable,
  ProColumns,
  ProFormDigit,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Button, message, Space, Tabs, TabsProps } from 'antd';
import React, { Key, useEffect, useRef, useState } from 'react';
import styles from './index.less';

const columns: ProColumns<RK_API.Host>[] = [
  {
    title: '模版',
    dataIndex: 'templateids',
    valueType: 'select',
    fieldProps: {
      mode: 'multiple',
      showSearch: true,
    },
    hideInTable: true,
    request: async () => {
      const res = await queryOptions(
        {
          method: 'template.get',
          sortfield: 'name',
        },
        zabbix,
      );
      return res?.map((item: RK_API.Template) => ({
        label: item.name,
        value: item.templateid,
      }));
    },
  },
  {
    title: '名称',
    dataIndex: 'name',
    width: 150,
    ellipsis: true,
    fixed: 'left',
    render: (dom, record) => {
      return (
        <a
          onClick={() => {
            history.push(`/monitor-config/host/details/${record.hostid}`);
          }}
        >
          {record.name}
        </a>
      );
    },
  },

  {
    title: '状态',
    width: 100,
    dataIndex: 'status',
    valueEnum: option2enum(STATUS),
    hideInSearch: true,
  },

  {
    title: '模板',
    dataIndex: 'parentTemplates',
    width: 200,
    hideInSearch: true,
    render(dom, record) {
      const { parentTemplates = [] } = record;
      if (!parentTemplates.length) return '';
      return parentTemplates.map((item: RK_API.TemplateGetDTO, index: number) => (
        <React.Fragment key={item?.templateid}>
          {index > 0 && <a>,</a>}
          <Button
            type="link"
            onClick={() => {
              history.push(`/monitor-config/template/edit/${item?.templateid}`);
            }}
          >
            {item?.name}
          </Button>
        </React.Fragment>
      ));
    },
  },
];

const columns1: ProColumns<Record<string, any>>[] = [
  {
    title: '名称',
    dataIndex: 'name',
    ellipsis: true,
    editable: false,
    render: (dom, record) => {
      return (
        <a
          onClick={() => {
            history.push(`/monitor-config/host/details/${record.hostid}`);
          }}
        >
          {record.name}
        </a>
      );
    },
  },
  {
    title: '排序',
    dataIndex: 'sort',
    valueType: 'digit',
    hideInSearch: true,
    width: 80,
    tooltip: '数值越高，排序越靠前',
    fieldProps: {
      min: 0,
      step: 1,
      precision: 0,
    },
  },
];

const GroupDrawerForm: React.FC<
  DrawerFormProps & {
    reload: () => void;
  }
> = ({ initialValues, reload }) => {
  const isEdit = initialValues;
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [sortHostList = [], setSortHostLis] = useState<Record<string, any>[]>([]);
  const sortHostListRef = useRef<Record<string, any>[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => initialValues?.hostIds);

  // 解决可编辑表格复用数据问题
  useEffect(() => {
    const arr = sortHostList?.map((item) => ({ ...item, randomId: Math.random() }));
    const ids = arr.map((item) => item.randomId);
    sortHostListRef.current = arr;
    setEditableRowKeys(ids);
  }, [sortHostList.length]);

  // 获取主机列表
  const { run: getHosts } = useRequest((params) => zabbixList(params), {
    manual: true,
  });

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '配置监控对象',
      children: (
        <ProTable<RK_API.Host>
          {...defaultTableConfig}
          rowKey="hostid"
          className="inner-table"
          size="small"
          headerTitle="监控对象列表"
          search={{
            filterType: 'light',
          }}
          options={false}
          columns={columns}
          rowSelection={{
            selectedRowKeys,
            onChange: (selectedRowKeys, selectedRows, info) => {
              if (info.type === 'all') {
                const list = selectedRows
                  .filter((item) => Boolean(item))
                  .filter((item) => !sortHostList.some((host) => host?.hostid === item?.hostid));
                if (list.length === 0) setSortHostLis([]);
                setSortHostLis((prev) => [
                  ...prev,
                  ...list.map((item) => ({
                    hostid: item.hostid,
                    name: item.name,
                    sort: 0,
                  })),
                ]);
              }
              setSelectedRowKeys(selectedRowKeys);
            },
            onSelect: (record, selected) => {
              const { hostid = '', name } = record;
              if (selected) {
                setSortHostLis((prev) => [...prev, { hostid, name, sort: 0 }]);
              } else {
                setSortHostLis((prev) => prev.filter((item) => item.hostid !== hostid));
              }
            },

            preserveSelectedRowKeys: true,
          }}
          tableAlertRender={({ selectedRowKeys, onCleanSelected }) => {
            return (
              <Space size={24}>
                <span>
                  已选 {selectedRowKeys.length} 项
                  <a style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>
                    取消选择
                  </a>
                </span>
              </Space>
            );
          }}
          request={async (params) => {
            const { name, groupids, templateids, parentTemplateids } = params;

            const res = await queryPagingTable<RK_API.Host>(
              {
                search: {
                  name,
                },
                templateids: templateids?.length ? templateids : null,
                parentTemplateids: parentTemplateids?.length ? parentTemplateids : null,
                sortfield: 'name',
                method: 'host.get',
                output: ['hostid', 'name'],
              },
              zabbixList,
            );
            // 获取所有host id
            const hostIds = res.data?.map((item: RK_API.Item) => item.hostid) || [];
            const { current = 1, pageSize = 10 } = params;
            const index = (current - 1) * pageSize;

            const hostList = await getHosts({
              search: {
                name,
              },
              groupids: groupids?.length ? groupids : null,
              templateids: templateids?.length ? templateids : null,
              parentTemplateids: parentTemplateids?.length ? parentTemplateids : null,
              selectParentTemplates: 'extend',
              sortfield: 'name',
              hostids: hostIds.splice(index, pageSize),
              method: 'host.get',
            });
            return {
              ...res,
              data: hostList,
            };
          }}
        />
      ),
    },
    {
      key: '2',
      label: '监控对象排序',
      children: (
        <EditableProTable
          {...defaultTableConfig}
          rowKey="randomId"
          className="inner-table"
          size="small"
          search={false}
          options={false}
          columns={columns1}
          recordCreatorProps={false}
          editable={{
            type: 'multiple',
            editableKeys,
            onValuesChange: (_, recordList) => {
              setSortHostLis(recordList);
            },
            onChange: setEditableRowKeys,
          }}
          params={{ editableKeys }}
          request={async () => {
            return {
              success: true,
              data: sortHostListRef.current,
            };
          }}
        />
      ),
    },
  ];

  return (
    <DrawerForm<API.CUPanoramicGroupRequest & API.updatePanoramicGroupParams>
      className={styles.drawer}
      title={isEdit ? '编辑全景组' : '新建全景组'}
      trigger={
        isEdit ? (
          <Button type="link">{initialValues.name}</Button>
        ) : (
          <Button key="add" type="primary" icon={<PlusOutlined />}>
            新建全景组
          </Button>
        )
      }
      onFinish={async (value) => {
        const { id } = value;
        const params: API.CUPanoramicGroupRequest = {
          ...value,
          hosts: sortHostList?.map((item) => ({
            hostId: item.hostid,
            name: item.name,
            sort: item.sort,
          })),
        };
        const msg = isEdit
          ? await updatePanoramicGroup({ id }, params)
          : await createPanoramicGroup(params);
        const success = msg.data;
        if (success) {
          message.success('操作成功!');
          reload();
        }
        return success;
      }}
      autoFocusFirstInput
      initialValues={initialValues}
      drawerProps={{
        destroyOnClose: true,
      }}
      onOpenChange={(visible) => {
        if (visible) {
          const arr =
            initialValues?.hosts?.map((item: Record<string, any>) => ({
              ...item,
              randomId: Math.random(),
            })) || [];
          const ids = arr.map((item: Record<string, any>) => item.randomId);
          sortHostListRef.current = arr;
          setEditableRowKeys(ids);
          setSelectedRowKeys(initialValues?.hostIds || []);
          setSortHostLis(initialValues?.hosts || []);
        }
      }}
    >
      {/* 不需要展示，只是为了form传值 */}
      <div className="rk-none">
        <ProFormText name="id" />
        <ProFormText name="top" />
      </div>
      <ProFormText name="name" width="lg" label="组名" rules={[requiredRule]} />
      <ProFormDigit
        tooltip="数值越高，排序越靠前"
        initialValue={0}
        name="sort"
        width="lg"
        label="层级"
        rules={[requiredRule]}
        fieldProps={{
          precision: 0,
        }}
      />

      <Tabs defaultActiveKey="1" items={items} />
    </DrawerForm>
  );
};

export default GroupDrawerForm;
