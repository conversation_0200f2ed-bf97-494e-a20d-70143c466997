import { panoramicGroupProblemPage } from '@/services/http/panoramic';
import { PageContainer } from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import DataCard from './components/DataCard';
import DataTable from './components/DataTable';
export const COL_SPAN = { xs: 24, sm: 12, md: 8, lg: 6, xl: 6, xxl: 4 };

const AnalysisPage = () => {
  const { id } = useParams();
  // 获取全景组
  const { data: panoramicGroupData } = useRequest(() =>
    panoramicGroupProblemPage({
      page: {
        page: 1,
        size: 100,
      },
      filter: {
        id,
      },
    }),
  );

  return (
    <PageContainer title={false}>
      <DataTable data={panoramicGroupData?.data?.at(0)} />
      <DataCard data={panoramicGroupData?.data?.at(0)} />
    </PageContainer>
  );
};

export default AnalysisPage;
