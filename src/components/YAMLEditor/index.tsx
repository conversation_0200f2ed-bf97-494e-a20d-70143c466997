import classNames from 'classnames/bind';
import { editor, Uri } from 'monaco-editor';
import { setDiagnosticsOptions } from 'monaco-yaml';
import React from 'react';
import MonacoEditor, { MonacoEditorProps } from 'react-monaco-editor';
import styles from './index.less';
const cx = classNames.bind(styles);

// The uri is used for the schema file match.
const modelUri = Uri.parse('a://b/foo.yaml');

setDiagnosticsOptions({
  enableSchemaRequest: true,
  hover: true,
  completion: true,
  validate: true,
  format: true,
});

editor.create(document.createElement('editor'), {
  language: 'yaml',
  model: editor.createModel('p1: \n', 'yaml', modelUri),
});

type YAMLEditorProps = MonacoEditorProps & {
  onError?: (editor: any) => void;
  readOnly?: boolean;
};

const YAMLEditor: React.FC<YAMLEditorProps> = (props) => {
  const { className, onError, readOnly } = props;
  return (
    <MonacoEditor
      height="250px"
      language="yaml"
      className={cx('rk-editor', className)}
      options={{
        wordWrap: 'on',
        readOnly: readOnly,
        cursorStyle: readOnly ? 'block' : 'line',
        minimap: {
          enabled: false,
        },
        scrollbar: {
          // horizontal: 'hidden',
          // vertical: 'hidden',
          alwaysConsumeMouseWheel: false,
        },
        lineNumbers: 'off',
        scrollBeyondLastLine: false,
        lineNumbersMinChars: 0,
        lineDecorationsWidth: 0, // 更改行号的宽度
        glyphMargin: false, // 关闭行号区域左侧的图标区
        automaticLayout: true, //以启用自适应布局
      }}
      editorDidMount={() => {
        onError?.(editor);
      }}
      {...props}
    />
  );
};

export default YAMLEditor;
