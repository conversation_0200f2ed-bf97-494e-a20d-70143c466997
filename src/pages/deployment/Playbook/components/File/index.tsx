import IconFont from '@/components/Icon';
import { DrawerForm, ProDescriptions } from '@ant-design/pro-components';
import { Typography } from 'antd';
import classNames from 'classnames/bind';
import styles from './index.less';
const cx = classNames.bind(styles);

const File: React.FC<
  API.AtomicTaskPathVO & {
    onClick?: () => void;
    isRoot: boolean;
  }
> = ({ name, isDirectory, onClick, isAtomicTask, isRoot, content }) => {
  const fileIcon = isDirectory ? (
    <IconFont type="icon-folder" className={cx('icon', { gray: isRoot && !isAtomicTask })} />
  ) : (
    <DrawerForm
      width={760}
      trigger={
        <IconFont type="icon-file" className={cx('icon', { gray: isRoot && !isAtomicTask })} />
      }
      className={cx('drawer')}
      drawerProps={{
        destroyOnClose: true,
        footer: false,
      }}
    >
      <ProDescriptions>
        <ProDescriptions.Item valueType="code" fieldProps={{}}>
          {content}
        </ProDescriptions.Item>
      </ProDescriptions>
    </DrawerForm>
  );
  return (
    <div className={cx('file')} onClick={onClick}>
      {fileIcon}

      <Typography.Text type="secondary">{name}</Typography.Text>
    </div>
  );
};

export default File;
