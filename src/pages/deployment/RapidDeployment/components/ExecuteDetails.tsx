import TextLoader from '@/components/TextLoader';
import { EXECUTE_STATUS, HOST_USABILITY } from '@/enums';
import { taskModuleOrchestrationResult } from '@/services/http/taskModuleOrchestration';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  PageContainer,
  ProCard,
  ProColumns,
  ProDescriptions,
  ProFormField,
  ProTable,
} from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import { memo, useMemo, useRef } from 'react';

const columns: ProColumns<API.HostPageVO>[] = [
  {
    title: '主机名',
    dataIndex: 'host',
  },
  {
    title: '状态',
    dataIndex: 'usability',
    valueEnum: option2enum(HOST_USABILITY),
  },
];

const ExecuteDetails = () => {
  const { id = '' } = useParams();
  const logEndRef = useRef(false);

  // 获取 log
  const { data: result, cancel } = useRequest(() => taskModuleOrchestrationResult({ id }), {
    pollingInterval: 2000,
    onSuccess: (res) => {
      if (res?.status === 'END') {
        cancel();
        logEndRef.current = true;
      }
    },
  });

  const tabItems = useMemo(
    () =>
      result?.taskModuleExecuteList?.map((item) => ({
        label: item?.taskModule?.name || '',
        key: item.id!,
        children: (
          <div>
            <ProFormField
              style={{ marginBottom: 0 }}
              valueType="jsonCode"
              text={
                <>
                  {item?.taskResult?.map((item) => item.log).join('\n')}
                  {!logEndRef.current && <TextLoader />}
                </>
              }
              mode="read"
              // @ts-ignore
              width="auto"
            />
          </div>
        ),
      })),
    [result, logEndRef.current],
  );

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProCard
        bodyStyle={{
          padding: '24px 24px',
        }}
        direction="column"
      >
        <ProDescriptions column={4}>
          <ProDescriptions.Item label="执行时间">{result?.executeTime}</ProDescriptions.Item>
          <ProDescriptions.Item label="执行状态" valueEnum={option2enum(EXECUTE_STATUS)}>
            {result?.status}
          </ProDescriptions.Item>
        </ProDescriptions>
        <ProTable<API.HostPageVO>
          {...defaultTableConfig}
          pagination={{
            disabled: false,
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
          className="inner-table"
          search={false}
          options={false}
          columns={columns}
          dataSource={result?.hostList || []}
          headerTitle="目标主机列表"
        />
        <ProCard
          title="监控模块执行日志"
          headStyle={{
            padding: 0,
          }}
          tabs={{
            items: tabItems,
          }}
        />
      </ProCard>
    </PageContainer>
  );
};

export default memo(ExecuteDetails);
