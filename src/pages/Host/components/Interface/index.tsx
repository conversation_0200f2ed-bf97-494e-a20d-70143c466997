import { CONNECTION_TYPE, INTERFACE_TYPE } from '@/enums';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { CheckCircleFilled } from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Space, theme } from 'antd';
import { produce } from 'immer';
import React, { useRef, useState } from 'react';
import Setter from './Setter';
const { useToken } = theme;

const Interface: React.FC<{
  value?: RK_API.Interface[];
  onChange?: (val: RK_API.Interface[]) => void;
}> = ({ value, onChange }) => {
  const [modalVisit, setModalVisit] = useState(false);
  const interfaceRowData = useRef<RK_API.Interface>();
  const { token } = useToken();
  const columns: ProColumns<RK_API.Interface>[] = [
    {
      title: '类型',
      width: 100,
      dataIndex: 'type',
      valueEnum: option2enum(INTERFACE_TYPE),
    },

    {
      title: 'IP',
      width: 100,
      dataIndex: 'ip',
    },
    {
      title: 'DNS名称',
      width: 100,
      dataIndex: 'dns',
    },
    {
      title: '连接到',
      width: 100,
      dataIndex: 'useip',
      valueEnum: option2enum(CONNECTION_TYPE),
    },
    {
      title: '端口',
      dataIndex: 'port',
      width: 80,
    },
    {
      title: '默认接口',
      width: 80,
      dataIndex: 'main',

      render: (dom, entity) => {
        return entity.main ? (
          <CheckCircleFilled style={{ fontSize: '16px', color: token.colorPrimary }} />
        ) : (
          <>-</>
        );
      },
    },
    // {
    //   title: 'SNMP详情',
    //   dataIndex: 'details',
    //   children: [
    //     {
    //       title: '接口版本',
    //       dataIndex: ['details', 'version'],
    //       valueEnum: option2enum(SNMP_VERSION),
    //     },
    //     {
    //       title: 'SNMP团体字',
    //       dataIndex: ['details', 'community'],
    //     },
    //     {
    //       title: '上下文名称',
    //       dataIndex: ['details', 'contextname'],
    //     },
    //     {
    //       title: '安全名称',
    //       dataIndex: ['details', 'securityname'],
    //     },
    //     {
    //       title: '安全级别',
    //       dataIndex: ['details', 'securitylevel'],
    //       valueEnum: option2enum(SECURITY_LEVEL),
    //     },
    //     {
    //       title: '批量请求',
    //       dataIndex: ['details', 'bulk'],
    //       valueEnum: {
    //         0: '否',
    //         1: '是',
    //       },
    //     },
    //   ],
    // },
    {
      title: '操作',
      width: 200,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (_, record) => {
        const { key, type } = record;
        return (
          <Space>
            {record.main !== '1' && (
              <a
                key="main"
                onClick={() => {
                  const updatedValue = produce(value || [], (draft) => {
                    const sameType = draft.filter((item) => item.type === type);
                    sameType.forEach((item: any) => {
                      item.main = item.key === key ? '1' : '0';
                    });
                  });
                  onChange?.(updatedValue);
                }}
              >
                用作默认接口
              </a>
            )}

            <a
              key="edit"
              onClick={() => {
                interfaceRowData.current = record;
                setModalVisit(true);
              }}
            >
              编辑
            </a>
            <a
              key="del"
              onClick={() => {
                const updatedValue = produce(value || [], (draft) => {
                  return draft.filter((item) => item.key !== key);
                });
                onChange?.(updatedValue);
              }}
            >
              移除
            </a>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <ProTable<RK_API.Interface>
        {...defaultTableConfig}
        scroll={{ x: 'max-content' }}
        rowKey="key"
        className="inner-table"
        headerTitle="主机接口"
        search={false}
        dataSource={value}
        columns={columns}
        toolbar={{
          actions: [
            <Button
              key="primary"
              type="primary"
              onClick={() => {
                setModalVisit(true);
              }}
            >
              新建
            </Button>,
          ],
        }}
      />

      <Setter
        open={modalVisit}
        onOpenChange={setModalVisit}
        initialValues={interfaceRowData.current}
        onFinish={async (val) => {
          // 存在同类型
          const existSameType = value?.some((item) => item.type === val.type);
          const rowData = val as RK_API.Interface;
          rowData.main = existSameType ? '0' : '1';
          const updatedValue = produce(value || [], (draft) => {
            const index = draft.findIndex((item) => item.key === val.key);
            if (index === -1) {
              draft.push(rowData);
            } else {
              draft[index] = rowData;
            }
          });
          onChange?.(updatedValue);
        }}
      />
    </>
  );
};

export default Interface;
