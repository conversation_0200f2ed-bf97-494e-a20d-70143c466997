import { ALARM_LEVEL } from '@/enums';
import { arrayToBinary, binaryToArray, getRandomId } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  ModalFormProps,
  ProFormDependency,
  ProFormInstance,
  ProFormList,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@ant-design/pro-components';
import { Typography } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { isArray } from 'lodash';
import React, { useEffect, useRef } from 'react';

export const periodTooltips = (
  <div>
    <div>格式:d-d,hh:mm-hh:mm</div>
    <div>配置:周一至周日 00:00到24:00</div>
    <div>
      输入:<Typography.Text copyable>1-7,00:00-24:00</Typography.Text>
    </div>
  </div>
);
const MediaSetter: React.FC<
  ModalFormProps & {
    mediaTypeList: Record<string, any>[] & DefaultOptionType[];
  }
> = ({ open, onOpenChange, initialValues, onFinish, mediaTypeList }) => {
  const formRef = useRef<ProFormInstance>();
  const mediaType = useRef<string>();

  useEffect(() => {
    mediaType.current = mediaTypeList?.find(
      ({ mediatypeid }) => mediatypeid === initialValues?.mediatypeid,
    )?.type;
    if (initialValues?.mediatypeid) {
      formRef.current?.setFieldsValue({
        ...initialValues,
        sendto:
          mediaType.current === '0'
            ? initialValues.sendto?.map((item: string[]) => ({ email: item }))
            : initialValues.sendto,
      });
    }
  }, [initialValues, mediaTypeList]);

  return (
    <DrawerForm
      formRef={formRef}
      width={460}
      title="报警媒介"
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const { sendto, ...rest } = value;
        const newSendTo = isArray(sendto) ? sendto.map((item) => item?.email || item) : [sendto];
        onFinish?.({
          ...rest,
          sendto: newSendTo,
        });
        return true;
      }}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
    >
      {/* 不需要展示，只是为了form传值 */}
      <div className="rk-none">
        <ProFormText name="id" initialValue={getRandomId()} />
        <ProFormText name="mediaid" />
      </div>
      <ProFormSelect
        label="类型"
        name="mediatypeid"
        allowClear={false}
        rules={[requiredRule]}
        options={mediaTypeList}
        fieldProps={{
          fieldNames: {
            label: 'name',
            value: 'mediatypeid',
          },
          onChange: (val, option) => {
            mediaType.current = (option as Record<string, any>).type;
            formRef.current?.setFieldValue(
              'sendto',
              mediaType.current === '0' ? [{ email: '' }] : '',
            );
          },
        }}
      />
      <ProFormDependency name={['mediatypeid']}>
        {() => {
          return (
            // 电子邮件
            mediaType.current === '0' && (
              <ProFormList
                name="sendto"
                min={1}
                creatorButtonProps={{
                  creatorButtonText: '新建',
                }}
                copyIconProps={false}
              >
                <ProFormText
                  key="email"
                  width={390}
                  name="email"
                  label="收件人"
                  rules={[
                    requiredRule,
                    {
                      type: 'email',
                    },
                  ]}
                />
              </ProFormList>
            )
          );
        }}
      </ProFormDependency>
      <ProFormDependency name={['mediatypeid']}>
        {({ mediatypeid }) => {
          return (
            // 类型不是电子邮件
            mediatypeid &&
            mediaType.current !== '0' && (
              <ProFormText name="sendto" label="收件人" rules={[requiredRule]} />
            )
          );
        }}
      </ProFormDependency>

      <ProFormText
        name="period"
        tooltip={periodTooltips}
        label="时间窗口"
        rules={[requiredRule]}
        initialValue="1-7,00:00-24:00"
      />
      <ProFormSelect
        label="告警级别"
        name="severity"
        mode="multiple"
        initialValue={63}
        options={ALARM_LEVEL}
        rules={[requiredRule]}
        getValueFromEvent={(value = []) => arrayToBinary(value)}
        convertValue={(value) => binaryToArray(ALARM_LEVEL, value)}
      />
      <ProFormSwitch
        label="启用"
        name="active"
        initialValue="0"
        getValueFromEvent={(val) => (val ? '0' : '1')}
        getValueProps={(value) => ({ checked: value === '0' })}
      />
    </DrawerForm>
  );
};

export default MediaSetter;
