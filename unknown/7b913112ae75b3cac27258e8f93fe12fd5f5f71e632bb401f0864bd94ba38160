import RkLine from '@/components/Charts/Line';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import { zabbix, zabbixPost } from '@/services/zabbix';
import { convertDateRangeToTimestamps, queryPagingTable, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { ReloadOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import { Card, Empty, Space, TimeRangePickerProps, Typography } from 'antd';
import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import { useRef, useState } from 'react';
const { Text } = Typography;
type ValueType = 'value_min' | 'value_avg' | 'value_max';

const typeMap: Record<ValueType, string> = {
  value_min: '最小值',
  value_avg: '平均值',
  value_max: '最大值',
};

interface ChatProps extends RK_API.Trend {
  value: number;
  type: string;
}

dayjs.extend(isoWeek);

const recentHours = [
  dayjs().add(-1, 'h').format('YYYY-MM-DD HH:mm:ss'),
  dayjs().format('YYYY-MM-DD HH:mm:ss'),
];

const rangePresets: TimeRangePickerProps['presets'] = [
  {
    label: '最近 5 分钟',
    value: [dayjs().add(-5, 'm'), dayjs()],
  },
  {
    label: '最近 30 分钟',
    value: [dayjs().add(-5, 'm'), dayjs()],
  },
  {
    label: '最近 1 个小时',
    value: [dayjs().add(-1, 'h'), dayjs()],
  },
  {
    label: '昨天',
    value: [dayjs().add(-1, 'd').startOf('day'), dayjs().add(-1, 'd').endOf('day')],
  },
  {
    label: '今天',
    value: [dayjs().startOf('day'), dayjs().endOf('day')],
  },
  {
    label: '今天到目前为止',
    value: [dayjs().startOf('day'), dayjs()],
  },
  {
    label: '本周',
    value: [dayjs().startOf('isoWeek'), dayjs().endOf('isoWeek')],
  },
  {
    label: '本月',
    value: [dayjs().startOf('month'), dayjs().endOf('month')],
  },
  {
    label: '本月到目前为止',
    value: [dayjs().startOf('month'), dayjs()],
  },
  {
    label: '本年',
    value: [dayjs().startOf('year'), dayjs().endOf('year')],
  },
  {
    label: '今年到目前为止',
    value: [dayjs().startOf('year'), dayjs()],
  },
  { label: '近 2 天', value: [dayjs().add(-2, 'd'), dayjs()] },
  { label: '近 7 天', value: [dayjs().add(-7, 'd'), dayjs()] },
  { label: '近 14 天', value: [dayjs().add(-14, 'd'), dayjs()] },
  { label: '近 30 天', value: [dayjs().add(-30, 'd'), dayjs()] },
  { label: '近 3 个月', value: [dayjs().add(-3, 'months'), dayjs()] },
  { label: '近 6 个月', value: [dayjs().add(-6, 'months'), dayjs()] },
  { label: '近 1 年', value: [dayjs().add(-1, 'year'), dayjs()] },
  { label: '近 2 年', value: [dayjs().add(-2, 'year'), dayjs()] },
];

type TrendChartProp = RK_API.Trend & {
  value: number | string;
};

const TrendChart = withStorageToUrl(({ queryParams }) => {
  const { id = '' } = useParams();

  const tableRef = useRef<ActionType | undefined>();
  const [valueType, setValueType] = useState();

  const { data: itemInfo } = useRequest(
    () =>
      zabbixPost({
        method: 'item.get',
        selectHosts: 'extend',
        output: ['name', 'value_type'],
        itemids: [id],
        preservekeys: true,
      }),
    {
      onSuccess: (data) => {
        setValueType(data?.[id]?.value_type);
      },
    },
  );

  const columns: ProColumns<TrendChartProp>[] = [
    {
      dataIndex: 'dateRange',
      title: '时间段',
      valueType: 'dateTimeRange',
      hideInTable: true,
      colSize: 4,
      fieldProps: {
        style: {
          width: 400,
        },
        showTime: true,
        presets: rangePresets,
      },
      initialValue: queryParams.get('dateRange') ? queryParams.getAll('dateRange') : recentHours,
    },
    {
      title: '时间',
      dataIndex: 'clock',
      hideInSearch: true,
      valueType: 'dateTime',
      renderText: (text) => {
        if (!text) return text;
        return dayjs.unix(text);
      },
    },
    {
      dataIndex: 'value',
      title: '值',
      hideInSearch: true,
    },
    // {
    //   dataIndex: 'value_min',
    //   title: '最小值',
    //   hideInSearch: true,
    // },
    // {
    //   dataIndex: 'value_max',
    //   title: '最大值',
    //   hideInSearch: true,
    // },
    // {
    //   dataIndex: 'value_avg',
    //   title: '平均值',
    //   hideInSearch: true,
    // },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<TrendChartProp>
        {...defaultTableConfig}
        rowKey={(record) => record.itemid + record.clock}
        search={SearchOptionRender}
        form={{}}
        onSubmit={syncToUrl}
        actionRef={tableRef}
        columns={columns}
        headerTitle="历史数据"
        tableExtraRender={(props, dataSource) => {
          if (!['0', '3'].includes(valueType || '')) {
            return '';
          }
          if (!dataSource.length)
            return (
              <Card>
                <Empty />
              </Card>
            );
          // 判断是否为历史数据
          const isHistoryData = dataSource.some((item) => item.value);
          const arr = dataSource.map((item) => ({
            ...item,
            clock: dayjs.unix(Number(item.clock)).format('YYYY-MM-DD HH:mm:ss'),
            value: Number((parseFloat(item.value as string) as number).toFixed(2)),
          }));
          const values = dataSource.map((item) => Number(item.value)) || [];
          // 计算最大值、最小值和平均值
          const maxValue = Math.max(...values);
          const minValue = Math.min(...values);
          const sum = values.reduce((acc, cur) => acc + cur, 0);
          const averageValue = values.length > 0 ? sum / values.length : 0;
          if (isHistoryData) {
            return (
              <Card
                // @ts-ignore
                title={` ${itemInfo?.[id]?.hosts?.at(0)?.name} - ${itemInfo?.[id]?.name}`}
                // @ts-ignore
                loading={props.action?.loading}
                // @ts-ignore
                extra={<ReloadOutlined onClick={() => props?.action?.reload?.()} />}
              >
                <Space style={{ marginBlockEnd: 24 }}>
                  <Text>最大值：{maxValue.toFixed(2)}</Text>
                  <Text>最小值：{minValue.toFixed(2)}</Text>
                  <Text>平均值：{averageValue.toFixed(2)}</Text>
                </Space>
                <RkLine
                  height={500}
                  color="#38CCE1"
                  data={arr}
                  xField="clock"
                  yField="value"
                  slider={{
                    start: 0,
                    end: Math.min(dataSource.length, 60) / dataSource.length,
                  }}
                  smooth
                />
              </Card>
            );
          }
          const enums: ValueType[] = ['value_min', 'value_avg', 'value_max'];
          const convertedData: ChatProps[] = [];
          dataSource.forEach((item) => {
            enums.forEach((type) => {
              convertedData.push({
                ...item,
                clock: dayjs.unix(Number(item.clock)).format('YYYY-MM-DD HH:mm:ss'),
                value: Number((parseFloat(item[type]) as number).toFixed(2)),
                type: typeMap[type],
              });
            });
          });

          return (
            <Card
              // @ts-ignore

              title={` ${itemInfo?.[id]?.hosts?.at(0)?.name} - ${itemInfo?.[id]?.name}`}
              // @ts-ignore
              loading={props.action?.loading}
              // @ts-ignore
              extra={<ReloadOutlined onClick={() => props?.action?.reload?.()} />}
            >
              <RkLine
                height={500}
                color={['#A3D8FF', '#A6FF96', '#FF76CE']}
                data={convertedData}
                xField="clock"
                yField="value"
                seriesField="type"
                slider={{
                  start: 0,
                  end: Math.min(convertedData.length, 60) / convertedData.length,
                }}
                smooth
              />
            </Card>
          );
        }}
        tableRender={(props, defaultDom) => {
          if (['0', '3'].includes(valueType || '')) {
            return '';
          }
          return defaultDom;
        }}
        params={{ valueType }}
        request={async (params) => {
          const { dateRange, valueType } = params;
          const { time_from, time_till } = convertDateRangeToTimestamps(dateRange);

          // 是否是 24 小时内的数据
          if (time_from && dayjs.unix(time_from as number).isAfter(dayjs().add(-1, 'day'))) {
            return await queryPagingTable(
              {
                itemids: [id],
                time_from,
                time_till,
                method: 'history.get',
                history: valueType,
              },
              zabbix,
            );
          }

          const res = await queryPagingTable(
            { itemids: [id], time_from, time_till, method: 'trend.get' },
            zabbix,
          );

          return res;
        }}
      />
    </PageContainer>
  );
});

export default TrendChart;
