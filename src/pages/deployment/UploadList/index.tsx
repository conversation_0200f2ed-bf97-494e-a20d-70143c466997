import { UPLOAD_PLAYBOOK_STATUS } from '@/enums';
import { atomicTaskUploadPage } from '@/services/http/atomicTask';
import { option2enum, queryRkPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef } from 'react';

// 表格
const columns: ProColumns<any>[] = [
  {
    title: '文件名',
    dataIndex: 'fileName',
    width: 200,
  },
  {
    title: '上传时间',
    dataIndex: 'uploadTime',
    width: 150,
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    width: 150,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 150,
    valueEnum: option2enum(UPLOAD_PLAYBOOK_STATUS),
  },
];
const UploadList: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<any, AntTableParams>
        {...defaultTableConfig}
        options={false}
        search={false}
        actionRef={tableRef}
        columns={columns}
        headerTitle="上传列表"
        polling={(dataSource) =>
          dataSource.find((item) => item.unzipStatus === '解压中') ? 5000 : 0
        }
        request={async (params) => queryRkPagingTable({ ...params }, atomicTaskUploadPage)}
      />
    </PageContainer>
  );
};

export default UploadList;
