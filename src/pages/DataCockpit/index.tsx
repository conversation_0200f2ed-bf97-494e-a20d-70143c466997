import RkArea from '@/components/Charts/Area';
import RkBar from '@/components/Charts/Bar';
import RKColumn from '@/components/Charts/Column';
import RkLine from '@/components/Charts/Line';
import RkLiquid from '@/components/Charts/Liquid';
import RkPie from '@/components/Charts/Pie';
import RkRadar from '@/components/Charts/Radar';
import RkRose from '@/components/Charts/Rose';
import RKCard from '@/components/RKCard';
import RKStatisticCard from '@/components/RKStatisticCard';
import { colors1, colors4, colors7, radarColors } from '@/utils/colors';
import { Col, Row } from 'antd';
import classNames from 'classnames/bind';
import styles from './index.less';
const cx = classNames.bind(styles);

const DataCockpit = () => {
  return (
    <div className={cx('container')}>
      <div className={cx('header')}>监控数据大屏</div>
      <div className={cx('content')}>
        <Row gutter={[24, 24]}>
          <Col span={6}>
            <RKCard title="环形图">
              <RkPie
                height={220}
                radius={0.7}
                innerRadius={0.7}
                showPercentValue
                innerTitle="健康率"
                tooltip={false}
                label={{
                  type: 'spider',
                  content: '{name}：{value}\n比例：{percentage}',
                }}
                statisticColor="white"
                innerContent={20 + '%'}
                data={[
                  {
                    type: '告警',
                    value: 20,
                  },
                  {
                    type: '未告警',
                    value: 80,
                  },
                ]}
                theme="custom-theme-dark"
              />
            </RKCard>
          </Col>
          <Col span={6}>
            <RKCard title="饼图">
              <RkPie
                color={colors1}
                height={220}
                radius={0.7}
                label={{
                  type: 'spider',
                  content: '{name}：{value}\n比例：{percentage}',
                }}
                statisticColor="white"
                data={[
                  {
                    type: '未分类',
                    value: 10,
                  },
                  {
                    type: '信息',
                    value: 20,
                  },
                  {
                    type: '警告',
                    value: 30,
                  },
                  {
                    type: '一般严重',
                    value: 40,
                  },
                  {
                    type: '严重',
                    value: 10,
                  },
                  {
                    type: '灾难',
                    value: 20,
                  },
                ]}
                theme="custom-theme-dark"
              />
            </RKCard>
          </Col>
          <Col span={6}>
            <RKCard title="水波图">
              <RkLiquid
                radius={0.8}
                height={220}
                percent={0.6}
                innerTitle="告警总数"
                innerContent="20"
                theme="custom-theme-dark"
                statisticColor="#fff"
                outline={{
                  border: 8,
                  distance: 0,
                }}
              />
            </RKCard>
          </Col>
          <Col span={6}>
            <RKCard title="玫瑰图">
              <RkRose
                color={colors7}
                theme="custom-theme-dark"
                height={220}
                xField="type"
                yField="value"
                seriesField="type"
                data={[
                  {
                    type: '分类一',
                    value: 27,
                  },
                  {
                    type: '分类二',
                    value: 25,
                  },
                  {
                    type: '分类三',
                    value: 18,
                  },
                  {
                    type: '分类四',
                    value: 15,
                  },
                  {
                    type: '分类五',
                    value: 10,
                  },
                  {
                    type: '其他',
                    value: 5,
                  },
                ]}
              />
            </RKCard>
          </Col>
          <Col span={6}>
            <RKCard title="柱状图">
              <RKColumn
                height={220}
                xField="hostGroup"
                yField="warning"
                color={() => {
                  return 'l(270) 0:#1e74bd 1:#3eded2';
                  // return 'l(270) 0:#7100fe 1:#8d03f9';
                  // return 'l(270) 0:#2643fe 1:#42a6f1';
                }}
                data={[
                  {
                    warning: 2,
                    hostGroup: 'redis',
                  },
                  {
                    warning: 2,
                    hostGroup: 'informix',
                  },
                  {
                    warning: 2,
                    hostGroup: 'os_linux',
                  },
                  {
                    warning: 2,
                    hostGroup: 'EvilHostGroup',
                  },
                  {
                    warning: 2,
                    hostGroup: 'mysql',
                  },
                  {
                    warning: 2,
                    hostGroup: 'tongweb',
                  },
                  {
                    warning: 1,
                    hostGroup: 'tomcat',
                  },
                  {
                    warning: 1,
                    hostGroup: 'bes',
                  },
                  {
                    warning: 1,
                    hostGroup: 'kingbase',
                  },
                  {
                    warning: 1,
                    hostGroup: 'apache',
                  },
                ]}
                theme="custom-theme-dark"
              />
            </RKCard>
          </Col>
          <Col span={6}>
            <RKCard title="折线图">
              <RkLine
                theme="custom-theme-dark"
                height={220}
                smooth
                xField="year"
                yField="value"
                seriesField="category"
                data={[
                  {
                    year: '2010',
                    value: 44,
                    category: 'Liquid fuel',
                  },
                  {
                    year: '2010',
                    value: 34,
                    category: 'Solid fuel',
                  },
                  {
                    year: '2010',
                    value: 67,
                    category: 'Gas fuel',
                  },
                  {
                    year: '2011',
                    value: 74,
                    category: 'Liquid fuel',
                  },
                  {
                    year: '2011',
                    value: 67,
                    category: 'Solid fuel',
                  },
                  {
                    year: '2011',
                    value: 70,
                    category: 'Gas fuel',
                  },
                  {
                    year: '2012',
                    value: 58,
                    category: 'Liquid fuel',
                  },
                  {
                    year: '2012',
                    value: 72,
                    category: 'Solid fuel',
                  },
                  {
                    year: '2012',
                    value: 81,
                    category: 'Gas fuel',
                  },
                  {
                    year: '2013',
                    value: 60,
                    category: 'Liquid fuel',
                  },
                  {
                    year: '2013',
                    value: 42,
                    category: 'Solid fuel',
                  },
                  {
                    year: '2013',
                    value: 63,
                    category: 'Gas fuel',
                  },
                  {
                    year: '2014',
                    value: 45,
                    category: 'Liquid fuel',
                  },
                  {
                    year: '2014',
                    value: 89,
                    category: 'Solid fuel',
                  },
                  {
                    year: '2014',
                    value: 75,
                    category: 'Gas fuel',
                  },
                ]}
              />
            </RKCard>
          </Col>
          <Col span={6}>
            <RKCard title="面积图">
              <RkArea
                theme="custom-theme-dark"
                height={220}
                smooth
                xField="timePeriod"
                yField="value"
                data={[
                  {
                    timePeriod: '2006 Q3',
                    value: 10,
                  },
                  {
                    timePeriod: '2006 Q4',
                    value: 20,
                  },
                  {
                    timePeriod: '2007 Q1',
                    value: 18,
                  },
                  {
                    timePeriod: '2007 Q2',
                    value: 33,
                  },
                  {
                    timePeriod: '2007 Q3',
                    value: 27,
                  },
                  {
                    timePeriod: '2007 Q4',
                    value: 19,
                  },
                ]}
              />
            </RKCard>
          </Col>

          <Col span={6}>
            <RKCard title="雷达图">
              <RkRadar
                color={radarColors}
                theme="custom-theme-dark"
                height={220}
                xField="item"
                yField="score"
                seriesField="user"
                meta={{
                  score: {
                    alias: '分数',
                    min: 0,
                    max: 80,
                  },
                }}
                data={[
                  {
                    item: 'Design',
                    user: 'a',
                    score: 70,
                  },
                  {
                    item: 'Design',
                    user: 'b',
                    score: 30,
                  },
                  {
                    item: 'Development',
                    user: 'a',
                    score: 60,
                  },
                  {
                    item: 'Development',
                    user: 'b',
                    score: 70,
                  },
                  {
                    item: 'Marketing',
                    user: 'a',
                    score: 50,
                  },
                  {
                    item: 'Marketing',
                    user: 'b',
                    score: 60,
                  },
                  {
                    item: 'Users',
                    user: 'a',
                    score: 40,
                  },
                  {
                    item: 'Users',
                    user: 'b',
                    score: 50,
                  },
                  {
                    item: 'Test',
                    user: 'a',
                    score: 60,
                  },
                  {
                    item: 'Test',
                    user: 'b',
                    score: 70,
                  },
                  {
                    item: 'Language',
                    user: 'a',
                    score: 70,
                  },
                  {
                    item: 'Language',
                    user: 'b',
                    score: 50,
                  },
                  {
                    item: 'Technology',
                    user: 'a',
                    score: 50,
                  },
                  {
                    item: 'Technology',
                    user: 'b',
                    score: 40,
                  },
                  {
                    item: 'Support',
                    user: 'a',
                    score: 30,
                  },
                  {
                    item: 'Support',
                    user: 'b',
                    score: 40,
                  },
                  {
                    item: 'Sales',
                    user: 'a',
                    score: 60,
                  },
                  {
                    item: 'Sales',
                    user: 'b',
                    score: 40,
                  },
                  {
                    item: 'UX',
                    user: 'a',
                    score: 50,
                  },
                  {
                    item: 'UX',
                    user: 'b',
                    score: 60,
                  },
                ]}
              />
            </RKCard>
          </Col>
          <Col span={6}>
            <RKCard title="条形图">
              <RkBar
                color={colors4}
                theme="custom-theme-dark"
                height={220}
                xField="value"
                yField="type"
                seriesField="type"
                data={[
                  {
                    type: '未分类',
                    value: 10,
                  },
                  {
                    type: '信息',
                    value: 20,
                  },
                  {
                    type: '警告',
                    value: 30,
                  },
                  {
                    type: '一般严重',
                    value: 40,
                  },
                  {
                    type: '严重',
                    value: 10,
                  },
                  {
                    type: '灾难',
                    value: 20,
                  },
                ]}
              />
            </RKCard>
          </Col>
          <Col span={6}>
            <Row gutter={[24, 24]}>
              <Col span={24}>
                <RKStatisticCard
                  theme="dark"
                  statistic={{
                    title: '总流量(人次)',
                    value: 601986875,
                  }}
                />
              </Col>
              <Col span={24}>
                <RKStatisticCard
                  theme="dark"
                  statistic={{
                    title: '总流量(人次)',
                    value: 601986875,
                  }}
                />
              </Col>
            </Row>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default DataCockpit;
