import RKCol from '@/components/RKCol';
import { requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useLocation, useNavigate, useParams } from '@umijs/max';
import { Card, Col, Form, Radio, Row, Space, Table } from 'antd';
import React, { useEffect, useState } from 'react';

// 模拟标签数据
const tagOptions = [
  { label: '系统', value: '系统' },
  { label: '性能', value: '性能' },
  { label: '网络', value: '网络' },
  { label: '存储', value: '存储' },
  { label: '安全', value: '安全' },
];

// 模拟监控指标数据
const metricsOptions = [
  { label: 'CPU使用率', value: 'CPU使用率' },
  { label: '内存使用率', value: '内存使用率' },
  { label: '磁盘使用率', value: '磁盘使用率' },
  { label: '网络流入速率', value: '网络流入速率' },
  { label: '网络流出速率', value: '网络流出速率' },
];

// 模拟事件等级数据
const eventLevelOptions = [
  { label: '信息', value: '1', color: 'blue' },
  { label: '警告', value: '2', color: 'orange' },
  { label: '一般严重', value: '3', color: 'grey' },
  { label: '严重', value: '4', color: 'red' },
  { label: '灾难', value: '5', color: 'purple' },
];

// 模拟条件操作符数据
const operatorOptions = [
  { label: '大于', value: '>' },
  { label: '等于', value: '=' },
  { label: '小于', value: '<' },
];

// 模拟条件值类型数据
const valueTypeOptions = [
  { label: '固定值', value: 'fixed' },
  { label: '百分比', value: 'percentage' },
  { label: '阈值', value: 'threshold' },
];

// 模拟依赖项数据
const dependenciesData = [
  { id: '1', name: 'CPU使用率' },
  { id: '2', name: '内存使用率' },
  { id: '3', name: '磁盘使用率' },
];

// 模拟阈值详情数据
const mockThresholdDetail = {
  id: '1',
  name: 'CPU使用率超过80%',
  eventName: 'CPU_USAGE_HIGH',
  eventLevel: '2',
  metricName: 'CPU使用率',
  tags: ['系统', '性能'],
  description: 'CPU使用率超过80%时触发警告',
  status: '0',
  condition: {
    valueType: 'percentage',
    operator: '>',
    value: '80',
  },
  dependencies: ['1', '2'],
};

const ThresholdDetail: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const location = useLocation();

  // 判断是否为查看模式
  const isViewMode = location.pathname.includes('/view/');

  const [initialValues, setInitialValues] = useState<any>(null);

  // 模拟获取数据
  useEffect(() => {
    // 实际项目中应该从API获取数据
    setInitialValues(mockThresholdDetail);
    form.setFieldsValue({
      ...mockThresholdDetail,
      valueType: mockThresholdDetail.condition.valueType,
      operator: mockThresholdDetail.condition.operator,
      value: mockThresholdDetail.condition.value,
    });
  }, [form]);

  // 处理取消按钮
  const handleCancel = () => {
    navigate(`/monitor-config/host/metrics-config/threshold/${id}`);
  };

  // 处理编辑按钮
  const handleEdit = () => {
    navigate(`/monitor-config/host/metrics-config/threshold/edit/${id}`);
  };

  // // 处理保存按钮
  // const handleSave = async () => {
  //   try {
  //     const values = await form.validateFields();
  //     console.log('Form values:', values);

  //     // 实际项目中应该调用API保存数据
  //     message.success('保存成功');
  //     navigate(`/monitor-config/host/metrics-config/threshold/${id}`);
  //   } catch (error) {
  //     console.error('Validation failed:', error);
  //   }
  // };

  // 依赖项表格列定义
  const dependenciesColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
  ];

  if (!initialValues) {
    return <div>加载中...</div>;
  }

  return (
    <PageContainer header={{ title: false }}>
      <Card>
        <ProForm
          form={form}
          submitter={
            isViewMode
              ? {
                  searchConfig: {
                    submitText: '编辑',
                    resetText: '取消',
                  },
                  onSubmit() {
                    handleEdit();
                  },
                  onReset: () => {
                    handleCancel();
                  },
                  render: (props, doms) => {
                    return <FooterToolbar>{doms}</FooterToolbar>;
                  },
                  resetButtonProps: {
                    disabled: false,
                  },
                }
              : {
                  searchConfig: {
                    submitText: '保存',
                    resetText: '取消',
                  },
                  onReset: () => {
                    handleCancel();
                  },
                  render: (props, doms) => {
                    return <FooterToolbar>{doms}</FooterToolbar>;
                  },
                  // submitButtonProps: {
                  //   loading: addLoading || editLoading,
                  // },
                  resetButtonProps: {
                    disabled: false,
                  },
                }
          }
          onFinish={async (values) => {
            console.warn('🚀 ~ onFinish={ ~ values:', values);
          }}
          readonly={isViewMode}
          initialValues={initialValues}
        >
          <Row gutter={24}>
            <RKCol>
              <ProFormText name="name" label="名称" disabled rules={[requiredRule]} />
            </RKCol>
            <RKCol>
              <ProFormText name="eventName" label="事件名称" disabled />
            </RKCol>
            <RKCol>
              <ProFormSelect
                name="tags"
                disabled
                label="标签"
                mode="multiple"
                options={tagOptions}
              />
            </RKCol>
            <RKCol>
              <ProFormSwitch
                name="status"
                label="启用"
                valuePropName="checked"
                fieldProps={{
                  checked: initialValues.status === '0',
                }}
              />
            </RKCol>
            <RKCol lg={12} md={12} sm={12}>
              <ProFormSelect
                className="ant-picker"
                name="metricName"
                label="监控指标"
                options={metricsOptions}
                disabled
                rules={[requiredRule]}
              />
            </RKCol>
            <RKCol lg={24} md={24} sm={24}>
              <Form.Item name="eventLevel" label="事件严重等级" rules={[requiredRule]}>
                <Radio.Group disabled>
                  {eventLevelOptions.map((level) => (
                    <Radio key={level.value} value={level.value}>
                      <Space>
                        <div
                          style={{
                            marginTop: 5,
                            width: 8,
                            height: 16,
                            backgroundColor: level.color,
                            display: 'inline-block',
                          }}
                        />
                        {level.label}
                      </Space>
                    </Radio>
                  ))}
                </Radio.Group>
              </Form.Item>
            </RKCol>
            <RKCol lg={24} md={24} sm={24}>
              <Form.Item label="条件" required>
                <Row gutter={8}>
                  <Col span={6}>
                    <Form.Item name="valueType" noStyle>
                      <ProFormSelect options={valueTypeOptions} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item name="operator" noStyle>
                      <ProFormSelect options={operatorOptions} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item name="value" noStyle>
                      <ProFormText />
                    </Form.Item>
                  </Col>
                </Row>
              </Form.Item>
            </RKCol>
            <RKCol lg={12} md={12} sm={12}>
              <ProFormTextArea
                className="ant-picker"
                name="description"
                label="描述"
                placeholder="请输入描述"
                fieldProps={{ rows: 1 }}
              />
            </RKCol>
          </Row>
        </ProForm>

        <div style={{ marginTop: 24 }}>
          <h3>依赖项</h3>
          <Table
            size="middle"
            rowKey="id"
            columns={dependenciesColumns}
            dataSource={dependenciesData.filter((item) =>
              initialValues.dependencies.includes(item.id),
            )}
            pagination={false}
          />
        </div>
      </Card>
    </PageContainer>
  );
};

export default ThresholdDetail;
