import { userLogin } from '@/services/http/user';
import { history, useRequest } from '@umijs/max';
import { Spin, Typography } from 'antd';
import defaultSettings from 'config/defaultSettings';
import { FC } from 'react';
import styles from './index.less';

const AutoLogin: FC<{
  account: string;
  password: string;
}> = ({ account, password }) => {
  useRequest(
    () =>
      userLogin({
        account,
        password,
      }),
    {
      pollingInterval: 5000,
      formatResult: (res) => res,
      onSuccess: (res) => {
        if (res.code === 200) {
          localStorage.setItem(defaultSettings.TOKEN_KEY, res.data || '');
          history.replace('/');
          window.location.reload();
        }
      },
    },
  );
  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <Spin spinning size="large" style={{ marginBlockEnd: 16 }} />
        <Typography.Title level={5}>自动登录中</Typography.Title>
        <Typography.Text>加载中，请耐心等待。</Typography.Text>
      </div>
    </div>
  );
};

export default AutoLogin;
