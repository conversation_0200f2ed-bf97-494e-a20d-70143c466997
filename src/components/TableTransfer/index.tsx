import { DeleteOutlined } from '@ant-design/icons';
import Table from 'antd/es/table';
import { ColumnsType, TableRowSelection } from 'antd/es/table/interface';
import Transfer, { TransferItem, TransferProps } from 'antd/es/transfer';
import classNames from 'classnames/bind';
import { difference, uniqBy } from 'lodash';
import React, { memo, useEffect, useState } from 'react';
import styles from './index.less';
const cx = classNames.bind(styles);

interface TableTransferProps extends Omit<TransferProps<TransferItem>, 'onChange'> {
  dataSource: Record<string, any>[];
  leftColumns: ColumnsType<Record<string, any>>;
  rightColumns: ColumnsType<Record<string, any>>;
  tableRowKey?: string;
  onChange?: (targetKeys: string[], dataSource: Record<string, any>[]) => void;
  loading?: boolean;
  total?: number;
  rightData?: any[]; // 回填的数据
  keywords?: string; // 搜索关键字
}

const TableTransfer: React.FC<TableTransferProps> = memo((props) => {
  const {
    tableRowKey = 'key',
    dataSource,
    leftColumns,
    rightColumns,
    targetKeys: initTargetKeys,
    onChange,
    loading,
    keywords = 'id',
    ...restProps
  } = props;

  const [targetKeys, setTargetKeys] = useState<string[]>([]);
  const [rightDataSource, setRightDataSource] = useState<Record<string, any>[]>([]);
  const [leftDataSource, setLeftDataSource] = useState<Record<string, any>[]>(dataSource);

  useEffect(() => {
    setTargetKeys(initTargetKeys || []);
    const data = uniqBy(
      dataSource.filter((item) => initTargetKeys?.includes(item[tableRowKey])),
      tableRowKey,
    );
    setRightDataSource(data || []);
  }, [initTargetKeys, dataSource]);

  const action: ColumnsType<Record<string, any>> = [
    {
      width: 80,
      title: '',
      dataIndex: '',
      key: 'x',
      align: 'center',
      render: (text, record) => (
        <a
          onClick={() => {
            const arr = targetKeys.filter((item) => item !== record[tableRowKey]);
            setTargetKeys(arr);
            onChange?.(arr, dataSource);
            setRightDataSource(rightDataSource.filter((item) => arr.includes(item[tableRowKey])));
          }}
        >
          <DeleteOutlined />
        </a>
      ),
    },
  ];

  const customRightColumns: ColumnsType<Record<string, any>> = [...rightColumns, ...action];

  // 选项右移的函数
  const customChange: TransferProps<TransferItem>['onChange'] = (nextTargetKeys: any) => {
    setTargetKeys(nextTargetKeys);
    onChange?.(nextTargetKeys, dataSource);
    setRightDataSource(dataSource.filter((item) => nextTargetKeys.includes(item[tableRowKey])));
  };

  const pagination = { total: dataSource.length, pageSize: 10 };

  return (
    <Transfer
      {...restProps}
      targetKeys={targetKeys}
      onChange={customChange}
      filterOption={(inputValue, item) => {
        return item[tableRowKey]!.indexOf(inputValue) !== -1;
      }}
      oneWay
      showSelectAll={false}
      dataSource={dataSource}
      rowKey={(record) => record[tableRowKey]}
      selectAllLabels={[
        ({ selectedCount }) => (
          <>
            {!!selectedCount && <>{selectedCount}/</>}
            {dataSource.length}项
          </>
        ),
        ({ totalCount }) => <>{totalCount}项</>,
      ]}
      onSearch={(direction, value) => {
        if (direction === 'left') {
          const list = dataSource.filter((item) => item[keywords].includes(value));
          setLeftDataSource(list);
        } else {
          const data = dataSource.filter((item) => targetKeys.includes(item[tableRowKey]));
          const list = data.filter((item: Record<string, any>) => item[keywords].includes(value));
          setRightDataSource(list);
        }
      }}
    >
      {({
        direction,
        onItemSelect,
        selectedKeys: listSelectedKeys,
        onItemSelectAll,
        // filteredItems,
      }) => {
        const rowSelection: TableRowSelection<Record<string, any>> = {
          getCheckboxProps: (item) => ({
            disabled: item.disabled,
          }),
          onSelectAll(selected, selectedRows) {
            const treeSelectedKeys = selectedRows
              .filter((item) => !item?.disabled)
              .map(({ [tableRowKey]: id }) => id);
            const diffKeys = selected
              ? difference(treeSelectedKeys, listSelectedKeys)
              : difference(listSelectedKeys, treeSelectedKeys);
            onItemSelectAll(diffKeys, selected);
          },
          onSelect({ [tableRowKey]: id }, selected) {
            onItemSelect(id, selected);
          },
          selectedRowKeys: listSelectedKeys,
        };

        const leftData = leftDataSource.map((item) => ({
          ...item,
          disabled: targetKeys.includes(item[tableRowKey]),
        }));

        return (
          <Table<Record<string, any>>
            className={cx('rk-transfer-table')}
            rowSelection={direction === 'left' ? rowSelection : undefined}
            columns={direction === 'left' ? leftColumns : customRightColumns}
            loading={direction === 'left' && loading}
            dataSource={direction === 'left' ? leftData : rightDataSource}
            size="small"
            rowKey={tableRowKey}
            onRow={(record) => ({
              onClick: () => {
                if (direction === 'right') return;
                if (record.disabled) return;
                onItemSelect(record[tableRowKey], !listSelectedKeys.includes(record[tableRowKey]));
              },
            })}
            pagination={direction === 'left' ? pagination : { pageSize: 10 }}
          />
        );
      }}
    </Transfer>
  );
});

export default TableTransfer;
