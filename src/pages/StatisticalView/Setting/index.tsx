import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import {
  deletePanoramicGroup,
  panoramicGroupPage,
  updatePanoramicGroup,
} from '@/services/http/panoramic';
import { zabbix } from '@/services/zabbix';
import { queryRkPagingTable, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { Link, useRequest } from '@umijs/max';
import { Button, message, Modal, Space, Switch } from 'antd';
import GlobalSetting from '../components/GlobalSetting';
import GroupDrawerForm from '../components/GroupDrawerForm';

const Setting: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.PanoramicGroupPageVO[]>([]);

  const reload = () => tableRef.current?.reloadAndRest?.();

  // 删除
  const { run: deleteRecord } = useRequest((ids) => deletePanoramicGroup({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success('删除成功');
      reload();
    },
    formatResult: (res) => res,
  });

  const handleDelete = async (rows: API.PanoramicGroupPageVO[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.name!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除全景组“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 置顶
  const { run: toTop } = useRequest((id, params) => updatePanoramicGroup({ id }, params), {
    manual: true,
    formatResult: (res) => res,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success('操作成功！');
      reload();
    },
  });

  // 获取主机
  const { data: hostList, loading } = useRequest(() =>
    zabbix({
      method: 'host.get',
      sortfield: 'name',
      output: ['hostid', 'name'],
    }),
  );

  const columns: ProColumns<API.PanoramicGroupPageVO>[] = [
    {
      title: '组名',
      dataIndex: 'name',
      width: 180,
      render: (dom, record) => {
        return <GroupDrawerForm key="add" initialValues={record} reload={reload} />;
      },
      initialValue: queryParams.get('name'),
    },
    {
      title: '监控对象',
      hideInSearch: true,
      dataIndex: 'hostIds',

      render: (dom, entity) => {
        const { hostIds } = entity;
        if (!hostIds?.length || !hostList?.length) return <>-</>;
        const hosts = hostList?.filter((item: RK_API.Host) => hostIds.includes(item.hostid!));
        if (!hosts.length) return '-';
        return hosts.map((item: RK_API.Host, index: number) => {
          return (
            <Link key={item.hostid} to={`/monitor-config/host/details/${item?.hostid}`}>
              {index > 0 && <span key={index}>,</span>}
              {item.name}
            </Link>
          );
        });
      },
    },
    {
      title: '置顶',
      hideInSearch: true,
      dataIndex: 'top',
      width: 150,
      render(dom, entity) {
        const { id, top, ...rest } = entity;
        return (
          <Switch
            defaultChecked={top}
            onChange={() => {
              toTop(id, {
                ...rest,
                top: !top,
              });
            }}
          />
        );
      },
    },
    {
      title: '操作',
      fixed: 'right',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space size={16}>
            <Button key="del" type="link" onClick={() => handleDelete([record])}>
              删除
            </Button>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.PanoramicGroupPageVO>
        {...defaultTableConfig}
        loading={loading}
        search={SearchOptionRender}
        onSubmit={syncToUrl}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columns={columns}
        headerTitle="全景组列表"
        toolbar={{
          actions: [<GroupDrawerForm key="add" reload={reload} />, <GlobalSetting key="setting" />],
        }}
        request={async (params) => queryRkPagingTable(params, panoramicGroupPage)}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
});

export default Setting;
