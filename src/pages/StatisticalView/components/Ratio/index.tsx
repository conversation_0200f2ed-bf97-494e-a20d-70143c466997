import { Tooltip } from 'antd';
import { FC } from 'react';
import styles from './index.less';

type RatioProps = {
  value?: string | number;
  units?: string;
  color?: string;
};

const Ratio: FC<RatioProps> = ({ value = 0, units = '', color }) => {
  const roundedValue = typeof value === 'string' ? parseFloat(value).toFixed(1) : value.toFixed(1);

  return (
    <Tooltip title={`${roundedValue}${units}`}>
      <div
        className={styles['ratio-container']}
        style={{
          // @ts-ignore
          '--final-width': roundedValue + '%',
        }}
      >
        <div
          className={styles['ratio-bar']}
          style={{
            // width: roundedValue + '%',
            background: color,
          }}
        />
      </div>
    </Tooltip>
    // {roundedValue} {units}
  );
};

export default Ratio;
