import { findRedirectByPath } from '@/utils';
import { useEmotionCss } from '@ant-design/use-emotion-css';
import { history } from '@umijs/max';
import { Button, Result } from 'antd';
import routes from 'config/routes';
import React from 'react';
const redirectPath = findRedirectByPath(routes, '/');

const UnAccessible: React.FC = () => {
  const resultClassName = useEmotionCss(() => {
    return {
      height: '100%',
      width: '100%',
      padding: 0,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      img: {
        width: '30vw',
        minWidth: 375,
      },
    };
  });
  return (
    <Result
      className={resultClassName}
      icon={<img src="/images/403.webp" />}
      subTitle="抱歉，你无权访问该页面"
      extra={
        <Button type="primary" onClick={() => history.push(redirectPath!)}>
          Back Home
        </Button>
      }
    />
  );
};

export default UnAccessible;
