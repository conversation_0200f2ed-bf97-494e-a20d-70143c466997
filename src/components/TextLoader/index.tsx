const TextLoader = () => {
  const rectWidth = 4;
  const rectHeight = 4;
  const rectSpacing = 8;
  const animationDuration = 2.5;
  const animationDelayIncrement = 0.5;

  const rects = [0, 1, 2, 3].map((index) => (
    <rect
      key={index}
      x={index * rectSpacing}
      y="0"
      width={rectWidth}
      height={rectHeight}
      fill="rgba(0, 0, 0, 0.88)"
    >
      <animate
        attributeName="opacity"
        attributeType="XML"
        values="1; .2; 1"
        begin={`${animationDelayIncrement * index}s`}
        dur={`${animationDuration}s`}
        repeatCount="indefinite"
      />
    </rect>
  ));

  return (
    <div className="loader loader--style7">
      <svg
        version="1.1"
        id="Layer_1"
        xmlns="http://www.w3.org/2000/svg"
        x="0px"
        y="0px"
        width={`${rects.length * rectSpacing}px`}
        height={`${rectHeight}px`}
        viewBox={`0 0 ${rects.length * rectSpacing} ${rectHeight}`}
        xmlSpace="preserve"
      >
        {rects}
      </svg>
    </div>
  );
};

export default TextLoader;
