import RKCol from '@/components/RKCol';
import { SEVERITIES } from '@/enums';
import { zabbix, zabbixPost } from '@/services/zabbix';
import { onSuccessAndGoBack, queryFormData } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProColumns,
  ProForm,
  ProFormDependency,
  ProFormInstance,
  ProFormRadio,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { history, Link, useParams, useRequest, useSearchParams } from '@umijs/max';
import { Row } from 'antd';
import React, { useRef, useState } from 'react';

const TriggerForm: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const [dependencies, setDependencies] = useState<Record<string, any>[]>([]);

  // 判断是否为编辑页面
  const { id } = useParams();

  const [getSearchArr] = useSearchParams();

  const isEditPage = !!id;

  // 新建
  const { run: add, loading: addLoading } = useRequest(
    (value) => zabbixPost({ ...value, method: 'trigger.create' }),
    {
      manual: true,
      onSuccess: onSuccessAndGoBack,
      formatResult: (res) => res,
    },
  );
  // 修改
  const { run: update, loading: updateLoading } = useRequest(
    (value) => zabbixPost({ ...value, method: 'trigger.update' }),
    {
      manual: true,
      onSuccess: onSuccessAndGoBack,
      formatResult: (res) => res,
    },
  );
  // 获取上层模版
  // const { run: getParentTemplate } = useRequest(
  //   (templateid) =>
  //     zabbix({
  //       triggerids: [templateid],
  //       method: 'template.get',
  //     }),
  //   {
  //     manual: true,
  //     onSuccess: (res = []) => {
  //       const data = res[0];
  //       if (data) formRef.current?.setFieldValue('parent_template', res?.[0].name);
  //     },
  //   },
  // );

  // 获取详情
  // useRequest(
  //   () =>
  //     zabbix({
  //       triggerids: [id],
  //       selectTags: 'extend',
  //       expandExpression: true,
  //       method: 'trigger.get',
  //     }),
  //   {
  //     ready: isEditPage,
  //     onSuccess: (res = []) => {
  //       const data = res[0];
  //       if (data) formRef.current?.setFieldsValue(res?.[0]);
  //       if (data.templateid) getParentTemplate(data.templateid);
  //     },
  //   },
  // );

  // 依赖关系columns
  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '名称',
      dataIndex: 'description',
      render(dom, entity) {
        return (
          <Link to={`/monitor-config/host/trigger/details/${id}/${entity.triggerid}`}>{dom}</Link>
        );
      },
    },
  ];

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<RK_API.Trigger>
        formRef={formRef}
        initialValues={{
          recovery_mode: '0', // 事件恢复生成模式 默认0:表达式
          type: '0', // 触发器是否能够生成多个问题事件 0 - (默认) 单个
          correlation_mode: '0', // 	事件恢复关联的模式 0 - (默认) 所有问题
          manual_close: '0', // 	允许手动关闭 0 - (默认) 不允许
          hostid: getSearchArr.getAll('hostid').at(0),
        }}
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          onReset: () => {
            history.go(-1);
          },

          render: (props, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
          submitButtonProps: {
            loading: addLoading || updateLoading,
          },
        }}
        params={{
          triggerId: id,
        }}
        request={async () => {
          const res = await queryFormData(
            {
              triggerids: [id],
              selectTags: 'extend',
              expandExpression: true,
              selectDependencies: 'extend',
              method: 'trigger.get',
            },
            isEditPage,
            zabbix,
          );

          setDependencies(res?.dependencies || []);
          return res;
        }}
        onFinish={async (values) => {
          const { triggerid, priority, comments, url, status, tags } = values;
          const updateParams = {
            triggerid,
            priority,
            url,
            comments,
            status,
            tags,
          };
          if (isEditPage) {
            update(updateParams);
          } else {
            add(values);
          }
        }}
      >
        {/* 不需要展示，只是为了form传值 */}
        <div className="rk-none">
          <ProFormText name="triggerid" />
          {/* <ProFormText name="hostid" /> */}
        </div>
        <Row gutter={24}>
          {/* <ProFormDependency name={['templateid']}>
            {(templateid) => {
              if (!templateid) return null;
              return (
                <RKCol>
                  <ProFormText disabled label="上层触发器" name="parent_template" />
                </RKCol>
              );
            }}
          </ProFormDependency> */}
          <RKCol>
            <ProFormTextArea
              disabled={isEditPage}
              label="名称"
              name="description"
              rules={[requiredRule]}
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 2,
                },
              }}
            />
          </RKCol>
          <ProFormDependency name={['description']}>
            {({ description }) => {
              return (
                <RKCol>
                  <ProFormTextArea
                    disabled={isEditPage}
                    label="事件名称"
                    name="event_name"
                    placeholder={description}
                    fieldProps={{
                      autoSize: {
                        minRows: 1,
                        maxRows: 2,
                      },
                    }}
                  />
                </RKCol>
              );
            }}
          </ProFormDependency>
          <RKCol>
            <ProFormSwitch
              label="启用"
              name="status"
              getValueFromEvent={(val) => (val ? '0 ' : '1')}
              getValueProps={(value) => ({ checked: value === '0' })}
            />
          </RKCol>
          {/* <RKCol>
            <ProFormTextArea
              disabled={isEditPage}
              label="操作数据"
              name="opdata"
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 2,
                },
              }}
            />
          </RKCol> */}
          {/* <RKCol>
            <ProFormSelect
              disabled={isEditPage}
              label="事件恢复生成模式"
              name="recovery_mode"
              options={RECOVERY_MODE}
            />
          </RKCol>  */}
          {/* {isEditPage && (
            <RKCol>
              <ProFormTextArea
                disabled
                label="恢复表达式"
                name="recovery_expression"
                rules={[requiredRule]}
                fieldProps={{
                  autoSize: {
                    minRows: 1,
                    maxRows: 2,
                  },
                }}
              />
            </RKCol>
          )} */}
          {/* 
          <RKCol>
            <ProFormSelect
              disabled={isEditPage}
              label="问题事件生成模式"
              name="type"
              options={PROBLEM_EVENT_TYPE}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              disabled={isEditPage}
              label="事件成功关闭"
              name="correlation_mode"
              options={CORRELATION_MODE}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              disabled={isEditPage}
              label="允许手动关闭"
              name="manual_close"
              options={MANUAL_CLOSE}
            />
          </RKCol> */}
          <RKCol lg={24} md={24} sm={24}>
            <ProFormRadio.Group label="事件等级" name="priority" options={SEVERITIES} />
          </RKCol>
          <RKCol lg={24} md={24} sm={24}>
            <ProFormTextArea
              disabled={isEditPage}
              label="条件"
              name="expression"
              rules={[requiredRule]}
              fieldProps={{
                autoSize: {
                  minRows: 1,
                  maxRows: 2,
                },
              }}
            />
          </RKCol>
          <RKCol lg={24} md={24} sm={24}>
            <ProFormTextArea
              name="comments"
              label="描述"
              fieldProps={{ autoSize: { minRows: 2, maxRows: 2 } }}
            />
          </RKCol>

          {/* <RKCol>
            <ProFormText name="url" label="URL" />
          </RKCol> */}
        </Row>
        {/* <Divider /> */}
        {/* 标记 */}
        {/* <TagSetter /> */}
        {/* 触发器依赖关系 */}
        <ProTable
          className="inner-table"
          headerTitle="依赖项"
          options={false}
          columns={columns}
          dataSource={dependencies}
          search={false}
          pagination={false}
          size="small"
          rowKey="triggerid"
        />
      </ProForm>
    </PageContainer>
  );
};

export default TriggerForm;
