import { Typography } from 'antd';
import classNames from 'classnames/bind';
import styles from './index.less';
const cx = classNames.bind(styles);

const RKCard: React.FC<{
  children?: React.ReactNode;
  title?: string;
  extra?: React.ReactNode;
}> = ({ children, title, extra }) => {
  return (
    <div className={cx('card')}>
      <div className={cx('header')}>
        <div className={cx('title')}>
          {title && <span className={cx('icon')} />}
          <Typography.Title level={4} className={cx('title')}>
            {title}
          </Typography.Title>
        </div>
        {extra}
      </div>
      <div className={cx('box')}>{children}</div>
    </div>
  );
};

export default RKCard;
