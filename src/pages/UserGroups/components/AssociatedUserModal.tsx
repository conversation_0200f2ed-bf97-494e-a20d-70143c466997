import TransferTable from '@/components/TableTransfer';
import { useUserList } from '@/hooks/useUserList';

import Modal, { ModalProps as AntModalProps } from 'antd/es/modal';
import React, { useEffect, useRef } from 'react';

const leftColumns = [
  {
    dataIndex: 'username',
    width: 150,
    ellipsis: true,
    title: '用户名',
  },
];

type ModalProps = Omit<AntModalProps, 'onOk'> & {
  targetKeys?: string[];
  onOk?: (keys: string[], totalData: RK_API.UserInfoDTO[]) => void;
  titles?: string[];
  onChange?: (keys: RK_API.UserInfoDTO[]) => void;
  value?: RK_API.UserInfoDTO[];
};

const AssociatedUserModal: React.FC<ModalProps> = (props) => {
  const { onOk, titles, onChange, value = [], ...restProps } = props;
  const selectedKeys = useRef<string[]>([]);
  const selectedTotalData = useRef<RK_API.UserInfoDTO[]>([]);
  useEffect(() => {
    selectedKeys.current = value?.map((item) => item.userid!) || [];
    selectedTotalData.current = [...value];
  }, [value]);
  const customOk = () => {
    const selectList =
      selectedTotalData.current?.filter((item) => selectedKeys.current.includes(item.userid!)) ||
      [];
    onOk?.(selectedKeys.current || [], selectList);
    onChange?.(selectList);
  };

  // 左侧列表请求数据
  const { userList = [], loading } = useUserList();

  return (
    <Modal title="关联用户" width={800} centered onOk={customOk} destroyOnClose {...restProps}>
      <TransferTable
        titles={titles || ['所有用户', '已关联用户']}
        tableRowKey="userid"
        dataSource={userList}
        loading={loading}
        leftColumns={leftColumns}
        rightColumns={leftColumns}
        onChange={(keys, totalData) => {
          selectedKeys.current = keys;
          selectedTotalData.current = totalData;
        }}
        targetKeys={selectedKeys.current}
        showSearch
        keywords="username"
      />
    </Modal>
  );
};

export default AssociatedUserModal;
