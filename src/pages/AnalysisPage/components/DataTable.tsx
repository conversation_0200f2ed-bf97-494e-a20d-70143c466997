import IconFont from '@/components/Icon';
import { SEVERITIES, SUPPRESSED_STATUS } from '@/enums';
import { zabbix } from '@/services/zabbix';
import { formatSecondsToString, option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Link, useRequest } from '@umijs/max';
import { Badge, Button, Tag, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { FC, memo } from 'react';

const DataTable: FC<{
  data?: API.PanoramicGroupProblemPageVO;
  hideHostColumns?: boolean;
}> = ({ data, hideHostColumns }) => {
  // 获取trigger
  const { run: getTriggers, data: triggers } = useRequest(
    (triggerids) =>
      zabbix({
        method: 'trigger.get',
        triggerids,
        monitored: '1',
        skipDependent: '1', // 跳过处于问题状态且依赖于其他触发器的触发器
        preservekeys: '1',
        selectHosts: 'extend',
        selectFilter: 'extend',
        output: ['hosts', 'manual_close', 'lastchange'],
      }),
    {
      manual: true,
    },
  );

  // 获取告警信息
  // const { run: getAlert } = useRequest(
  //   (eventids) =>
  //     zabbix({
  //       method: 'alert.get',
  //       selectUsers: 'extend',
  //       selectMediatypes: 'extend',
  //       eventids,
  //     }),
  //   {
  //     manual: true,
  //   },
  // );

  const columns: ProColumns<RK_API.Problem>[] = [
    {
      title: '时间',
      width: 180,
      dataIndex: 'clock',
      hideInSearch: true,
      renderText(text, record) {
        return dayjs.unix(triggers?.[record.objectid]?.lastchange).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '严重性',
      width: 100,
      dataIndex: 'severity',
      valueEnum: option2enum(SEVERITIES),
      render: (text, record) => {
        if (!text) return text;
        const severityObj = SEVERITIES.find((item) => record.severity === item.value);
        return severityObj ? (
          <Tooltip title={severityObj.label}>
            <Tag color={severityObj.tagColor}>{text}</Tag>
          </Tooltip>
        ) : (
          text
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'suppressed',
      width: 100,
      hideInSearch: true,
      valueEnum: option2enum(SUPPRESSED_STATUS),
      render: (dom, entity) => {
        if (Number(entity?.r_eventid) > 0) {
          return <Badge status="success" text="已解决" />;
        }
        return dom;
      },
    },
    {
      title: '主机',
      dataIndex: 'objectid',
      hideInSearch: true,
      width: 200,
      hideInTable: hideHostColumns,
      renderText: (objectId, entity) => {
        // @ts-ignore
        const obj = entity?.hosts?.length ? entity : triggers?.[objectId];
        return obj?.hosts?.at(0)?.name;
      },
      ellipsis: true,
    },
    {
      title: '问题',
      dataIndex: 'name',
      width: 300,
      ellipsis: true,
    },
    {
      title: '持续时间',
      dataIndex: 'clock',
      width: 200,
      renderText(text) {
        const now = dayjs().unix();
        const seconds = now - text;
        return formatSecondsToString(seconds);
      },
    },
  ];
  return (
    <ProTable<RK_API.Problem>
      {...defaultTableConfig}
      scroll={{
        x: '100%',
      }}
      style={{
        marginBlockEnd: 24,
      }}
      rowKey="eventid"
      pagination={{
        defaultPageSize: 5,
        showSizeChanger: true,
        simple: true,
      }}
      columns={columns}
      headerTitle={
        data?.name ? (
          <>
            {`${data.name || ''}-告警`}
            <Link to="/monitor/topology">
              <Button icon={<IconFont type="icon-topology" />} type="link" />
            </Link>
          </>
        ) : (
          '告警'
        )
      }
      // options={false}
      search={false}
      size="small"
      params={{ id: data?.hostIds }}
      request={async () => {
        if (!data?.hostIds?.length) return {};
        const problem = await queryPagingTable<RK_API.Trigger>(
          {
            method: 'problem.get',
            hostids: data?.hostIds,
            source: 0,
            object: 0,
            recent: false,
            selectTags: 'extend',
            selectAcknowledges: 'extend',
            suppressed: false,
            sortorder: ['DESC'],
            output: ['eventid', 'severity', 'objectid', 'name', 'suppressed', 'clock', 'r_eventid'],
          },
          zabbix,
        );
        const arr = problem.data?.map((item: RK_API.Problem) => item.objectid);
        // 使用 Set 来去重
        const triggerIds = [...new Set(arr)];
        const res = await getTriggers(triggerIds);
        // 不要依赖于其他触发器的触发
        const filterKeys = Object.keys(res || []);

        const resultData = problem?.data?.filter((item: RK_API.Problem) =>
          filterKeys.includes(item.objectid),
        );

        // const eventids = resultData?.map((item: RK_API.Problem) => item.eventid);
        // if (eventids.length) {
        //   getAlert(eventids);
        // }
        return {
          ...problem,
          data: resultData,
          total: resultData?.length,
        };
      }}
    />
  );
};

export default memo(DataTable);
