import { MACRO_TYPE } from '@/enums';
import { getRandomId } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  ActionType,
  EditableFormInstance,
  EditableProTable,
  ProColumns,
} from '@ant-design/pro-components';
import { Input, Tag } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less';

export type DataSourceType = {
  key: string;
  type: string;
  macro: string;
  value: string;
  defaultValue?: string;
  hostmacroid?: string;
  globalmacroid?: string;
  description?: string;
  inheritName?: string;
  inheritType?: string;
};

const MacrosSetter: React.FC<{
  value?: DataSourceType[];
  onChange?: (value: DataSourceType[]) => void;
  editorFormRef: React.Ref<EditableFormInstance<DataSourceType>>;
}> = ({ value, onChange, editorFormRef }) => {
  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '主机宏ID',
      dataIndex: 'hostmacroid',
      hideInTable: true,
    },
    {
      title: '',
      width: 200,
      dataIndex: 'globalmacroid',
      editable: false,
      render(dom, entity) {
        const { globalmacroid = '', inheritName = '' } = entity;
        if (globalmacroid) return <Tag color="blue">global</Tag>;
        if (inheritName) return <Tag color="gold">{inheritName}</Tag>;
        return <Tag color="cyan">private</Tag>;
      },
    },
    {
      title: '原始值',
      dataIndex: 'defaultValue',
      width: 120,
      editable: false,
      copyable: true,
    },
    {
      title: '宏',
      dataIndex: 'macro',
      width: 200,
      formItemProps: {
        rules: [
          requiredRule,
          {
            pattern: /^\{\$[A-Z0-9_.]+\}$/,
            message: '必须用花括号括起来，并以$开头,只能包含A-Z0-9_.等字符',
          },
        ],
      },
      valueType: 'textarea',
      fieldProps: {
        autoSize: {
          minRows: 1,
          maxRows: 2,
        },
        allowClear: true,
      },
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 130,
      valueType: 'select',
      fieldProps: {
        options: MACRO_TYPE,
        allowClear: false,
      },
    },
    {
      title: '值',
      width: 200,
      dataIndex: 'value',
      renderFormItem: (_, config) => {
        const { type } = config.record!;
        switch (type) {
          case '1':
            return <Input.Password autoComplete="new-password" />;
          default:
            return <TextArea autoComplete="off" autoSize={{ minRows: 1, maxRows: 2 }} allowClear />;
        }
      },
      formItemProps: {
        // rules: [requiredRule],
      },
    },
    {
      title: '描述',
      width: 300,
      dataIndex: 'description',
      valueType: 'textarea',
      fieldProps: {
        autoSize: {
          minRows: 1,
          maxRows: 2,
        },
        allowClear: true,
      },
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      width: 80,
    },
  ];

  const tableRef = useRef<ActionType | undefined>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>();

  useEffect(() => {
    setEditableRowKeys(
      value?.map((item) => item.key || item.hostmacroid || item.globalmacroid || '') || [],
    );
  }, [value]);

  return (
    <EditableProTable<DataSourceType>
      editableFormRef={editorFormRef}
      actionRef={tableRef}
      className={styles.editable}
      headerTitle="宏配置"
      rowKey={(record) => record.key || record.hostmacroid || record.globalmacroid || ''}
      recordCreatorProps={{
        newRecordType: 'dataSource',
        record: () => ({
          key: getRandomId(),
          type: '0',
          macro: '',
          value: '',
        }),
      }}
      scroll={{ x: 'max-content' }}
      loading={false}
      columns={columns}
      value={value}
      editable={{
        type: 'multiple',
        editableKeys,
        actionRender: (row) => {
          if (row.inheritType) return [];
          return [
            <a
              key="delete"
              onClick={() => {
                onChange?.(value?.filter((item) => item.key !== row.key) || []);
              }}
            >
              删除
            </a>,
          ];
        },
        onChange: setEditableRowKeys,
        onValuesChange: (record, recordList) => {
          onChange?.(recordList);
        },
      }}
    />
  );
};

export default MacrosSetter;
