// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 此处后端没有提供注释 GET /api/v1/rump/external/monitoring/${param0} */
export async function externalMonitoringPanelById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.externalMonitoringPanelByIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultExternalMonitoringPanelVO>(
    `/api/v1/rump/external/monitoring/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 PUT /api/v1/rump/external/monitoring/${param0}/update */
export async function externalMonitoringPanelUpdate(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.externalMonitoringPanelUpdateParams,
  body: API.ExternalMonitoringPanelCURequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/api/v1/rump/external/monitoring/${param0}/update`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/v1/rump/external/monitoring/create */
export async function externalMonitoringPanelCreate(
  body: API.ExternalMonitoringPanelCURequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/external/monitoring/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/v1/rump/external/monitoring/page */
export async function externalMonitoringPanelRemoveByIdsPage(
  body: API.ExternalMonitoringPanelPageRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultPageVOExternalMonitoringPanelPageVO>(
    '/api/v1/rump/external/monitoring/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 DELETE /api/v1/rump/external/monitoring/removeByIds */
export async function externalMonitoringPanelRemoveByIds(
  body: API.BatchIdRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/external/monitoring/removeByIds', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
