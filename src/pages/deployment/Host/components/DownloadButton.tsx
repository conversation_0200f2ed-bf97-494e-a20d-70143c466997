import { DownloadOutlined } from '@ant-design/icons';
import { request } from '@umijs/max';
import { Button } from 'antd';
import React, { useEffect } from 'react';

const DownloadButton: React.FC<{
  url: string;
  fileName: string;
  title?: string;
  type?: 'link' | 'default' | 'primary';
  method?: 'get' | 'post';
  data?: Record<string, any>;
}> = ({ url, fileName, title, type, method = 'get', data }) => {
  const handleDownload = () => {
    request(url, {
      responseType: 'blob',
      skipErrorHandler: true,
      method,
      data,
    }).then((res) => {
      const a = document.createElement('a');
      const url = window.URL.createObjectURL(res);
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    });
  };

  useEffect(() => {
    return () => {
      // 释放URL对象
      window.URL.revokeObjectURL(url);
    };
  }, [url]);

  if (type === 'link') return <a onClick={handleDownload}>{title || '下载'}</a>;

  return (
    <Button onClick={handleDownload} type={type}>
      <DownloadOutlined />
      {title || '下载'}
    </Button>
  );
};

export default DownloadButton;
