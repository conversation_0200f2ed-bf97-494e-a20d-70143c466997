// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 此处后端没有提供注释 POST /api/v1/rump/task/module/orchestration/create */
export async function taskModuleOrchestrationCreate(
  body: API.TaskModuleOrchestrationCURequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/task/module/orchestration/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/v1/rump/task/module/orchestration/detail/${param0} */
export async function taskModuleOrchestrationDetail(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.taskModuleOrchestrationDetailParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultTaskModuleOrchestrationVO>(
    `/api/v1/rump/task/module/orchestration/detail/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 POST /api/v1/rump/task/module/orchestration/execute/${param0} */
export async function taskModuleOrchestrationExecute(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.taskModuleOrchestrationExecuteParams,
  body: API.TaskModuleOrchestrationExecuteRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultIdVO>(`/api/v1/rump/task/module/orchestration/execute/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/v1/rump/task/module/orchestration/page */
export async function taskModuleOrchestrationPage(
  body: API.TaskModulePageRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultPageVOTaskModuleOrchestrationPageVO>(
    '/api/v1/rump/task/module/orchestration/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 POST /api/v1/rump/task/module/orchestration/pre/execute/${param0} */
export async function taskModuleOrchestrationPreTaskExecute(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.taskModuleOrchestrationPreTaskExecuteParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultIdVO>(`/api/v1/rump/task/module/orchestration/pre/execute/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/v1/rump/task/module/orchestration/result/${param0} */
export async function taskModuleOrchestrationResult(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.taskModuleOrchestrationResultParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultTaskModuleOrchestrationExecuteVO>(
    `/api/v1/rump/task/module/orchestration/result/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 POST /api/v1/rump/task/module/orchestration/result/page/${param0} */
export async function taskModuleOrchestrationResultPage(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.taskModuleOrchestrationResultPageParams,
  body: API.TaskModuleOrchestrationResultPageRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultPageVOTaskModuleOrchestrationExecutePageVO>(
    `/api/v1/rump/task/module/orchestration/result/page/${param0}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      params: { ...queryParams },
      data: body,
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 POST /api/v1/rump/task/module/orchestration/update/${param0} */
export async function taskModuleOrchestrationUpdate(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.taskModuleOrchestrationUpdateParams,
  body: API.TaskModuleOrchestrationCURequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/api/v1/rump/task/module/orchestration/update/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}
