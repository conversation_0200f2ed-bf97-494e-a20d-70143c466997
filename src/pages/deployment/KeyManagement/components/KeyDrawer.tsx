import { KEY_TYPES } from '@/enums';
import {
  hostSshKeyCreate,
  hostSshKeyFindById,
  hostSshKeyUpdateById,
} from '@/services/http/hostSshKey';
import { requiredRule } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import {
  DrawerForm,
  DrawerFormProps,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message } from 'antd';
import React, { FC, useMemo, useRef } from 'react';

const KeyDrawer: FC<
  DrawerFormProps & {
    type?: string;
    onSuccess?: () => void;
    children?: React.ReactNode;
    id?: string;
  }
> = ({ type = 'create', onSuccess, id, ...restProps }) => {
  const formRef = useRef<ProFormInstance>();
  const { run } = useRequest(() => hostSshKeyFindById({ id: id! }), {
    manual: true,
    onSuccess: (res) => {
      if (res?.data) formRef.current?.setFieldsValue(res.data);
    },
    formatResult: (res) => res,
  });
  const trigger = useMemo(() => {
    return type === 'create' ? (
      <Button key="primary" type="primary" icon={<PlusOutlined />}>
        新建密钥
      </Button>
    ) : (
      <a>{type}</a>
    );
  }, [type]);
  return (
    <DrawerForm
      formRef={formRef}
      width={500}
      title={type === 'create' ? '新建密钥' : '编辑密钥'}
      trigger={trigger}
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      onOpenChange={(visible) => {
        if (visible && id) run();
      }}
      onFinish={async (values) => {
        const res =
          type === 'create'
            ? await hostSshKeyCreate(values)
            : await hostSshKeyUpdateById({ id: values?.id }, values);
        const success = res.data;
        if (success) {
          message.success('操作成功！');
          onSuccess?.();
        }
        return success;
      }}
      {...restProps}
    >
      <div className="rk-none">
        <ProFormText name="id" />
      </div>
      <ProFormText name="name" label="密钥名称" rules={[requiredRule]} />
      <ProFormSelect name="keyType" label="密钥类型" options={KEY_TYPES} rules={[requiredRule]} />
      <ProFormTextArea
        label="私钥"
        allowClear={true}
        name="privateKey"
        fieldProps={{ autoSize: { minRows: 4, maxRows: 8 } }}
        rules={[requiredRule]}
      />
      <ProFormTextArea
        label="公钥"
        allowClear={true}
        name="publicKey"
        fieldProps={{ autoSize: { minRows: 4, maxRows: 8 } }}
        rules={[requiredRule]}
      />
    </DrawerForm>
  );
};

export default KeyDrawer;
