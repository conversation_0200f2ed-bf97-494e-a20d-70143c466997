import { <PERSON><PERSON>, ConfigProvider, Typography } from 'antd';
import classNames from 'classnames/bind';
import { FC } from 'react';
import styles from './index.less';
const cx = classNames.bind(styles);

const TypeCard: FC<{ name: string; avatar: string; checked: boolean; onClick?: () => void }> = ({
  name,
  avatar,
  checked,
  onClick,
}) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Typography: {
            sizeMarginHeadingVerticalEnd: 0,
          },
        },
      }}
    >
      <div
        className={cx('card', {
          active: checked,
        })}
        onClick={onClick}
      >
        <Avatar src={avatar} size={48} shape="square" />
        <Typography.Title level={4}>{name}</Typography.Title>
      </div>
    </ConfigProvider>
  );
};

export default TypeCard;
