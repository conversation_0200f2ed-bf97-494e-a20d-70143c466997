import { history } from '@umijs/max';
import { Skeleton, Typography } from 'antd';
import classNames from 'classnames/bind';
import React from 'react';
import styles from './index.less';

const { Text, Title, Paragraph } = Typography;

const cx = classNames.bind(styles);

const COLOR_MAP: Record<string, string> = {
  '0': '',
  '1': 'linear-gradient(135deg, #fff 0%, rgba(45,183,245,0.45) 100%)',
  '2': 'linear-gradient(135deg, #fff 0%, rgba(251,210,106,.8) 100%)',
  '3': 'linear-gradient(135deg, #fff 0%, rgba(255,122,69,1) 100%)',
  '4': 'linear-gradient(135deg, #fff 0%, rgba(230,0,0,.9) 100%)',
  '5': 'linear-gradient(135deg, #fff 0%, rgba(144,0,33,.9) 100%)',
};

export type DataCardProps = {
  type?: string;
  title: string;
  value1?: string | number;
  value2?: string | number;
  loading?: boolean;
  id: string;
  extraInfo?: Record<string, any>[];
};

const DataCard: React.FC<DataCardProps> = ({
  type,
  title,
  value1 = 0,
  value2 = 0,
  loading,
  id,
  extraInfo,
}) => {
  const handleClick = () => {
    history.push(`/monitor/statistical-view/monitor/${id}`);
  };
  return (
    <div
      className={cx('parent')}
      style={{
        // @ts-ignore
        '--card-bg': COLOR_MAP[type],
      }}
      onClick={handleClick}
    >
      <div className={cx('card')}>
        <div className={cx('glass')} />
        <div className={cx('content')}>
          <Skeleton
            active
            paragraph={{
              rows: 2,
            }}
            loading={loading}
          >
            <Title
              level={4}
              ellipsis={{
                rows: 1,
              }}
            >
              {title}
            </Title>
            <Text type="secondary">健康度：</Text>
            <Text strong>{value1}%</Text>
            <br />
            <Text type="secondary">告警数量：</Text>
            <Text strong>{value2}</Text>
            {extraInfo?.map((item) => (
              <Paragraph key={item.itemid} ellipsis={{ rows: 1 }} style={{ marginBottom: 0 }}>
                <Text type="secondary">{item.name}：</Text>
                <Text strong>{item.lastvalue}</Text>
              </Paragraph>
            ))}
          </Skeleton>
        </div>
      </div>
    </div>
  );
};

export default DataCard;
