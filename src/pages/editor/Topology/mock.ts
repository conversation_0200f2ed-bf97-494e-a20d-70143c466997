export const data = {
  cells: [
    {
      position: {
        x: -600,
        y: -280,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10144_1',
            group: 'left',
          },
          {
            id: 'port_10144_2',
            group: 'right',
          },
        ],
      },
      id: '963c58e7-21ff-4122-a575-0ae94c3d6d20',
      data: {
        hostid: '10144',
        name: '100.121',
        status: '1',
        problem: false,
      },
      zIndex: 1,
    },
    {
      position: {
        x: -460,
        y: -280,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10123_1',
            group: 'left',
          },
          {
            id: 'port_10123_2',
            group: 'right',
          },
        ],
      },
      id: 'eca85bef-1c0a-497b-9e2b-cc39edcc0f55',
      data: {
        hostid: '10123',
        name: '*************',
        status: '0',
        problem: true,
      },
      zIndex: 2,
    },
    {
      position: {
        x: -320,
        y: 40,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10125_1',
            group: 'left',
          },
          {
            id: 'port_10125_2',
            group: 'right',
          },
        ],
      },
      id: 'a0631ea0-32ad-43f4-add6-3d32c5b38973',
      data: {
        hostid: '10125',
        name: '*************',
        status: '0',
        problem: true,
      },
      zIndex: 3,
    },
    {
      position: {
        x: -420,
        y: -60,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10124_1',
            group: 'left',
          },
          {
            id: 'port_10124_2',
            group: 'right',
          },
        ],
      },
      id: 'ae9d1d85-266d-41d9-aedc-428c556007d0',
      data: {
        hostid: '10124',
        name: '*************',
        status: '0',
        problem: true,
      },
      zIndex: 4,
    },
    {
      position: {
        x: -520,
        y: 20,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10273_1',
            group: 'left',
          },
          {
            id: 'port_10273_2',
            group: 'right',
          },
        ],
      },
      id: 'a338ca11-96ec-4137-a8e3-d7be821520ca',
      data: {
        hostid: '10273',
        name: '5A外网门户.wls.**************.7001.AdminServer',
        status: '0',
        problem: true,
      },
      zIndex: 5,
    },
    {
      position: {
        x: -220,
        y: -140,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10274_1',
            group: 'left',
          },
          {
            id: 'port_10274_2',
            group: 'right',
          },
        ],
      },
      id: '844f1c4a-c67f-4e08-b383-8c42de4696c2',
      data: {
        hostid: '10274',
        name: '5A外网门户.wls.**************.8001.ms1',
        status: '0',
        problem: true,
      },
      zIndex: 6,
    },
    {
      position: {
        x: -600,
        y: -60,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10421_1',
            group: 'left',
          },
          {
            id: 'port_10421_2',
            group: 'right',
          },
        ],
      },
      id: '3c7a8fbe-e677-4173-bc0c-9dcb433d9f2b',
      data: {
        hostid: '10421',
        name: '7.9test',
        status: '1',
        problem: false,
      },
      zIndex: 7,
    },
    {
      position: {
        x: -260,
        y: -240,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10199_1',
            group: 'left',
          },
          {
            id: 'port_10199_2',
            group: 'right',
          },
        ],
      },
      id: '2e6e739a-fa4d-4492-981d-5007e5812183',
      data: {
        hostid: '10199',
        name: 'AD域win2012-100.41',
        status: '1',
        problem: false,
      },
      zIndex: 8,
    },
    {
      position: {
        x: -320,
        y: 140,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10136_1',
            group: 'left',
          },
          {
            id: 'port_10136_2',
            group: 'right',
          },
        ],
      },
      id: '4692b325-8af5-41c0-8511-d5669205a14a',
      data: {
        hostid: '10136',
        name: 'AD域备win2012-100.42',
        status: '1',
        problem: false,
      },
      zIndex: 9,
    },
    {
      position: {
        x: -420,
        y: -160,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10185_1',
            group: 'left',
          },
          {
            id: 'port_10185_2',
            group: 'right',
          },
        ],
      },
      id: 'c13578d8-0996-484b-bf8f-1ea68f88c6e6',
      data: {
        hostid: '10185',
        name: 'Beian-win7-32',
        status: '1',
        problem: false,
      },
      zIndex: 10,
    },
    {
      position: {
        x: -580,
        y: 140,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10257_1',
            group: 'left',
          },
          {
            id: 'port_10257_2',
            group: 'right',
          },
        ],
      },
      id: '7798be3c-5760-47c9-938c-2839c9306ed4',
      data: {
        hostid: '10257',
        name: 'B代支付系统.bes.**************.16600.ser1',
        status: '0',
        problem: true,
      },
      zIndex: 11,
    },
    {
      position: {
        x: -460,
        y: 100,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10393_1',
            group: 'left',
          },
          {
            id: 'port_10393_2',
            group: 'right',
          },
        ],
      },
      id: 'd68b97ff-92d1-429e-a0f9-639dc2378a5f',
      data: {
        hostid: '10393',
        name: 'Evil_host',
        status: '0',
        problem: true,
      },
      zIndex: 12,
    },
    {
      position: {
        x: -260,
        y: -40,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10412_1',
            group: 'left',
          },
          {
            id: 'port_10412_2',
            group: 'right',
          },
        ],
      },
      id: '30445b12-ead2-4459-a6bb-81d0a1c7bd8a',
      data: {
        hostid: '10412',
        name: 'Evil_host_import',
        status: '0',
        problem: false,
      },
      zIndex: 13,
    },
    {
      position: {
        x: -120,
        y: -300,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10222_1',
            group: 'left',
          },
          {
            id: 'port_10222_2',
            group: 'right',
          },
        ],
      },
      id: 'e01b7990-f117-4341-ac76-8de582c3d3d5',
      data: {
        hostid: '10222',
        name: 'Lxtest-rhel6.8-**************',
        status: '1',
        problem: false,
      },
      zIndex: 14,
    },
    {
      position: {
        x: -120,
        y: -20,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10149_1',
            group: 'left',
          },
          {
            id: 'port_10149_2',
            group: 'right',
          },
        ],
      },
      id: '7c2ad78b-bd26-4ecc-923a-9205ee30aba1',
      data: {
        hostid: '10149',
        name: 'MySQL主备自动切换实验测试-01-************',
        status: '1',
        problem: false,
      },
      zIndex: 15,
    },
    {
      position: {
        x: -420,
        y: 280,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10188_1',
            group: 'left',
          },
          {
            id: 'port_10188_2',
            group: 'right',
          },
        ],
      },
      id: '23241413-fe09-47d7-b771-ff346d8a32da',
      data: {
        hostid: '10188',
        name: 'MySQL主备自动切换实验测试-02-************',
        status: '1',
        problem: false,
      },
      zIndex: 16,
    },
    {
      position: {
        x: -160,
        y: 280,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10192_1',
            group: 'left',
          },
          {
            id: 'port_10192_2',
            group: 'right',
          },
        ],
      },
      id: 'f8119e5c-9177-4c2c-85fb-b9b2df3e3dae',
      data: {
        hostid: '10192',
        name: 'MySQL主备自动切换实验测试-03-*************',
        status: '1',
        problem: false,
      },
      zIndex: 17,
    },
    {
      position: {
        x: -200,
        y: 140,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10153_1',
            group: 'left',
          },
          {
            id: 'port_10153_2',
            group: 'right',
          },
        ],
      },
      id: '466dfa42-815d-46a1-bb80-e702ccf24509',
      data: {
        hostid: '10153',
        name: 'OA',
        status: '1',
        problem: false,
      },
      zIndex: 18,
    },
    {
      position: {
        x: -420,
        y: 200,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10181_1',
            group: 'left',
          },
          {
            id: 'port_10181_2',
            group: 'right',
          },
        ],
      },
      id: 'c18bfd86-dfbf-4690-a0ab-635cde4084a5',
      data: {
        hostid: '10181',
        name: 'Oracle linux 7.7',
        status: '1',
        problem: false,
      },
      zIndex: 19,
    },
    {
      position: {
        x: -100,
        y: 100,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10415_1',
            group: 'left',
          },
          {
            id: 'port_10415_2',
            group: 'right',
          },
        ],
      },
      id: 'f98533a2-59cb-4f4f-ac73-0c87a46a6b27',
      data: {
        hostid: '10415',
        name: 'RHEL7.9',
        status: '1',
        problem: false,
      },
      zIndex: 20,
    },
    {
      position: {
        x: -520,
        y: 300,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10417_1',
            group: 'left',
          },
          {
            id: 'port_10417_2',
            group: 'right',
          },
        ],
      },
      id: '56f59709-79a7-43a6-a4df-6505700f6463',
      data: {
        hostid: '10417',
        name: 'RK-19C-**************',
        status: '1',
        problem: false,
      },
      zIndex: 21,
    },
    {
      position: {
        x: -280,
        y: 220,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10307_1',
            group: 'left',
          },
          {
            id: 'port_10307_2',
            group: 'right',
          },
        ],
      },
      id: 'b9462192-1ada-44a1-ad9e-03e56212485d',
      data: {
        hostid: '10307',
        name: 'RK-19C-RAC-**************',
        status: '1',
        problem: false,
      },
      zIndex: 22,
    },
    {
      position: {
        x: -20,
        y: -60,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10308_1',
            group: 'left',
          },
          {
            id: 'port_10308_2',
            group: 'right',
          },
        ],
      },
      id: '18b455fd-fa68-4198-a830-985c4546d853',
      data: {
        hostid: '10308',
        name: 'RK-19C-RAC-**************',
        status: '1',
        problem: false,
      },
      zIndex: 23,
    },
    {
      position: {
        x: -420,
        y: 20,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10261_1',
            group: 'left',
          },
          {
            id: 'port_10261_2',
            group: 'right',
          },
        ],
      },
      id: '6e1cb0b8-08e5-428e-b499-eda51b8d2608',
      data: {
        hostid: '10261',
        name: 'RK-ACCP-kylinv10sp2-**************',
        status: '1',
        problem: false,
      },
      zIndex: 24,
    },
    {
      position: {
        x: -20,
        y: 240,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10342_1',
            group: 'left',
          },
          {
            id: 'port_10342_2',
            group: 'right',
          },
        ],
      },
      id: '92c2e5b1-9d93-4f78-8ec3-8600c19b3dde',
      data: {
        hostid: '10342',
        name: 'RK-OPM测试和演示环境-CENTOS7.9-06',
        status: '1',
        problem: false,
      },
      zIndex: 25,
    },
    {
      position: {
        x: 20,
        y: 80,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10310_1',
            group: 'left',
          },
          {
            id: 'port_10310_2',
            group: 'right',
          },
        ],
      },
      id: '399c3854-a5bc-48ae-b4dc-a6824c42543c',
      data: {
        hostid: '10310',
        name: 'RK-MYSQL MGR-**************',
        status: '1',
        problem: false,
      },
      zIndex: 26,
    },
    {
      position: {
        x: -760,
        y: 40,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10311_1',
            group: 'left',
          },
          {
            id: 'port_10311_2',
            group: 'right',
          },
        ],
      },
      id: 'b97f05f6-11d9-43f1-9a70-02acdff9a835',
      data: {
        hostid: '10311',
        name: 'RK-MYSQL MGR-**************',
        status: '1',
        problem: false,
      },
      zIndex: 27,
    },
    {
      position: {
        x: -760,
        y: 220,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10312_1',
            group: 'left',
          },
          {
            id: 'port_10312_2',
            group: 'right',
          },
        ],
      },
      id: '6c0af67c-6e2f-4341-b689-9b5acdf21719',
      data: {
        hostid: '10312',
        name: 'RK-MYSQL MGR-**************',
        status: '1',
        problem: false,
      },
      zIndex: 28,
    },
    {
      position: {
        x: -760,
        y: 320,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10249_1',
            group: 'left',
          },
          {
            id: 'port_10249_2',
            group: 'right',
          },
        ],
      },
      id: '06dcd35c-0334-4830-b896-73a800dd1ec7',
      data: {
        hostid: '10249',
        name: 'RK-OA-**************',
        status: '1',
        problem: false,
      },
      zIndex: 29,
    },
    {
      position: {
        x: -1000,
        y: 180,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10351_1',
            group: 'left',
          },
          {
            id: 'port_10351_2',
            group: 'right',
          },
        ],
      },
      id: 'efb5fad0-5d2e-4239-b14d-b6125b752f45',
      data: {
        hostid: '10351',
        name: 'RK-OPM测试和演示环境-SUSE15-12',
        status: '1',
        problem: false,
      },
      zIndex: 30,
    },
    {
      position: {
        x: -800,
        y: -60,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10345_1',
            group: 'left',
          },
          {
            id: 'port_10345_2',
            group: 'right',
          },
        ],
      },
      id: '0283bc62-7a75-4ddf-94b4-79405d89ea5c',
      data: {
        hostid: '10345',
        name: 'RK-OPM测试和演示环境-CENTOS8.5-09',
        status: '1',
        problem: false,
      },
      zIndex: 31,
    },
    {
      position: {
        x: -660,
        y: 60,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10343_1',
            group: 'left',
          },
          {
            id: 'port_10343_2',
            group: 'right',
          },
        ],
      },
      id: '118d4196-8060-478f-b13e-6fff305f5a78',
      data: {
        hostid: '10343',
        name: 'RK-OPM测试和演示环境-CENTOS7.9-07',
        status: '1',
        problem: false,
      },
      zIndex: 32,
    },
    {
      position: {
        x: -680,
        y: 180,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10344_1',
            group: 'left',
          },
          {
            id: 'port_10344_2',
            group: 'right',
          },
        ],
      },
      id: '0e03cee9-a620-4018-83a3-50cf475d3100',
      data: {
        hostid: '10344',
        name: 'RK-OPM测试和演示环境-CENTOS8.5-08',
        status: '1',
        problem: false,
      },
      zIndex: 33,
    },
    {
      position: {
        x: -620,
        y: 280,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10373_1',
            group: 'left',
          },
          {
            id: 'port_10373_2',
            group: 'right',
          },
        ],
      },
      id: 'b5049d85-58cc-4851-bc35-e635fe2d0954',
      data: {
        hostid: '10373',
        name: 'RK-OPM测试和演示环境-SUSE12SP4-74',
        status: '1',
        problem: false,
      },
      zIndex: 34,
    },
    {
      position: {
        x: -820,
        y: 120,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10339_1',
            group: 'left',
          },
          {
            id: 'port_10339_2',
            group: 'right',
          },
        ],
      },
      id: '08c15839-bd69-4f07-9cef-d99a323e4089',
      data: {
        hostid: '10339',
        name: 'RK-OPM测试和演示环境-RHEL8.5-04',
        status: '1',
        problem: false,
      },
      zIndex: 35,
    },
    {
      position: {
        x: -540,
        y: 220,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10337_1',
            group: 'left',
          },
          {
            id: 'port_10337_2',
            group: 'right',
          },
        ],
      },
      id: 'c8bd4c84-44a7-4230-aff2-b8d9b22f8fc4',
      data: {
        hostid: '10337',
        name: 'RK-OPM测试和演示环境-RHEL7.9-02',
        status: '1',
        problem: false,
      },
      zIndex: 36,
    },
    {
      position: {
        x: -660,
        y: -20,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10341_1',
            group: 'left',
          },
          {
            id: 'port_10341_2',
            group: 'right',
          },
        ],
      },
      id: 'bb4ae878-2428-47ca-aaf3-2df932a5b7d0',
      data: {
        hostid: '10341',
        name: 'RK-OPM测试和演示环境-RHEL7.9-01',
        status: '1',
        problem: false,
      },
      zIndex: 37,
    },
    {
      position: {
        x: -760,
        y: -120,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10340_1',
            group: 'left',
          },
          {
            id: 'port_10340_2',
            group: 'right',
          },
        ],
      },
      id: '8c806e0e-a2c0-4a29-b1a6-59aab10477c5',
      data: {
        hostid: '10340',
        name: 'RK-OPM测试和演示环境-RHEL8.5-05',
        status: '1',
        problem: false,
      },
      zIndex: 38,
    },
    {
      position: {
        x: -780,
        y: -240,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10349_1',
            group: 'left',
          },
          {
            id: 'port_10349_2',
            group: 'right',
          },
        ],
      },
      id: '5049c93c-8490-4562-9cd2-7a0ee1d4ea7b',
      data: {
        hostid: '10349',
        name: 'RK-OPM测试和演示环境-SUSE12SP4-11',
        status: '1',
        problem: false,
      },
      zIndex: 39,
    },
    {
      position: {
        x: -720,
        y: -380,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10350_1',
            group: 'left',
          },
          {
            id: 'port_10350_2',
            group: 'right',
          },
        ],
      },
      id: '97397897-dcdb-4d2e-8f68-af1ed5191e3c',
      data: {
        hostid: '10350',
        name: 'RK-OPM测试和演示环境-SUSE15-13',
        status: '1',
        problem: false,
      },
      zIndex: 40,
    },
    {
      position: {
        x: -560,
        y: -360,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10314_1',
            group: 'left',
          },
          {
            id: 'port_10314_2',
            group: 'right',
          },
        ],
      },
      id: 'bca25c06-0a13-41fc-8e9b-9579b87223e7',
      data: {
        hostid: '10314',
        name: 'RK-ORACL19C-RAC-**************',
        status: '1',
        problem: false,
      },
      zIndex: 41,
    },
    {
      position: {
        x: -680,
        y: -300,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10193_1',
            group: 'left',
          },
          {
            id: 'port_10193_2',
            group: 'right',
          },
        ],
      },
      id: 'e0b3d479-6b56-42ac-8a44-7a489ced811f',
      data: {
        hostid: '10193',
        name: 'RK-RHEL-8.5-GUI-TMP',
        status: '1',
        problem: false,
      },
      zIndex: 42,
    },
    {
      position: {
        x: -640,
        y: -180,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10247_1',
            group: 'left',
          },
          {
            id: 'port_10247_2',
            group: 'right',
          },
        ],
      },
      id: '8412f077-d9ae-4227-80d6-3cab38f46285',
      data: {
        hostid: '10247',
        name: 'RK-RHEL-DNS-**************',
        status: '1',
        problem: false,
      },
      zIndex: 43,
    },
    {
      position: {
        x: -440,
        y: -380,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10346_1',
            group: 'left',
          },
          {
            id: 'port_10346_2',
            group: 'right',
          },
        ],
      },
      id: '231aa694-a8d1-4bc4-9486-9461fb729bf3',
      data: {
        hostid: '10346',
        name: 'RK-SUSE12SP4-tmp',
        status: '1',
        problem: false,
      },
      zIndex: 44,
    },
    {
      position: {
        x: -600,
        y: -440,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10201_1',
            group: 'left',
          },
          {
            id: 'port_10201_2',
            group: 'right',
          },
        ],
      },
      id: 'a8604ef2-575a-4d55-8ab6-719ce3ec8ba7',
      data: {
        hostid: '10201',
        name: 'RK-RHEL7.5-TMP',
        status: '1',
        problem: false,
      },
      zIndex: 45,
    },
    {
      position: {
        x: -340,
        y: -300,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10315_1',
            group: 'left',
          },
          {
            id: 'port_10315_2',
            group: 'right',
          },
        ],
      },
      id: '62253ed2-809b-4738-a8b2-d2cdc2380aa9',
      data: {
        hostid: '10315',
        name: 'RK-ORACL19C-RAC-**************',
        status: '1',
        problem: false,
      },
      zIndex: 46,
    },
    {
      position: {
        x: -300,
        y: -380,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10316_1',
            group: 'left',
          },
          {
            id: 'port_10316_2',
            group: 'right',
          },
        ],
      },
      id: '90b08dcf-cfee-4530-b0ec-a19998c77d67',
      data: {
        hostid: '10316',
        name: 'RK-ORACL19C-RAC-**************',
        status: '1',
        problem: false,
      },
      zIndex: 47,
    },
    {
      position: {
        x: -460,
        y: -500,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10352_1',
            group: 'left',
          },
          {
            id: 'port_10352_2',
            group: 'right',
          },
        ],
      },
      id: '99bb4f8f-8d85-456f-8216-c7f4e41d20dd',
      data: {
        hostid: '10352',
        name: 'RK-SUSE15SP1-tmp',
        status: '1',
        problem: false,
      },
      zIndex: 48,
    },
    {
      position: {
        x: -200,
        y: -400,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10322_1',
            group: 'left',
          },
          {
            id: 'port_10322_2',
            group: 'right',
          },
        ],
      },
      id: '97a16c2f-d04c-413e-b42e-47606e55fc6b',
      data: {
        hostid: '10322',
        name: 'RK-WIN2022-Devops01',
        status: '1',
        problem: false,
      },
      zIndex: 49,
    },
    {
      position: {
        x: -40,
        y: -180,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10140_1',
            group: 'left',
          },
          {
            id: 'port_10140_2',
            group: 'right',
          },
        ],
      },
      id: 'f1649501-e45b-403d-b142-6c5975241ad5',
      data: {
        hostid: '10140',
        name: 'RK-WIN2022-TEMP',
        status: '1',
        problem: false,
      },
      zIndex: 50,
    },
    {
      position: {
        x: -300,
        y: -480,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10208_1',
            group: 'left',
          },
          {
            id: 'port_10208_2',
            group: 'right',
          },
        ],
      },
      id: '7a8bb716-3599-440a-bd81-2aa6f1b061e5',
      data: {
        hostid: '10208',
        name: 'RK-centos-7.9-tmp',
        status: '1',
        problem: false,
      },
      zIndex: 51,
    },
    {
      position: {
        x: -100,
        y: -460,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10189_1',
            group: 'left',
          },
          {
            id: 'port_10189_2',
            group: 'right',
          },
        ],
      },
      id: '6b7d35a2-101f-423f-957c-5c95584efdfb',
      data: {
        hostid: '10189',
        name: 'RK-centos-8.5-tmp',
        status: '1',
        problem: false,
      },
      zIndex: 52,
    },
    {
      position: {
        x: -220,
        y: -320,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10359_1',
            group: 'left',
          },
          {
            id: 'port_10359_2',
            group: 'right',
          },
        ],
      },
      id: 'eb9f6281-1cfb-41b0-9a35-9387ef9d0846',
      data: {
        hostid: '10359',
        name: 'RK-diaoyuceshi-**************',
        status: '1',
        problem: false,
      },
      zIndex: 53,
    },
    {
      position: {
        x: -40,
        y: -360,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10246_1',
            group: 'left',
          },
          {
            id: 'port_10246_2',
            group: 'right',
          },
        ],
      },
      id: 'ebf01829-9a8f-42a5-83fb-689531f0bb78',
      data: {
        hostid: '10246',
        name: 'RK-jiankong.**************',
        status: '1',
        problem: false,
      },
      zIndex: 54,
    },
    {
      position: {
        x: -200,
        y: -540,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10434_1',
            group: 'left',
          },
          {
            id: 'port_10434_2',
            group: 'right',
          },
        ],
      },
      id: '184bb59b-5711-448b-833e-f0231cdc24d6',
      data: {
        hostid: '10434',
        name: 'RK-hexin',
        status: '0',
        problem: true,
      },
      zIndex: 55,
    },
    {
      position: {
        x: -360,
        y: -560,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10128_1',
            group: 'left',
          },
          {
            id: 'port_10128_2',
            group: 'right',
          },
        ],
      },
      id: '8631bd54-2fb9-4c6f-bf4d-9d6d9fc62c23',
      data: {
        hostid: '10128',
        name: 'RK-iso_yum_file-server',
        status: '1',
        problem: false,
      },
      zIndex: 56,
    },
    {
      position: {
        x: -40,
        y: -540,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10169_1',
            group: 'left',
          },
          {
            id: 'port_10169_2',
            group: 'right',
          },
        ],
      },
      id: '20da29c0-5d63-45f6-b7fa-8587efcfde8d',
      data: {
        hostid: '10169',
        name: 'RK-jiankong-01-***************',
        status: '1',
        problem: false,
      },
      zIndex: 57,
    },
    {
      position: {
        x: 20,
        y: -420,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10168_1',
            group: 'left',
          },
          {
            id: 'port_10168_2',
            group: 'right',
          },
        ],
      },
      id: '05ef4226-91df-4074-b594-3c4490661cd6',
      data: {
        hostid: '10168',
        name: 'RK-jiankong-02-***************',
        status: '1',
        problem: false,
      },
      zIndex: 58,
    },
    {
      position: {
        x: -320,
        y: -180,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10306_1',
            group: 'left',
          },
          {
            id: 'port_10306_2',
            group: 'right',
          },
        ],
      },
      id: '8baf670c-fd24-4c0f-ae91-59f0213b9f65',
      data: {
        hostid: '10306',
        name: 'RK-oraclelinux7.9-tmp',
        status: '1',
        problem: false,
      },
      zIndex: 59,
    },
    {
      position: {
        x: -40,
        y: -260,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10221_1',
            group: 'left',
          },
          {
            id: 'port_10221_2',
            group: 'right',
          },
        ],
      },
      id: '4562e2d1-a9b0-4054-95d5-329cbb4226ed',
      data: {
        hostid: '10221',
        name: 'RK-rhel-6.8-tmp',
        status: '1',
        problem: false,
      },
      zIndex: 60,
    },
    {
      position: {
        x: -160,
        y: -200,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10332_1',
            group: 'left',
          },
          {
            id: 'port_10332_2',
            group: 'right',
          },
        ],
      },
      id: 'e112db98-d78e-4650-a713-2e11342df1f2',
      data: {
        hostid: '10332',
        name: 'RK-rhel7.5-GUI-TMP',
        status: '1',
        problem: false,
      },
      zIndex: 61,
    },
    {
      position: {
        x: -120,
        y: -120,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10327_1',
            group: 'left',
          },
          {
            id: 'port_10327_2',
            group: 'right',
          },
        ],
      },
      id: 'e489919e-0cc7-42ed-b57c-dc5309c1e0b2',
      data: {
        hostid: '10327',
        name: 'RK-rhle6.4-tmp',
        status: '1',
        problem: false,
      },
      zIndex: 62,
    },
    {
      position: {
        x: -400,
        y: -440,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10156_1',
            group: 'left',
          },
          {
            id: 'port_10156_2',
            group: 'right',
          },
        ],
      },
      id: '0e85bf4f-9b98-461e-9e75-974e82d1b166',
      data: {
        hostid: '10156',
        name: 'RK-win2012-tmp',
        status: '1',
        problem: false,
      },
      zIndex: 63,
    },
    {
      position: {
        x: -640,
        y: -580,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10187_1',
            group: 'left',
          },
          {
            id: 'port_10187_2',
            group: 'right',
          },
        ],
      },
      id: 'b9d99a6d-e07e-4ca4-ad33-dea1d48e82bf',
      data: {
        hostid: '10187',
        name: 'RK-win2016-Devops02',
        status: '1',
        problem: false,
      },
      zIndex: 64,
    },
    {
      position: {
        x: -580,
        y: -540,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10427_1',
            group: 'left',
          },
          {
            id: 'port_10427_2',
            group: 'right',
          },
        ],
      },
      id: '1094e6a5-c864-42b3-ae37-2e19623c80fe',
      data: {
        hostid: '10427',
        name: 'RK_19C-RAC_**************',
        status: '1',
        problem: false,
      },
      zIndex: 65,
    },
    {
      position: {
        x: -680,
        y: -640,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10428_1',
            group: 'left',
          },
          {
            id: 'port_10428_2',
            group: 'right',
          },
        ],
      },
      id: 'ba721a2f-02e0-4522-8b4c-e7937aee3b25',
      data: {
        hostid: '10428',
        name: 'RK_19C-RAC_**************',
        status: '1',
        problem: false,
      },
      zIndex: 66,
    },
    {
      position: {
        x: -500,
        y: -640,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10418_1',
            group: 'left',
          },
          {
            id: 'port_10418_2',
            group: 'right',
          },
        ],
      },
      id: '846f17f2-11b6-4b37-b1b1-b5f0b081ec80',
      data: {
        hostid: '10418',
        name: 'RK_19C-RAC_Storage',
        status: '1',
        problem: false,
      },
      zIndex: 67,
    },
    {
      position: {
        x: -320,
        y: -620,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10420_1',
            group: 'left',
          },
          {
            id: 'port_10420_2',
            group: 'right',
          },
        ],
      },
      id: '5b3a2680-43a4-426c-ac93-ab7425773147',
      data: {
        hostid: '10420',
        name: 'RK_AOI_RHEL7.9',
        status: '1',
        problem: true,
      },
      zIndex: 68,
    },
    {
      position: {
        x: -1080,
        y: -80,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10379_1',
            group: 'left',
          },
          {
            id: 'port_10379_2',
            group: 'right',
          },
        ],
      },
      id: 'e9a7c51c-b932-4036-8e2d-6eade87e2c40',
      data: {
        hostid: '10379',
        name: 'RK_OB_TEST001_192.168.35.101',
        status: '1',
        problem: false,
      },
      zIndex: 69,
    },
    {
      position: {
        x: -920,
        y: -160,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10382_1',
            group: 'left',
          },
          {
            id: 'port_10382_2',
            group: 'right',
          },
        ],
      },
      id: 'e489792b-316f-4c9f-9632-b5e67076774e',
      data: {
        hostid: '10382',
        name: 'RK_OB_TEST002_192.168.35.102',
        status: '1',
        problem: false,
      },
      zIndex: 70,
    },
    {
      position: {
        x: -960,
        y: -360,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10381_1',
            group: 'left',
          },
          {
            id: 'port_10381_2',
            group: 'right',
          },
        ],
      },
      id: '7b121485-4008-440c-8a7e-e77467a9f9ac',
      data: {
        hostid: '10381',
        name: 'RK_OB_TEST003_192.168.35.103',
        status: '1',
        problem: false,
      },
      zIndex: 71,
    },
    {
      position: {
        x: 100,
        y: -220,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10380_1',
            group: 'left',
          },
          {
            id: 'port_10380_2',
            group: 'right',
          },
        ],
      },
      id: 'bd891217-0a25-4884-8c17-71ea23171826',
      data: {
        hostid: '10380',
        name: 'RK_OB_TEST004_192.168.35.104',
        status: '1',
        problem: false,
      },
      zIndex: 72,
    },
    {
      position: {
        x: -980,
        y: 100,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10148_1',
            group: 'left',
          },
          {
            id: 'port_10148_2',
            group: 'right',
          },
        ],
      },
      id: '59e54703-dec4-4292-b904-24a2a4dee5f1',
      data: {
        hostid: '10148',
        name: 'Rac-centos8.5-***********',
        status: '1',
        problem: false,
      },
      zIndex: 73,
    },
    {
      position: {
        x: -1000,
        y: 320,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10174_1',
            group: 'left',
          },
          {
            id: 'port_10174_2',
            group: 'right',
          },
        ],
      },
      id: '21fc2f99-7f04-4310-b678-f3426f362a05',
      data: {
        hostid: '10174',
        name: 'Rac-suse-***********',
        status: '1',
        problem: false,
      },
      zIndex: 74,
    },
    {
      position: {
        x: -1020,
        y: -220,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10134_1',
            group: 'left',
          },
          {
            id: 'port_10134_2',
            group: 'right',
          },
        ],
      },
      id: '13eacd7a-749e-40e8-9edc-dc754c8216e5',
      data: {
        hostid: '10134',
        name: 'Rac-suse-***********',
        status: '1',
        problem: false,
      },
      zIndex: 75,
    },
    {
      position: {
        x: -880,
        y: 20,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10138_1',
            group: 'left',
          },
          {
            id: 'port_10138_2',
            group: 'right',
          },
        ],
      },
      id: 'eb4ad184-154a-4433-9352-9cacede0e0b2',
      data: {
        hostid: '10138',
        name: 'Rac-suse-***********',
        status: '1',
        problem: false,
      },
      zIndex: 76,
    },
    {
      position: {
        x: -880,
        y: -260,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10196_1',
            group: 'left',
          },
          {
            id: 'port_10196_2',
            group: 'right',
          },
        ],
      },
      id: '096a367c-cf27-40db-b27a-f220f3ff6108',
      data: {
        hostid: '10196',
        name: 'Rac-suse-***********',
        status: '1',
        problem: false,
      },
      zIndex: 77,
    },
    {
      position: {
        x: -920,
        y: -460,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10157_1',
            group: 'left',
          },
          {
            id: 'port_10157_2',
            group: 'right',
          },
        ],
      },
      id: '0777bd03-23a1-4574-b6eb-68e30eadcf62',
      data: {
        hostid: '10157',
        name: 'Rac-suse-***********',
        status: '1',
        problem: false,
      },
      zIndex: 78,
    },
    {
      position: {
        x: -940,
        y: 240,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10203_1',
            group: 'left',
          },
          {
            id: 'port_10203_2',
            group: 'right',
          },
        ],
      },
      id: '617ea41f-b31c-4301-ab49-693003bc068e',
      data: {
        hostid: '10203',
        name: 'VM server',
        status: '1',
        problem: false,
      },
      zIndex: 79,
    },
    {
      position: {
        x: -1100,
        y: 80,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10154_1',
            group: 'left',
          },
          {
            id: 'port_10154_2',
            group: 'right',
          },
        ],
      },
      id: 'a09ae71c-616d-4ef9-bc59-1814f16386ef',
      data: {
        hostid: '10154',
        name: 'VMware vCenter Server7.0',
        status: '1',
        problem: false,
      },
      zIndex: 80,
    },
    {
      position: {
        x: -960,
        y: -560,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10431_1',
            group: 'left',
          },
          {
            id: 'port_10431_2',
            group: 'right',
          },
        ],
      },
      id: '36fbc767-f26f-48ba-8bd4-7cb96aa15449',
      data: {
        hostid: '10431',
        name: 'acacac',
        status: '0',
        problem: false,
      },
      zIndex: 81,
    },
    {
      position: {
        x: -840,
        y: -600,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10190_1',
            group: 'left',
          },
          {
            id: 'port_10190_2',
            group: 'right',
          },
        ],
      },
      id: '07abaca4-3d1b-42da-b6d1-14cd04ed2755',
      data: {
        hostid: '10190',
        name: 'ansible-tower',
        status: '1',
        problem: false,
      },
      zIndex: 82,
    },
    {
      position: {
        x: -1080,
        y: -420,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10389_1',
            group: 'left',
          },
          {
            id: 'port_10389_2',
            group: 'right',
          },
        ],
      },
      id: 'fc4f6a16-7142-4552-ae88-1d8093a8424d',
      data: {
        hostid: '10389',
        name: 'ansible-操控vcenter-server端',
        status: '1',
        problem: false,
      },
      zIndex: 83,
    },
    {
      position: {
        x: -580,
        y: -720,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10146_1',
            group: 'left',
          },
          {
            id: 'port_10146_2',
            group: 'right',
          },
        ],
      },
      id: '77891b2e-8f30-4bb0-a60a-cf8c8ed6477f',
      data: {
        hostid: '10146',
        name: 'ansible培训-*************',
        status: '1',
        problem: false,
      },
      zIndex: 84,
    },
    {
      position: {
        x: -900,
        y: 400,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10250_1',
            group: 'left',
          },
          {
            id: 'port_10250_2',
            group: 'right',
          },
        ],
      },
      id: '5df5206e-c63a-48d9-8c16-88495d11fb6e',
      data: {
        hostid: '10250',
        name: 'apache.**************.apa1',
        status: '0',
        problem: false,
      },
      zIndex: 85,
    },
    {
      position: {
        x: -1120,
        y: 220,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10161_1',
            group: 'left',
          },
          {
            id: 'port_10161_2',
            group: 'right',
          },
        ],
      },
      id: '44d86815-9eb6-4a16-ba87-c4e9a1299835',
      data: {
        hostid: '10161',
        name: 'centos76-tmp',
        status: '1',
        problem: false,
      },
      zIndex: 86,
    },
    {
      position: {
        x: -840,
        y: -360,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10202_1',
            group: 'left',
          },
          {
            id: 'port_10202_2',
            group: 'right',
          },
        ],
      },
      id: 'ba07bbd5-1b81-4150-b744-3f53f3cc49d1',
      data: {
        hostid: '10202',
        name: 'centos79-tmp',
        status: '1',
        problem: false,
      },
      zIndex: 87,
    },
    {
      position: {
        x: -200,
        y: -640,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10205_1',
            group: 'left',
          },
          {
            id: 'port_10205_2',
            group: 'right',
          },
        ],
      },
      id: '5edf3b98-cec4-4140-b9eb-18cbe8fde639',
      data: {
        hostid: '10205',
        name: 'centos8.5',
        status: '1',
        problem: false,
      },
      zIndex: 88,
    },
    {
      position: {
        x: -740,
        y: 420,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10133_1',
            group: 'left',
          },
          {
            id: 'port_10133_2',
            group: 'right',
          },
        ],
      },
      id: '109c7d71-1e4a-4d7c-8c3b-adef245c6be3',
      data: {
        hostid: '10133',
        name: 'centos84-tmp',
        status: '1',
        problem: false,
      },
      zIndex: 89,
    },
    {
      position: {
        x: -60,
        y: -640,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10155_1',
            group: 'left',
          },
          {
            id: 'port_10155_2',
            group: 'right',
          },
        ],
      },
      id: '43b65176-1882-425b-a4d1-0f064aee6230',
      data: {
        hostid: '10155',
        name: 'chengdong-win7-tiaoban',
        status: '1',
        problem: false,
      },
      zIndex: 90,
    },
    {
      position: {
        x: 120,
        y: -320,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10320_1',
            group: 'left',
          },
          {
            id: 'port_10320_2',
            group: 'right',
          },
        ],
      },
      id: 'ce9975f5-e0df-4585-92d6-abb40d78366d',
      data: {
        hostid: '10320',
        name: 'docker-autodeployment-**************',
        status: '1',
        problem: false,
      },
      zIndex: 91,
    },
    {
      position: {
        x: 100,
        y: -40,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10425_1',
            group: 'left',
          },
          {
            id: 'port_10425_2',
            group: 'right',
          },
        ],
      },
      id: 'f2840dc7-73f5-4a7b-992d-b32fadbfcf78',
      data: {
        hostid: '10425',
        name: 'dongwei_19cRAC_192.168.100.48',
        status: '1',
        problem: false,
      },
      zIndex: 92,
    },
    {
      position: {
        x: 200,
        y: 200,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10423_1',
            group: 'left',
          },
          {
            id: 'port_10423_2',
            group: 'right',
          },
        ],
      },
      id: '9b4b675b-0ecd-4564-8995-bf1abd4ae90d',
      data: {
        hostid: '10423',
        name: 'dongwei_19cRAC_**************',
        status: '1',
        problem: false,
      },
      zIndex: 93,
    },
    {
      position: {
        x: 200,
        y: -100,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10424_1',
            group: 'left',
          },
          {
            id: 'port_10424_2',
            group: 'right',
          },
        ],
      },
      id: '9e0418ed-c490-4745-9f3f-d25ab46cf555',
      data: {
        hostid: '10424',
        name: 'dongwei_19cRAC_192.168.100.49',
        status: '1',
        problem: false,
      },
      zIndex: 94,
    },
    {
      position: {
        x: 100,
        y: -500,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10426_1',
            group: 'left',
          },
          {
            id: 'port_10426_2',
            group: 'right',
          },
        ],
      },
      id: '3f44ce6d-3f3f-4d7a-ad58-b9b87d809060',
      data: {
        hostid: '10426',
        name: 'dongwei_19cRAC_**************',
        status: '1',
        problem: false,
      },
      zIndex: 95,
    },
    {
      position: {
        x: -540,
        y: 400,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10235_1',
            group: 'left',
          },
          {
            id: 'port_10235_2',
            group: 'right',
          },
        ],
      },
      id: 'e3772153-e77a-449f-87cb-67bd8c25121e',
      data: {
        hostid: '10235',
        name: 'hardware.*************',
        status: '0',
        problem: false,
      },
      zIndex: 96,
    },
    {
      position: {
        x: 120,
        y: -640,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10162_1',
            group: 'left',
          },
          {
            id: 'port_10162_2',
            group: 'right',
          },
        ],
      },
      id: '119c4b1f-0291-482e-a594-773ae291f6fe',
      data: {
        hostid: '10162',
        name: 'houtuo-centos8.5-test1',
        status: '1',
        problem: false,
      },
      zIndex: 97,
    },
    {
      position: {
        x: 260,
        y: -300,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10392_1',
            group: 'left',
          },
          {
            id: 'port_10392_2',
            group: 'right',
          },
        ],
      },
      id: 'b1cffe64-d7d4-4f94-b20d-6b653dceeb3d',
      data: {
        hostid: '10392',
        name: 'evil',
        status: '0',
        problem: true,
      },
      zIndex: 98,
    },
    {
      position: {
        x: 220,
        y: -480,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10175_1',
            group: 'left',
          },
          {
            id: 'port_10175_2',
            group: 'right',
          },
        ],
      },
      id: '844d91a2-abd7-458d-8e51-d4bead3bbad9',
      data: {
        hostid: '10175',
        name: 'houtuo-centos8.5-test2',
        status: '1',
        problem: false,
      },
      zIndex: 99,
    },
    {
      position: {
        x: -360,
        y: -720,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10355_1',
            group: 'left',
          },
          {
            id: 'port_10355_2',
            group: 'right',
          },
        ],
      },
      id: 'b06c2978-4d7d-46dc-ab36-c3481e4f7d2d',
      data: {
        hostid: '10355',
        name: 'huawei.*************',
        status: '0',
        problem: false,
      },
      zIndex: 100,
    },
    {
      position: {
        x: -340,
        y: 380,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10238_1',
            group: 'left',
          },
          {
            id: 'port_10238_2',
            group: 'right',
          },
        ],
      },
      id: 'bb9142be-6e3f-4425-98da-e8a1fc77dedf',
      data: {
        hostid: '10238',
        name: 'ibmds.***************.ds5020',
        status: '0',
        problem: false,
      },
      zIndex: 101,
    },
    {
      position: {
        x: 260,
        y: 60,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10253_1',
            group: 'left',
          },
          {
            id: 'port_10253_2',
            group: 'right',
          },
        ],
      },
      id: '635017cb-9ad5-4e9e-8274-12a039c131d9',
      data: {
        hostid: '10253',
        name: 'informix.**************.22345.demo_on',
        status: '0',
        problem: true,
      },
      zIndex: 102,
    },
    {
      position: {
        x: -700,
        y: -220,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10368_1',
            group: 'left',
          },
          {
            id: 'port_10368_2',
            group: 'right',
          },
        ],
      },
      id: '953da37c-9e53-4f8d-b860-61a66b68198f',
      data: {
        hostid: '10368',
        name: 'kdb.**************.rhel84',
        status: '0',
        problem: true,
      },
      zIndex: 103,
    },
    {
      position: {
        x: -520,
        y: -100,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10236_1',
            group: 'left',
          },
          {
            id: 'port_10236_2',
            group: 'right',
          },
        ],
      },
      id: '1c7c1d14-362a-4a28-b60f-ef92a6f3f35b',
      data: {
        hostid: '10236',
        name: 'linux.***************.rhel85',
        status: '0',
        problem: true,
      },
      zIndex: 104,
    },
    {
      position: {
        x: -460,
        y: -220,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10251_1',
            group: 'left',
          },
          {
            id: 'port_10251_2',
            group: 'right',
          },
        ],
      },
      id: 'fd78f643-c6dd-43fd-a160-ae4892fbd5de',
      data: {
        hostid: '10251',
        name: 'linux.**************.rhel84',
        status: '0',
        problem: false,
      },
      zIndex: 105,
    },
    {
      position: {
        x: -780,
        y: -440,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10225_1',
            group: 'left',
          },
          {
            id: 'port_10225_2',
            group: 'right',
          },
        ],
      },
      id: 'd36c5d03-8bc5-422d-bf78-ea7c52d26535',
      data: {
        hostid: '10225',
        name: 'linuxbk.***************',
        status: '1',
        problem: false,
      },
      zIndex: 106,
    },
    {
      position: {
        x: -820,
        y: -500,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10271_1',
            group: 'left',
          },
          {
            id: 'port_10271_2',
            group: 'right',
          },
        ],
      },
      id: '4cadf9c3-3f41-43f2-b68e-5096ac5db8b5',
      data: {
        hostid: '10271',
        name: 'linux.*************.ansibleTraining',
        status: '0',
        problem: true,
      },
      zIndex: 107,
    },
    {
      position: {
        x: -680,
        y: -440,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10237_1',
            group: 'left',
          },
          {
            id: 'port_10237_2',
            group: 'right',
          },
        ],
      },
      id: 'a3019161-aa97-4080-8ef5-7cbe34226969',
      data: {
        hostid: '10237',
        name: 'logmon.***************',
        status: '0',
        problem: true,
      },
      zIndex: 108,
    },
    {
      position: {
        x: -560,
        y: -200,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10086_1',
            group: 'left',
          },
          {
            id: 'port_10086_2',
            group: 'right',
          },
        ],
      },
      id: '447436fa-390a-43b3-a9a5-c49c57d52ff9',
      data: {
        hostid: '10086',
        name: 'montoolserver',
        status: '0',
        problem: true,
      },
      zIndex: 109,
    },
    {
      position: {
        x: -500,
        y: -440,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10378_1',
            group: 'left',
          },
          {
            id: 'port_10378_2',
            group: 'right',
          },
        ],
      },
      id: '9dff4464-5f32-409a-abd6-c4fa3d68247c',
      data: {
        hostid: '10378',
        name: 'logmon.***************',
        status: '0',
        problem: false,
      },
      zIndex: 110,
    },
    {
      position: {
        x: -720,
        y: -520,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10358_1',
            group: 'left',
          },
          {
            id: 'port_10358_2',
            group: 'right',
          },
        ],
      },
      id: '12fccab4-c84d-4a62-a898-924155cae4fc',
      data: {
        hostid: '10358',
        name: 'mysql.192.168.200.148_odbc',
        status: '0',
        problem: true,
      },
      zIndex: 111,
    },
    {
      position: {
        x: -500,
        y: -560,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10243_1',
            group: 'left',
          },
          {
            id: 'port_10243_2',
            group: 'right',
          },
        ],
      },
      id: '3a74ea1e-5c5d-4d01-a74c-f0729cfe7fc4',
      data: {
        hostid: '10243',
        name: 'oraclebk.***********',
        status: '0',
        problem: false,
      },
      zIndex: 112,
    },
    {
      position: {
        x: -200,
        y: -480,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10184_1',
            group: 'left',
          },
          {
            id: 'port_10184_2',
            group: 'right',
          },
        ],
      },
      id: '5cd2da7f-f574-483f-b900-2519e6507a18',
      data: {
        hostid: '10184',
        name: 'oracle数据库部署测试实验-01-************',
        status: '1',
        problem: false,
      },
      zIndex: 113,
    },
    {
      position: {
        x: -680,
        y: -100,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10139_1',
            group: 'left',
          },
          {
            id: 'port_10139_2',
            group: 'right',
          },
        ],
      },
      id: '638610ed-f147-45a2-824c-e241b51f83ad',
      data: {
        hostid: '10139',
        name: 'oracle数据库部署测试实验-02-************',
        status: '1',
        problem: false,
      },
      zIndex: 114,
    },
    {
      position: {
        x: -660,
        y: -380,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10195_1',
            group: 'left',
          },
          {
            id: 'port_10195_2',
            group: 'right',
          },
        ],
      },
      id: '77b87014-43e6-4044-8732-d5b5891119e1',
      data: {
        hostid: '10195',
        name: 'oracle数据库部署测试实验-03-************',
        status: '1',
        problem: false,
      },
      zIndex: 115,
    },
    {
      position: {
        x: -820,
        y: -160,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10445_1',
            group: 'left',
          },
          {
            id: 'port_10445_2',
            group: 'right',
          },
        ],
      },
      id: 'f703b088-d5f3-45f1-98ee-ce7a2c3b0d17',
      data: {
        hostid: '10445',
        name: 'pgsa2.**************.rhel84',
        status: '0',
        problem: false,
      },
      zIndex: 116,
    },
    {
      position: {
        x: -900,
        y: -80,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10245_1',
            group: 'left',
          },
          {
            id: 'port_10245_2',
            group: 'right',
          },
        ],
      },
      id: 'd1b8cac9-1509-4ef7-8ce7-25be4201d7f1',
      data: {
        hostid: '10245',
        name: 'rac数据库11g.oracle.***********.1521.prod1.node1',
        status: '0',
        problem: true,
      },
      zIndex: 117,
    },
    {
      position: {
        x: -960,
        y: -20,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10244_1',
            group: 'left',
          },
          {
            id: 'port_10244_2',
            group: 'right',
          },
        ],
      },
      id: '48af9f9f-1700-48a8-a019-0ca32093c439',
      data: {
        hostid: '10244',
        name: 'rac数据库11g.oracle.***********.1521.prod2.node2',
        status: '0',
        problem: true,
      },
      zIndex: 118,
    },
    {
      position: {
        x: -1000,
        y: -140,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10438_1',
            group: 'left',
          },
          {
            id: 'port_10438_2',
            group: 'right',
          },
        ],
      },
      id: '6efd656f-a194-484c-8f49-4c1d77e61726',
      data: {
        hostid: '10438',
        name: 'rac数据库19c.oracle.**************.1521.cbsdb1.node1',
        status: '0',
        problem: true,
      },
      zIndex: 119,
    },
    {
      position: {
        x: -960,
        y: -300,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10437_1',
            group: 'left',
          },
          {
            id: 'port_10437_2',
            group: 'right',
          },
        ],
      },
      id: '0379864b-4d97-486b-a29d-4bd917eff7e4',
      data: {
        hostid: '10437',
        name: 'rac数据库19c.oracle.**************.1521.cbsdb2.node2',
        status: '0',
        problem: true,
      },
      zIndex: 120,
    },
    {
      position: {
        x: -200,
        y: 60,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10159_1',
            group: 'left',
          },
          {
            id: 'port_10159_2',
            group: 'right',
          },
        ],
      },
      id: '63cea817-97a1-4176-9a97-4e0549f21467',
      data: {
        hostid: '10159',
        name: '麒麟V10-**************',
        status: '1',
        problem: false,
      },
      zIndex: 121,
    },
    {
      position: {
        x: -140,
        y: 220,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10142_1',
            group: 'left',
          },
          {
            id: 'port_10142_2',
            group: 'right',
          },
        ],
      },
      id: '725be8fc-021f-42a1-89d3-b0b57abe9228',
      data: {
        hostid: '10142',
        name: '贵阳银行综合支付和银联前置ADG切换演练验证01-*************',
        status: '1',
        problem: false,
      },
      zIndex: 122,
    },
    {
      position: {
        x: -20,
        y: 20,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10126_1',
            group: 'left',
          },
          {
            id: 'port_10126_2',
            group: 'right',
          },
        ],
      },
      id: '71708c7e-fa94-4ad8-ac51-a204fc03d4ba',
      data: {
        hostid: '10126',
        name: '贵阳银行综合支付和银联前置ADG切换演练验证01-*************',
        status: '1',
        problem: false,
      },
      zIndex: 123,
    },
    {
      position: {
        x: 80,
        y: -140,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10178_1',
            group: 'left',
          },
          {
            id: 'port_10178_2',
            group: 'right',
          },
        ],
      },
      id: 'e3d71dd3-0918-44db-a6de-eebee1b66795',
      data: {
        hostid: '10178',
        name: '贵阳银行综合支付和银联前置ADG切换演练验证01-*************',
        status: '1',
        problem: false,
      },
      zIndex: 124,
    },
    {
      position: {
        x: 20,
        y: 160,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10194_1',
            group: 'left',
          },
          {
            id: 'port_10194_2',
            group: 'right',
          },
        ],
      },
      id: '6ce29fc0-2d6e-49bc-8375-4ad2ef7d556a',
      data: {
        hostid: '10194',
        name: '贵阳银行综合支付和银联前置ADG切换演练验证01-*************',
        status: '1',
        problem: false,
      },
      zIndex: 125,
    },
    {
      position: {
        x: 140,
        y: 60,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10173_1',
            group: 'left',
          },
          {
            id: 'port_10173_2',
            group: 'right',
          },
        ],
      },
      id: 'e0bb70d7-2a78-4298-a591-88e544f45428',
      data: {
        hostid: '10173',
        name: '贵阳银行综合支付和银联前置ADG切换演练验证01-*************',
        status: '1',
        problem: false,
      },
      zIndex: 126,
    },
    {
      position: {
        x: 120,
        y: 160,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10321_1',
            group: 'left',
          },
          {
            id: 'port_10321_2',
            group: 'right',
          },
        ],
      },
      id: 'dc233cfd-ac5d-47db-a351-95d33c32295f',
      data: {
        hostid: '10321',
        name: '自动清数平台_192.168.100.57',
        status: '1',
        problem: false,
      },
      zIndex: 127,
    },
    {
      position: {
        x: 200,
        y: -20,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10319_1',
            group: 'left',
          },
          {
            id: 'port_10319_2',
            group: 'right',
          },
        ],
      },
      id: 'fad69248-fd54-4bcb-a230-d054f4ef93c3',
      data: {
        hostid: '10319',
        name: '自动清数平台-**************-RHEL',
        status: '1',
        problem: false,
      },
      zIndex: 128,
    },
    {
      position: {
        x: 200,
        y: -200,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10129_1',
            group: 'left',
          },
          {
            id: 'port_10129_2',
            group: 'right',
          },
        ],
      },
      id: '6350bdeb-f12e-4061-aa34-0f2bcbe5e51c',
      data: {
        hostid: '10129',
        name: '监控平台学习环境02-**************',
        status: '1',
        problem: false,
      },
      zIndex: 129,
    },
    {
      position: {
        x: -320,
        y: -80,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10141_1',
            group: 'left',
          },
          {
            id: 'port_10141_2',
            group: 'right',
          },
        ],
      },
      id: '38711a19-9f36-441c-8edf-6113deb62c2c',
      data: {
        hostid: '10141',
        name: '监控平台学习环境01-**************',
        status: '1',
        problem: false,
      },
      zIndex: 130,
    },
    {
      position: {
        x: -360,
        y: -240,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10387_1',
            group: 'left',
          },
          {
            id: 'port_10387_2',
            group: 'right',
          },
        ],
      },
      id: '3171726c-d778-41ec-9b8c-ff5646a8871d',
      data: {
        hostid: '10387',
        name: '测试公司数据库同步产品-*************',
        status: '1',
        problem: false,
      },
      zIndex: 131,
    },
    {
      position: {
        x: 180,
        y: -380,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10255_1',
            group: 'left',
          },
          {
            id: 'port_10255_2',
            group: 'right',
          },
        ],
      },
      id: '174d6992-4d57-49e7-b924-00ce82fb0df3',
      data: {
        hostid: '10255',
        name: '测试公司数据库同步产品-*************',
        status: '1',
        problem: false,
      },
      zIndex: 132,
    },
    {
      position: {
        x: 180,
        y: -580,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10254_1',
            group: 'left',
          },
          {
            id: 'port_10254_2',
            group: 'right',
          },
        ],
      },
      id: '69d60f1f-b820-4c58-96e2-f760bb850ab1',
      data: {
        hostid: '10254',
        name: '测试公司数据库同步产品-*************',
        status: '1',
        problem: false,
      },
      zIndex: 133,
    },
    {
      position: {
        x: -140,
        y: -700,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10241_1',
            group: 'left',
          },
          {
            id: 'port_10241_2',
            group: 'right',
          },
        ],
      },
      id: '0190ff83-39d8-44ed-be3a-75bf82247aa5',
      data: {
        hostid: '10241',
        name: '信贷系统.nginx.***************.8080.rkmonfronted',
        status: '0',
        problem: true,
      },
      zIndex: 134,
    },
    {
      position: {
        x: -420,
        y: -640,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10376_1',
            group: 'left',
          },
          {
            id: 'port_10376_2',
            group: 'right',
          },
        ],
      },
      id: '5acb2364-516f-4a1c-a9c0-4e01644d2867',
      data: {
        hostid: '10376',
        name: '二代支付系统.tomcat.************.9999.ser1',
        status: '0',
        problem: true,
      },
      zIndex: 135,
    },
    {
      position: {
        x: 40,
        y: -600,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10259_1',
            group: 'left',
          },
          {
            id: 'port_10259_2',
            group: 'right',
          },
        ],
      },
      id: '00144e81-10d8-45cc-a834-6f5c1af081ba',
      data: {
        hostid: '10259',
        name: '二代支付系统.tongweb.**************.7201.ser1',
        status: '0',
        problem: true,
      },
      zIndex: 136,
    },
    {
      position: {
        x: 80,
        y: -700,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10167_1',
            group: 'left',
          },
          {
            id: 'port_10167_2',
            group: 'right',
          },
        ],
      },
      id: '3b2bed52-d129-47f3-a86f-02354bcfe2af',
      data: {
        hostid: '10167',
        name: '代码库-**************',
        status: '1',
        problem: false,
      },
      zIndex: 137,
    },
    {
      position: {
        x: -780,
        y: -700,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10122_1',
            group: 'left',
          },
          {
            id: 'port_10122_2',
            group: 'right',
          },
        ],
      },
      id: '19aed9b2-e651-4086-a91d-4c8239231cb2',
      data: {
        hostid: '10122',
        name: '信贷系统.mysql.**************.3306.rkmondb',
        status: '0',
        problem: true,
      },
      zIndex: 138,
    },
    {
      position: {
        x: 300,
        y: -100,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10258_1',
            group: 'left',
          },
          {
            id: 'port_10258_2',
            group: 'right',
          },
        ],
      },
      id: 'bf1334b9-2ac4-49d9-bc6f-009239a61607',
      data: {
        hostid: '10258',
        name: '二代支付系统.tongweb.**************.7200.admin',
        status: '0',
        problem: true,
      },
      zIndex: 139,
    },
    {
      position: {
        x: 320,
        y: 160,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10131_1',
            group: 'left',
          },
          {
            id: 'port_10131_2',
            group: 'right',
          },
        ],
      },
      id: 'd98f9905-83d2-4637-88f2-a8c6ee3154bd',
      data: {
        hostid: '10131',
        name: 'zabbix60_rhel84',
        status: '1',
        problem: false,
      },
      zIndex: 140,
    },
    {
      position: {
        x: 360,
        y: 0,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10362_1',
            group: 'left',
          },
          {
            id: 'port_10362_2',
            group: 'right',
          },
        ],
      },
      id: '3b0b3514-3c3f-4eef-879c-91accecfc276',
      data: {
        hostid: '10362',
        name: 'yuandba2.**************.rhel84',
        status: '0',
        problem: false,
      },
      zIndex: 141,
    },
    {
      position: {
        x: 320,
        y: -220,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10170_1',
            group: 'left',
          },
          {
            id: 'port_10170_2',
            group: 'right',
          },
        ],
      },
      id: '6c95797e-1a48-494e-a28d-e468608516e0',
      data: {
        hostid: '10170',
        name: 'wukunyuncentos8.5-oracle',
        status: '1',
        problem: false,
      },
      zIndex: 142,
    },
    {
      position: {
        x: 440,
        y: 100,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10240_1',
            group: 'left',
          },
          {
            id: 'port_10240_2',
            group: 'right',
          },
        ],
      },
      id: 'd02d49ff-479e-4ae7-ab31-da2340c86721',
      data: {
        hostid: '10240',
        name: 'windows.*************.RK-DEV',
        status: '1',
        problem: false,
      },
      zIndex: 143,
    },
    {
      position: {
        x: 280,
        y: 260,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10209_1',
            group: 'left',
          },
          {
            id: 'port_10209_2',
            group: 'right',
          },
        ],
      },
      id: '545dd437-b6a0-4d9b-80b9-12242722fa02',
      data: {
        hostid: '10209',
        name: 'win-100.91',
        status: '1',
        problem: false,
      },
      zIndex: 144,
    },
    {
      position: {
        x: 440,
        y: 220,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10119_1',
            group: 'left',
          },
          {
            id: 'port_10119_2',
            group: 'right',
          },
        ],
      },
      id: 'fddeb605-03c3-413b-b39f-7ba4703b7d1f',
      data: {
        hostid: '10119',
        name: 'vc.**************',
        status: '0',
        problem: true,
      },
      zIndex: 145,
    },
    {
      position: {
        x: 460,
        y: 0,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10200_1',
            group: 'left',
          },
          {
            id: 'port_10200_2',
            group: 'right',
          },
        ],
      },
      id: '178fba1a-1f5c-4e1e-af73-6b55853632c0',
      data: {
        hostid: '10200',
        name: 'ubantu-16-tmp',
        status: '1',
        problem: false,
      },
      zIndex: 146,
    },
    {
      position: {
        x: 380,
        y: -280,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10239_1',
            group: 'left',
          },
          {
            id: 'port_10239_2',
            group: 'right',
          },
        ],
      },
      id: '0019b569-ebcf-4db8-b1c9-0222b5a084fd',
      data: {
        hostid: '10239',
        name: 'snmptrap.***************',
        status: '0',
        problem: false,
      },
      zIndex: 147,
    },
    {
      position: {
        x: 460,
        y: -200,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10227_1',
            group: 'left',
          },
          {
            id: 'port_10227_2',
            group: 'right',
          },
        ],
      },
      id: '56ab5809-b1d4-466b-ad4e-6b469481df15',
      data: {
        hostid: '10227',
        name: 'switcherbk.***************',
        status: '0',
        problem: true,
      },
      zIndex: 148,
    },
    {
      position: {
        x: 420,
        y: -120,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10206_1',
            group: 'left',
          },
          {
            id: 'port_10206_2',
            group: 'right',
          },
        ],
      },
      id: 'ccb49435-3bf3-44e5-9841-9b9f031bbb2e',
      data: {
        hostid: '10206',
        name: 'tanhuang-mail2',
        status: '1',
        problem: false,
      },
      zIndex: 149,
    },
    {
      position: {
        x: 440,
        y: -340,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10084_1',
            group: 'left',
          },
          {
            id: 'port_10084_2',
            group: 'right',
          },
        ],
      },
      id: '905d8c57-a047-4a04-97b9-93d262ff1d77',
      data: {
        hostid: '10084',
        name: 'rkmser1',
        status: '0',
        problem: true,
      },
      zIndex: 150,
    },
    {
      position: {
        x: 540,
        y: -280,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10440_1',
            group: 'left',
          },
          {
            id: 'port_10440_2',
            group: 'right',
          },
        ],
      },
      id: 'bf7f5f45-7846-46d1-bff6-21acd073043d',
      data: {
        hostid: '10440',
        name: 'rkmon_tongji',
        status: '0',
        problem: false,
      },
      zIndex: 151,
    },
    {
      position: {
        x: 340,
        y: -340,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10186_1',
            group: 'left',
          },
          {
            id: 'port_10186_2',
            group: 'right',
          },
        ],
      },
      id: 'a4afe3cf-b043-4711-a4c7-9621ce72f8e8',
      data: {
        hostid: '10186',
        name: 'rk-win2016-tmp',
        status: '1',
        problem: false,
      },
      zIndex: 152,
    },
    {
      position: {
        x: 560,
        y: -140,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10160_1',
            group: 'left',
          },
          {
            id: 'port_10160_2',
            group: 'right',
          },
        ],
      },
      id: '6556b6a9-c08c-42d7-9189-cf74e7598482',
      data: {
        hostid: '10160',
        name: 'rk-rhel-7.9-tmp',
        status: '1',
        problem: false,
      },
      zIndex: 153,
    },
    {
      position: {
        x: 180,
        y: -260,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10422_1',
            group: 'left',
          },
          {
            id: 'port_10422_2',
            group: 'right',
          },
        ],
      },
      id: 'cfd64290-e0a0-45b7-8031-ec550c5197a4',
      data: {
        hostid: '10422',
        name: 'rk_19c_rac1_100.32',
        status: '1',
        problem: false,
      },
      zIndex: 154,
    },
    {
      position: {
        x: 580,
        y: -40,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10176_1',
            group: 'left',
          },
          {
            id: 'port_10176_2',
            group: 'right',
          },
        ],
      },
      id: 'a3672d5b-d150-4e0d-b0ee-ab393e3d6d33',
      data: {
        hostid: '10176',
        name: 'rkmon_proxy1',
        status: '1',
        problem: false,
      },
      zIndex: 155,
    },
    {
      position: {
        x: 660,
        y: -240,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10164_1',
            group: 'left',
          },
          {
            id: 'port_10164_2',
            group: 'right',
          },
        ],
      },
      id: 'e228a17a-fa65-4162-9f72-34fe0b2c4a98',
      data: {
        hostid: '10164',
        name: 'rkmon_server',
        status: '1',
        problem: true,
      },
      zIndex: 156,
    },
    {
      position: {
        x: 600,
        y: -340,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10165_1',
            group: 'left',
          },
          {
            id: 'port_10165_2',
            group: 'right',
          },
        ],
      },
      id: 'c4faa3f7-9967-4f97-9ade-ed30b58b5fe2',
      data: {
        hostid: '10165',
        name: 'rk-nextcloud-***********',
        status: '1',
        problem: false,
      },
      zIndex: 157,
    },
    {
      position: {
        x: 660,
        y: 60,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10147_1',
            group: 'left',
          },
          {
            id: 'port_10147_2',
            group: 'right',
          },
        ],
      },
      id: '2f586153-9d73-4a2c-878b-37dd8fd5bd7c',
      data: {
        hostid: '10147',
        name: 'rainbow-server-100.120',
        status: '1',
        problem: false,
      },
      zIndex: 158,
    },
    {
      position: {
        x: 700,
        y: -140,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10252_1',
            group: 'left',
          },
          {
            id: 'port_10252_2',
            group: 'right',
          },
        ],
      },
      id: 'e173d3d8-6b6b-44ca-b0dc-84f12dc8b5c5',
      data: {
        hostid: '10252',
        name: 'redis.**************.rhel84',
        status: '0',
        problem: true,
      },
      zIndex: 159,
    },
    {
      position: {
        x: 740,
        y: -300,
      },
      size: {
        width: 100,
        height: 60,
      },
      view: 'react-shape-view',
      shape: 'web-network-node',
      ports: {
        groups: {
          left: {
            position: [26, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
          right: {
            position: [70, 19],
            attrs: {
              circle: {
                magnet: true,
                stroke: '#D9D9D9FF',
                r: 4,
              },
            },
          },
        },
        items: [
          {
            id: 'port_10435_1',
            group: 'left',
          },
          {
            id: 'port_10435_2',
            group: 'right',
          },
        ],
      },
      id: '8b20cce2-0ffb-418e-ad8f-fdd0e843a941',
      data: {
        hostid: '10435',
        name: 'rac数据库19c.oracle.**************.1521.cbsdb1.node1',
        status: '0',
        problem: true,
      },
      zIndex: 160,
    },
  ],
};
