import { BASE_URL } from '@/utils/setting';
import { UploadOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { message, Space, Upload as AntUpload, UploadProps } from 'antd';
import defaultSettings from 'config/defaultSettings';
const props: UploadProps = {
  name: 'file',
  action: `${BASE_URL}/v1/atomic/task/upload/zip`,
  method: 'post',
  accept: '.zip',
  headers: {
    Auth: localStorage.getItem(defaultSettings.TOKEN_KEY) + '',
  },
  showUploadList: false,
  beforeUpload: (file) => {
    const isFolder = file.type === 'application/x-msdownload'; // 判断是否为文件夹

    if (isFolder) {
      // 如果文件类型是 application/x-msdownload，即文件夹，阻止上传
      message.error('不能上传文件夹');
      return false; // 阻止文件上传
    }

    return true; // 允许文件上传
  },
  onChange(info) {
    const msg = info.file?.response?.message;
    if (info.file.status === 'done') {
      if (info.file?.response?.data) {
        message.success(
          <Space>
            {msg || '上传成功'}
            <a
              onClick={() => {
                history.push('/deployment/playbook/upload-list');
              }}
            >
              解压进度
            </a>
          </Space>,
          5,
        );
      }
    } else if (info.file.status === 'error') {
      // 文件上传失败
      message.error(msg || `${info.file.name} 上传失败`);
    }
  },
};
const Upload = () => {
  return (
    <AntUpload {...props}>
      <Space>
        <UploadOutlined />
        上传文件
      </Space>
    </AntUpload>
  );
};

export default Upload;
