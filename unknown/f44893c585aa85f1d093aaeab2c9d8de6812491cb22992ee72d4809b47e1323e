import { history, useLocation } from '@umijs/max';
import React, { useEffect } from 'react';

export interface WithRouteEditingProps {
  queryParams: URLSearchParams;
}

const withStorageToUrl = <P extends WithRouteEditingProps>(Component: React.ComponentType<P>) => {
  return (props: Omit<P, keyof WithRouteEditingProps>) => {
    const { pathname } = useLocation();
    const searchUrl = window.localStorage.getItem(pathname) || '';
    const queryParams = new URLSearchParams(searchUrl || '');
    useEffect(() => {
      history.replace(`${pathname}?${searchUrl}`);
    }, []);

    // Merge the original component props with the route editing props
    const mergedProps = {
      ...props,
      queryParams,
    } as P;

    return <Component {...mergedProps} />;
  };
};

export default withStorageToUrl;
