import Icon from '@/components/Icon';
import { Space } from 'antd';
import classNames from 'classnames/bind';
import React from 'react';
import { DeviceTypeMap } from '../../WebNetworkNode';
import styles from './index.less';

const cx = classNames.bind(styles);

interface IconSelectorProps {
  value?: string;
  onChange?: (value: string) => void;
}

const IconSelector: React.FC<IconSelectorProps> = ({ value, onChange }) => {
  const handleClick = (key: string) => {
    onChange?.(key);
  };

  return (
    <div className={cx('icon-container')}>
      <Space size={16} wrap>
        {Object.keys(DeviceTypeMap).map((key) => {
          return (
            <Icon
              key={key}
              type={`icon-${key}`}
              className={cx('icon', {
                selected: key === value,
              })}
              onClick={() => handleClick(key)}
            />
          );
        })}
      </Space>
    </div>
  );
};

export default IconSelector;
