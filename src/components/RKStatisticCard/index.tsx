import { StatisticCard, StatisticCardProps } from '@ant-design/pro-components';
import classNames from 'classnames/bind';
import styles from './index.less';
const cx = classNames.bind(styles);

const RKStatisticCard: React.FC<
  StatisticCardProps & {
    theme?: 'light' | 'dark';
  }
> = ({ className, theme = 'light', ...restProps }) => {
  return (
    <StatisticCard className={cx({ 'dark-card': theme === 'dark' }, className)} {...restProps} />
  );
};
export default RKStatisticCard;
