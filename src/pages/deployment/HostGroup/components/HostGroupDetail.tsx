import RKCol from '@/components/RKCol';
import { hostGroupCreate, hostGroupFindById, hostGroupUpdateById } from '@/services/http/hostGroup';
import { onSuccessAndGoBack, onSuccessAndRefresh } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProColumns,
  ProForm,
  ProFormInstance,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { history, useParams, useRequest } from '@umijs/max';
import { Row } from 'antd';
import React, { useCallback, useRef, useState } from 'react';
import SelectHostDrawer from './SelectHostDrawer';

const HostGroupDetail: React.FC = () => {
  // 判断是否为编辑页面
  const { id } = useParams();
  const isEditPage = !!id;

  const [hostList, setHostList] = useState<API.HostGroupVO[]>([]);
  const formRef = useRef<ProFormInstance>();

  // 删除主机
  const hostDelete = useCallback((id: string) => {
    const hosts = formRef.current?.getFieldValue('hosts');
    const list = hosts.filter((item: API.HostVO) => item.id !== id);
    formRef.current?.setFieldsValue({
      hosts: list,
    });
    setHostList(list);
  }, []);

  // 新建
  const { run: save, loading: saveLoading } = useRequest(hostGroupCreate, {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 编辑
  const { run: edit, loading: editLoading } = useRequest(hostGroupUpdateById, {
    manual: true,
    onSuccess: (res) => onSuccessAndRefresh(res),
    formatResult: (res) => res,
  });
  // 获取详情
  useRequest(() => hostGroupFindById({ id: id! }), {
    ready: isEditPage,
    onSuccess: (res) => {
      setHostList(res?.hosts || []);
      formRef.current?.setFieldsValue({ ...res });
    },
  });

  const columns: ProColumns<API.HostVO>[] = [
    {
      title: '主机名',
      dataIndex: 'host',
    },
    {
      title: '描述',
      dataIndex: 'description',
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => [
        <a key="del" onClick={() => hostDelete(record.id as string)}>
          删除
        </a>,
      ],
    },
  ];

  const handleSelect = useCallback(
    async (arr: API.HostPageVO[]) => {
      const addedIds = hostList?.map((i) => i.id);
      const notAddArr = arr?.filter((item) => !addedIds?.includes(item.id));

      const newArr = [...hostList, ...notAddArr];
      formRef.current?.setFieldsValue({
        hosts: newArr,
      });
      setHostList(newArr);
    },
    [hostList],
  );

  return (
    <PageContainer
      header={{
        title: false,
      }}
      className="detail-container"
    >
      <ProForm
        formRef={formRef}
        layout="vertical"
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          onReset: () => {
            history.go(-1);
          },
          render: (props, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
          submitButtonProps: {
            loading: saveLoading || editLoading,
          },
        }}
        onFinish={async ({ id, ...values }) => {
          const form = {
            name: values.name,
            description: values.description,
            hostIds: values.hosts?.map((i: API.HostPageVO) => i.id),
          };
          if (isEditPage) {
            edit({ id }, form);
          } else {
            save(form);
          }
        }}
      >
        <div style={{ display: 'none' }}>
          <ProFormText name="id" label="id" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText name="name" label="组名" placeholder="请输入" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormText name="description" label="描述" placeholder="请输入" />
          </RKCol>
        </Row>
        <ProForm.Item name="hosts"></ProForm.Item>
        <ProTable
          {...defaultTableConfig}
          className="inner-table"
          options={false}
          search={false}
          headerTitle="关联主机"
          dataSource={hostList}
          columns={columns}
          toolbar={{
            actions: [
              <SelectHostDrawer
                key="add"
                handleSelect={handleSelect}
                initialValues={{ hostList }}
              />,
            ],
          }}
        />
      </ProForm>
    </PageContainer>
  );
};

export default HostGroupDetail;
