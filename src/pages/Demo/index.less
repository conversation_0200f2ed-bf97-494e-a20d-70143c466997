:root {
  /* 渐变边框色 */
  --line-bg: linear-gradient(180deg, #1e74bd, #32b7d8, #3eded2);
  /* 内容背景色 */
  --content-bg: linear-gradient(180deg, #11151c 0%, #1a396e 100%);
  /* clip-path裁剪 */
  --path: polygon(0 0, calc(100% - 10px) 0, 100% 10px, 100% 100%, 10px 100%, 0 calc(100% - 10px));
}
.container {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100vw;
  height: 100vh;
  overflow: auto;
  background-color: #11151b;
  background-image: url(/images/bg.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  .header {
    width: 100%;
    // max-width: 1920px;
    height: 80px;
    color: #a5ccf2;
    font-weight: 600;
    font-size: 36px;
    font-family: 'VF Regular';
    line-height: 80px;
    text-align: center;
    background-image: url(/images/union/header.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
  }
  .content {
    position: relative;
    width: 100vw;
    // max-width: 1920px;
    padding: 24px;
  }

  :global {
    .ant-select-selector {
      color: #fff !important;
      background-color: transparent !important;
      border-color: #1e74bd !important;
    }
    .ant-select-arrow {
      color: #1e74bd !important;
    }
    .ant-pro-card {
      background-color: transparent !important;
      .ant-table {
        color: #fff !important;
        background-color: transparent !important;
      }
      .ant-table-thead th {
        color: #fff;
        background: #164c7e !important;
      }
      .ant-pagination-total-text {
        color: #fff !important;
      }
      .ant-pagination .ant-pagination-item-active {
        border-color: #1e74bd;
      }
      table {
        td,
        th {
          border-color: #163a5a;
        }
      }
      .ant-table-cell {
        &::before {
          background-color: #516885 !important;
        }
      }
    }
  }
}
