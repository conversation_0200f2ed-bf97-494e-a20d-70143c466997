import IconFont from '@/components/Icon';
import { findGlobalConfig } from '@/services/http/globalConfig';
import { panoramicGroupProblemPage } from '@/services/http/panoramic';
import { zabbixPost } from '@/services/zabbix';
import { groupedData } from '@/utils';
import { FullscreenExitOutlined, FullscreenOutlined, SettingOutlined } from '@ant-design/icons';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { Access, Link, useAccess, useLocation, useRequest } from '@umijs/max';
import { Col, Row, Tooltip, Typography } from 'antd';
import React, { useMemo, useState } from 'react';
import DataCard from './components/DataCard';
import Ratio from './components/Ratio';
import styles from './index.less';

const { Text } = Typography;
export const COL_SPAN = { xs: 24, sm: 12, md: 8, lg: 6, xl: 6, xxl: 4 };

// 将数据增加个 title 字段
export const covertData = (arr: RK_API.Item[], title: string, type: string, color: string) => {
  if (!arr?.length || !title) return [];
  return arr.map((item) => ({
    ...item,
    title,
    hostName: item?.hosts?.[0]?.name || '',
    dataType: type,
    color,
  }));
};

export type UsageRateProps = RK_API.Item & {
  title: string;
  hostName: string;
  color: string;
  dataType: 'CPU' | 'Memory' | 'Disk';
};

// 获取最大告警程度
export const getMaxAlarmLevel = (problemArr?: API.ProblemVO[]) => {
  if (!problemArr?.length) return '0';
  const arr = problemArr?.map((item) => item.severity) || [];
  const maxSeverity = arr.reduce((max, current) => (current! > max! ? current : max), arr[0]);
  return maxSeverity;
};

// 根据tag rkmview_qjst_host 来获取排序 map  如果rkmview_qjst_host值为-1 则不显示

// export const getRkmviewQjstHostValue = (host: RK_API.Host): string => {
//   const tagValue = host?.tags?.find((tag) => tag.tag === 'rkmview_qjst_host')?.value;
//   if (tagValue) {
//     return tagValue;
//   }
//   const inheritedTagValue = host?.inheritedTags?.find(
//     (tag) => tag.tag === 'rkmview_qjst_host',
//   )?.value;
//   if (inheritedTagValue) {
//     return inheritedTagValue;
//   }
//   return '9999999';
// };

const StatisticalView = () => {
  const { isAdmin } = useAccess();
  const [isFirstLoad, setIsFirstLoad] = useState(true);
  const [pollingInterval, setPollingInterval] = useState(0); // 默认10秒

  // 获取刷新频率
  useRequest(findGlobalConfig, {
    onSuccess: (res) => {
      setPollingInterval((res?.refreshSeconds || 10) * 1000);
    },
  });

  // 获取全景组
  const { data: panoramicGroupData, loading: panoramicGroupLoading } = useRequest(
    () =>
      panoramicGroupProblemPage({
        page: {
          page: 1,
          size: 100,
        },
      }),
    {
      ready: !!pollingInterval,
      pollingInterval,
      onSuccess: () => {
        setIsFirstLoad(false);
      },
    },
  );

  // 获取分组页面数据排序
  // const { data: panoramicGroupSortData } = useRequest(() =>
  //   panoramicGroupPage({
  //     page: {
  //       page: 1,
  //       size: 100,
  //     },
  //   }),
  // );

  // 获取所有要展示的主机 id
  const allHostIds = useMemo(
    () => panoramicGroupData?.data?.flatMap((item) => item.hostIds),
    [panoramicGroupData],
  );
  // 获取主机的排序
  const sortOrder = useMemo(() => {
    const allHosts = panoramicGroupData?.data?.flatMap((item) => item.hosts || []);
    return allHosts?.reduce((acc, item) => {
      if (!acc[item.hostid!] || acc[item.hostid!] < item.sort!) {
        acc[item.hostid!] = item.sort!;
      }
      return acc;
    }, {} as { [hostid: string]: number });
  }, [panoramicGroupData]);

  // 获取 cpu使用率(CPU) 内存使用率(MEM) 磁盘使用率(Filesystem)
  const { data: cpuUsageArray = [], loading: cpuUsageLoading } = useRequest(
    () =>
      zabbixPost({
        method: 'item.get',
        hostids: allHostIds,
        tags: [{ tag: 'rkmon_view_type_rate', value: 'CPU', operator: '1' }],
        selectHosts: 'extend',
        output: ['name', 'lastvalue', 'units', 'hostid', 'tag'],
      }),
    {
      ready: !!allHostIds?.length,
      refreshDeps: [allHostIds?.length],
      pollingInterval,
    },
  );

  const { data: memoryUsageArray = [], loading: memoryUsageLoading } = useRequest(
    () =>
      zabbixPost({
        method: 'item.get',
        hostids: allHostIds,
        tags: [{ tag: 'rkmon_view_type_rate', value: 'MEM', operator: '1' }],
        selectHosts: 'extend',
        output: ['name', 'lastvalue', 'units', 'hostid', 'tag'],
      }),
    {
      ready: !!allHostIds?.length,
      refreshDeps: [allHostIds?.length],
      pollingInterval,
    },
  );
  const { data: diskUsageArray = [], loading: diskUsageLoading } = useRequest(
    () =>
      zabbixPost({
        method: 'item.get',
        hostids: allHostIds,
        tags: [{ tag: 'rkmon_view_type_rate', value: 'Filesystem /', operator: '1' }],
        selectHosts: 'extend',
        output: ['name', 'lastvalue', 'units', 'hostid', 'tag'],
      }),
    {
      ready: !!allHostIds?.length,
      refreshDeps: [allHostIds?.length],
      pollingInterval,
    },
  );
  // cpu使用率(CPU) 内存使用率(MEM) 磁盘使用率(Filesystem) 根据 hostId 分组
  const usageRateData = useMemo(() => {
    const arr = [
      ...covertData(cpuUsageArray, 'cpu使用率', 'CPU', '#38CCE1'),
      ...covertData(memoryUsageArray, '内存使用率', 'Memory', '#EA22FD'),
      ...covertData(diskUsageArray, '磁盘使用率', 'Disk', '#A66CFE'),
    ];
    return groupedData(arr, 'hostid');
  }, [cpuUsageArray, memoryUsageArray, diskUsageArray]);

  // 获取卡片上 tag 为rkmview_qjst_item 的监控指标

  const { data: monitorArr = [] } = useRequest(
    () =>
      zabbixPost({
        method: 'item.get',
        hostids: allHostIds,
        tags: [{ tag: 'rkmview_qjst_item', value: '', operator: '0' }],
        selectHosts: 'extend',
        output: ['name', 'lastvalue', 'hostid'],
      }),
    {
      ready: !!allHostIds?.length,
      refreshDeps: [allHostIds?.length],
      pollingInterval,
    },
  );

  const monitorData = useMemo(() => {
    const categorizedData: { [key: string]: any[] } = {};

    monitorArr.forEach((item: RK_API.Item) => {
      const hostid = item.hostid;
      if (!categorizedData[hostid]) {
        categorizedData[hostid] = [];
      }
      categorizedData[hostid].push(item);
    });
    return categorizedData;
  }, [monitorArr]);

  // 根据排序规则对主机数据进行排序
  const sortedUsageRateData = useMemo(() => {
    const hostData = Object.keys(usageRateData).map((hostid) => ({
      hostid,
      data: usageRateData[hostid],
      sortOrder: sortOrder?.[hostid] || 0,
    }));

    return hostData.sort((a, b) => b.sortOrder - a.sortOrder);
  }, [usageRateData, sortOrder]);

  // 使用率 loading
  const usageLoading = useMemo(
    () => cpuUsageLoading || memoryUsageLoading || diskUsageLoading,
    [cpuUsageLoading, memoryUsageLoading, diskUsageLoading],
  );

  // 判断是否为全屏页面

  const { pathname } = useLocation();
  const isFullScreen = pathname.includes('full');
  return (
    <>
      {/* 设置入口 */}
      <div className={styles['setting-container']}>
        {isFullScreen ? (
          <Link to="/monitor/statistical-view">
            <Tooltip title="退出全屏">
              <FullscreenExitOutlined className={styles.icon} />
            </Tooltip>
          </Link>
        ) : (
          <Link to="/monitor/statistical-view/full">
            <Tooltip title="全屏">
              <FullscreenOutlined className={styles.icon} />
            </Tooltip>
          </Link>
        )}

        <Access accessible={isAdmin || false}>
          <Link to="/monitor/statistical-view/setting">
            <Tooltip title="设置">
              <SettingOutlined className={styles.icon} />
            </Tooltip>
          </Link>
        </Access>
      </div>
      <PageContainer
        header={{
          title: false,
          breadcrumb: {},
        }}
        className={styles.container}
        loading={isFirstLoad && panoramicGroupLoading}
      >
        <Row gutter={[16, 16]}>
          {panoramicGroupData?.data?.map((item) => {
            const hostId = item.hostIds?.find((item) => monitorData[item]);
            const info = hostId ? monitorData[hostId] : undefined;

            return (
              <Col {...COL_SPAN} key={item.id}>
                <DataCard
                  id={item.id!}
                  type={getMaxAlarmLevel(item?.problemList)}
                  title={item?.name || ''}
                  loading={isFirstLoad && panoramicGroupLoading}
                  value1={item?.health || 0}
                  value2={item?.problemList?.length}
                  extraInfo={info}
                />
              </Col>
            );
          })}
        </Row>

        <ProCard
          style={{
            backgroundColor: 'transparent',
          }}
          bodyStyle={{
            paddingInline: 0,
            paddingBlock: 0,
          }}
          gutter={[16, 24]}
          wrap
        >
          {sortedUsageRateData.map(({ hostid, data }) => {
            const title = data?.at(0)?.hostName;
            return (
              <ProCard
                key={hostid}
                loading={isFirstLoad && usageLoading}
                title={
                  <div>
                    <Text
                      style={{
                        width: '100%',
                      }}
                      strong
                      ellipsis={{
                        tooltip: title,
                      }}
                    >
                      {title}
                    </Text>
                  </div>
                }
                headerBordered
                className={styles.card}
                headStyle={{
                  display: 'block',
                  overflow: 'hidden',
                  fontSize: 16,
                }}
                colSpan={COL_SPAN}
              >
                {data.map((item) => {
                  const value = item.lastvalue || '';

                  const roundedValue = parseFloat(value).toFixed(1);
                  const t = `${item.name}（${roundedValue}
                        ${item.units}）`;
                  return (
                    <React.Fragment key={item.itemid}>
                      <Text
                        style={{ width: '100%' }}
                        ellipsis={{
                          tooltip: t,
                        }}
                      >
                        {item.dataType && (
                          <IconFont className={styles.icon} type={`icon-${item.dataType}`} />
                        )}
                        {t}
                      </Text>
                      <Ratio value={roundedValue} units={item.units} color={item.color} />
                    </React.Fragment>
                  );
                })}
              </ProCard>
            );
          })}
        </ProCard>
      </PageContainer>
    </>
  );
};

export default StatisticalView;
