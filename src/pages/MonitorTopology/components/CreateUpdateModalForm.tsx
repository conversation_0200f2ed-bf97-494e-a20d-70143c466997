import { createTopology, updateTopology } from '@/services/http/topology';
import { requiredRule } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ModalFormProps,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, message } from 'antd';
import React from 'react';

const CreateUpdateModalForm: React.FC<
  ModalFormProps & {
    reload: () => void;
  }
> = ({ initialValues, reload }) => {
  const isEdit = initialValues;

  return (
    <ModalForm<API.CUPanoramicGroupRequest & API.updatePanoramicGroupParams>
      width={500}
      title={isEdit ? '编辑拓扑' : '新建拓扑'}
      trigger={
        isEdit ? (
          <Button type="link">{initialValues.name}</Button>
        ) : (
          <Button key="add" type="primary" icon={<PlusOutlined />}>
            新建拓扑
          </Button>
        )
      }
      onFinish={async (value) => {
        const { id, ...rest } = value;

        const msg = isEdit ? await updateTopology({ id }, rest) : await createTopology(value);
        const success = msg.data;
        if (success) {
          message.success('操作成功!');
          reload();
        }
        return success;
      }}
      autoFocusFirstInput
      initialValues={initialValues}
      modalProps={{
        destroyOnClose: true,
        centered: true,
      }}
    >
      {/* 不需要展示，只是为了form传值 */}
      <div className="rk-none">
        <ProFormText name="id" />
      </div>
      <ProFormText name="name" label="名称" rules={[requiredRule]} />
      <ProFormTextArea
        name="description"
        label="描述"
        fieldProps={{ autoSize: { minRows: 1, maxRows: 3 } }}
      />
    </ModalForm>
  );
};

export default CreateUpdateModalForm;
