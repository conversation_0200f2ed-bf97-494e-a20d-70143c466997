import MacrosSetter, { DataSourceType } from '@/components/MacrosSetter';
import TagSetter from '@/components/TagsSetter';
import { useTemplateList } from '@/hooks/useTemplateList';
import { zabbix, zabbixPost } from '@/services/zabbix';
import { getRandomId, onSuccessAndGoBack } from '@/utils';
import {
  EditableFormInstance,
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import React, { useEffect, useRef } from 'react';
import Base from './Base';
import Encryption from './Encryption';
import Interface from './Interface';
import IPMI from './IPMI';

type ItemProps = RK_API.GlobalMacro &
  RK_API.HostMacro & {
    key?: string;
    inheritName?: string;
    inheritType?: string;
  };

interface MergedItem extends ItemProps {
  defaultValue?: string;
}

const mergeArrays = (inheritMacro: ItemProps[], ownMacro: ItemProps[]): MergedItem[] => {
  const newInheritMacro = inheritMacro.map((item) => ({
    ...item,
    defaultValue: item.value,
    key: getRandomId(),
  }));
  const mergedArray: MergedItem[] = newInheritMacro;
  ownMacro.forEach((item) => {
    const existingItemIndex = mergedArray.findIndex((m) => m.macro === item.macro);
    if (existingItemIndex !== -1) {
      mergedArray[existingItemIndex] = { ...mergedArray[existingItemIndex], ...item };
    } else {
      mergedArray.push(item);
    }
  });

  return mergedArray;
};

const HostDetails: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const editorFormRef = useRef<EditableFormInstance<DataSourceType>>(null);

  // 判断是否为编辑页面
  const { id } = useParams();
  const isEditPage = !!id;

  // 模版
  const { templateList } = useTemplateList();

  // 新建
  const { run: add, loading: addLoading } = useRequest(
    (value) => zabbixPost({ ...value, method: 'host.create' }),
    {
      manual: true,
      onSuccess: onSuccessAndGoBack,
      formatResult: (res) => res,
    },
  );
  // 修改
  const { run: update, loading: updateLoading } = useRequest(
    (value) => zabbixPost({ ...value, method: 'host.update' }),
    {
      manual: true,
      onSuccess: onSuccessAndGoBack,
      formatResult: (res) => res,
    },
  );

  // 继承的模版名称map
  const inheritHostMacroMap = useRef<Record<string, string>>();

  // 查询继承的全局宏
  const { data: globalMacro, run: getGlobalMacro } = useRequest(
    () =>
      zabbix({
        globalmacro: true,
        sortfield: 'macro',
        method: 'usermacro.get',
      }),
    {
      manual: true,
    },
  );
  // 查询继承的用户宏
  const { data: hostMacro, run: getHostMacro } = useRequest(
    (hostids) =>
      zabbix({
        hostids,
        sortfield: 'macro',
        method: 'usermacro.get',
      }),
    {
      manual: true,
    },
  );

  // 主机详情
  const { data } = useRequest(
    () =>
      zabbix({
        hostids: [id],
        selectTags: 'extend',
        selectGroups: 'extend',
        selectParentTemplates: 'extend',
        selectInterfaces: 'extend',
        selectMacros: 'extend',
        method: 'host.get',
      }),
    {
      ready: isEditPage,
      onSuccess: (res) => {
        const info = res?.[0] || {};
        const { parentTemplates = [] } = info;

        getGlobalMacro();
        // 如果有父默模版
        if (parentTemplates.length) {
          const map: Record<string, string> = {};
          const ids = parentTemplates.map((item: RK_API.Template) => {
            map[item.templateid!] = item.name!;
            return item.templateid;
          });
          inheritHostMacroMap.current = map;
          getHostMacro(ids);
        }
        formRef.current?.setFieldsValue?.(info);
      },
    },
  );

  // 新增页面查询全局宏
  useEffect(() => {
    if (!isEditPage) {
      getGlobalMacro();
    }
  }, []);

  useEffect(() => {
    const info = data?.[0] || {};
    // 模版自己的宏
    const { macros = [] } = info;
    // 继承宏
    const inheritMacro = [
      ...(globalMacro?.map((item: RK_API.GlobalMacro) => ({ ...item, inheritType: 'global' })) ||
        []),
      ...(hostMacro?.map((item: Record<string, any>) => ({
        ...item,
        inheritType: 'host',
        inheritName: inheritHostMacroMap.current?.[item.hostid],
      })) || []),
    ];
    const mergeMacro = mergeArrays(inheritMacro as ItemProps[], macros);
    formRef.current?.setFieldsValue?.({
      macros: mergeMacro,
    });
  }, [globalMacro, hostMacro]);

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<RK_API.Host>
        formRef={formRef}
        initialValues={{
          ipmi_authtype: '-1', // IPMI 验证算法
          ipmi_privilege: '2', // IPMI 权限等级
          tls_accept: '1', // 连接主机
          tls_connect: '1', // 从主机连接
          status: '0',
          proxy_hostid: '0',
        }}
        onValuesChange={({ templates }) => {
          // 监听模版
          if (templates?.length) {
            getHostMacro(templates.find((item: API.Template) => item.templateid));
            const map: Record<string, string> = {};
            templateList.forEach((item: RK_API.Template) => {
              map[item.templateid!] = item.name!;
            });
            inheritHostMacroMap.current = map;
          }
        }}
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          onReset: () => {
            history.go(-1);
          },

          render: (props, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
          submitButtonProps: {
            loading: addLoading || updateLoading,
          },
        }}
        onFinish={async (values) => {
          const res = {
            ...values,
            name: values.name || values.host,
          };
          // 宏配置的表单验证
          editorFormRef.current?.validateFields().then(() => {
            if (isEditPage) {
              update(res);
            } else {
              add(res);
            }
          });
        }}
        // request={() =>
        //   queryFormData(
        //     {
        //       hostids: [id],
        //       selectTags: 'extend',
        //       selectGroups: 'extend',
        //       selectParentTemplates: 'extend',
        //       selectInterfaces: 'extend',
        //       selectMacros: 'extend',
        //       method: 'host.get',
        //     },
        //     isEditPage,
        //     zabbix,
        //   )
        // }
      >
        {/* 不需要展示，只是为了form传值 */}
        <div className="rk-none">
          <ProFormText name="hostid" placeholder="请输入" />
        </div>
        {/* 基础设置 */}
        <Base />
        {/* 主机接口 */}
        <ProForm.Item
          name="interfaces"
          convertValue={(value = []) =>
            value.map((item: RK_API.Interface) => ({
              ...item,
              key: item?.key || item?.interfaceid,
            }))
          }
          transform={(value: Record<string, any>[], namePath) => ({
            [namePath]: value.map((item) => ({ ...item, hostid: id })),
          })}
        >
          <Interface />
        </ProForm.Item>
        {/* IPMI配置 */}
        <IPMI />
        {/* 加密 */}
        <Encryption />
        {/* 标记 */}
        <TagSetter />
        {/* 宏设置 */}
        <ProForm.Item
          name="macros"
          transform={(value: Record<string, any>[], namePath) => ({
            [namePath]: value
              .filter((item) => {
                if (item.inheritType) {
                  return item.value !== item.defaultValue;
                }
                return item;
              })
              .map(({ hostmacroid, macro, value, description }) => ({
                hostmacroid,
                macro,
                value,
                description,
              })),
          })}
        >
          <MacrosSetter editorFormRef={editorFormRef} />
        </ProForm.Item>
      </ProForm>
    </PageContainer>
  );
};

export default HostDetails;
