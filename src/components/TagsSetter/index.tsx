import { ProFormGroup, ProFormList, ProFormText } from '@ant-design/pro-components';
import HeadTitle from '../HeadTitle';

const TagsSetter: React.FC = () => {
  return (
    <>
      <HeadTitle>标签</HeadTitle>
      <ProFormList
        style={{ width: 710 }}
        name="tags"
        creatorButtonProps={{
          creatorButtonText: '新建',
        }}
        creatorRecord={{
          tag: '',
          value: '',
        }}
        copyIconProps={false}
      >
        <ProFormGroup>
          <ProFormText key="tag" width="md" name="tag" label="名称" />
          <ProFormText key="value" width="md" name="value" label="值" />
        </ProFormGroup>
      </ProFormList>
    </>
  );
};

export default TagsSetter;
