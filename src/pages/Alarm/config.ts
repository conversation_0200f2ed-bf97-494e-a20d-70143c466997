import Cherry from 'cherry-markdown';
import { CherryOptions } from 'cherry-markdown/types/cherry';

const help = Cherry.createMenuHook('快捷键', {
  iconName: 'question',
  subMenuConfig: [
    {
      noIcon: true,
      name: '一级标题 Ctrl+1',
    },
    {
      noIcon: true,
      name: '二级标题 ctrl+2',
    },
    {
      noIcon: true,
      name: '三级标题 ctrl+3',
    },
    {
      noIcon: true,
      name: '四级标题 ctrl+4',
    },
    {
      noIcon: true,
      name: '五级标题 ctrl+5',
    },
    {
      noIcon: true,
      name: '六级标题 ctrl+6',
    },
    {
      noIcon: true,
      name: '代码块 ctrl+shift+c',
    },
    {
      noIcon: true,
      name: '超链接 ctrl+shift+l',
    },
    {
      noIcon: true,
      name: '引用 ctrl+shift+q',
    },
  ],
});

// 添加图片压缩函数
const compressImage = (file: File, maxWidth = 1920, quality = 0.8): Promise<string> => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        let width = img.width;
        let height = img.height;

        // 如果图片宽度超过最大宽度，等比例缩放
        if (width > maxWidth) {
          height = Math.round((height * maxWidth) / width);
          width = maxWidth;
        }

        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');
        ctx?.drawImage(img, 0, 0, width, height);

        // 转换为base64，压缩质量为0.8
        const base64String = canvas.toDataURL(file.type, quality);
        resolve(base64String);
      };
      img.src = e.target?.result as string;
    };
    reader.readAsDataURL(file);
  });
};

const config: Partial<CherryOptions> = {
  id: 'markdown-container',
  value: '',
  forceAppend: false,
  locale: 'zh_CN',

  editor: {
    // defaultModel: 'previewOnly',
  },
  toolbars: {
    theme: 'dark',
    toolbar: [
      'bold',
      'italic',
      'strikethrough',
      '|',
      'color',
      'header',
      '|',
      'list',
      'panel',
      {
        insert: [
          'image',
          'link',
          'hr',
          'br',
          'code',
          'formula',
          'table',
          'line-table',
          'bar-table',
        ],
      },
      'graph',
      '|',
      'undo',
      'redo',
      'copy',
      'togglePreview',
      'switchModel',
      // 'export',
      // '|',
      'help',
    ],
    // 新行悬停菜单配置
    float: ['header', '|', 'quote', 'table', 'code'],
    shortcutKey: {
      'Ctrl-1': 'header',
      'Ctrl-2': 'header',
      'Ctrl-3': 'header',
      'Ctrl-4': 'header',
      'Ctrl-5': 'header',
      'Ctrl-6': 'header',
      'Ctrl-b': 'bold',
      'Ctrl-Shift-c': 'code',
      'Ctrl-Shift-l': 'link',
      'Ctrl-Shift-q': 'quote',
    },
    customMenu: {
      help,
    },
  },
  fileUpload(file, callback) {
    // 判断是否为图片
    if (file.type.startsWith('image/')) {
      // 图片大于1MB才进行压缩
      if (file.size > 1024 * 1024) {
        compressImage(file).then((base64String) => {
          callback(base64String, {
            width: '90%',
            height: 'auto',
          });
        });
      } else {
        // 小图片直接转base64
        const reader = new FileReader();
        reader.onload = () => {
          const base64String = reader.result as string;
          callback(base64String, {
            width: '90%',
            height: 'auto',
          });
        };
        reader.readAsDataURL(file);
      }
    } else {
      // 非图片文件直接转base64
      const reader = new FileReader();
      reader.onload = () => {
        const base64String = reader.result as string;
        callback(base64String);
      };
      reader.readAsDataURL(file);
    }
  },
  previewer: {
    dom: false,
    className: 'cherry-markdown',
    // 预览区编辑能力
    enablePreviewerBubble: true,
    lazyLoadImg: {
      // 加载图片时如果需要展示loading图，则配置loading图的地址
      loadingImgPath: '',
      // 同一时间最多有几个图片请求，最大同时加载6张图片
      maxNumPerTime: 2,
      // 不进行懒加载处理的图片数量，如果为0，即所有图片都进行懒加载处理， 如果设置为-1，则所有图片都不进行懒加载处理
      noLoadImgNum: 5,
      // 首次自动加载几张图片（不论图片是否滚动到视野内），autoLoadImgNum = -1 表示会自动加载完所有图片
      autoLoadImgNum: 5,
      // 针对加载失败的图片 或 beforeLoadOneImgCallback 返回false 的图片，最多尝试加载几次，为了防止死循环，最多5次。以图片的src为纬度统计重试次数
      maxTryTimesPerSrc: 2,
      // 加载一张图片之前的回调函数，函数return false 会终止加载操作
      beforeLoadOneImgCallback: () => {
        return true;
      },
      // 加载一张图片失败之后的回调函数
      failLoadOneImgCallback: () => {},
      // 加载一张图片之后的回调函数，如果图片加载失败，则不会回调该函数
      afterLoadOneImgCallback: () => {},
      // 加载完所有图片后调用的回调函数
      afterLoadAllImgCallback: () => {},
    },
  },
  isPreviewOnly: true,
};

export default config;
