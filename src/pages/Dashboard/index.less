.card {
  display: flex;
  height: 102px;
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  ~ .card {
    margin-left: 24px;
  }
  img {
    width: 100px;
    margin-right: 24px;
    filter: hue-rotate(318deg) opacity(0.78);
  }
  .card-info {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .title {
      color: @colorTextSecondary;
      font-size: 16px;
      line-height: 30px;
    }
    .total {
      color: @colorText;
      font-weight: 500;
      font-size: 22px;
      font-family: '阿里妈妈数黑体 Bold';
      line-height: 30px;
      cursor: pointer;
      font-variant-numeric: tabular-nums;
      a {
        color: @colorText;
      }
    }
    ~ .card-info {
      border-left: solid 1px @colorFillSecondary;
    }
  }
}
.chart-box {
  margin-bottom: 24px;
  padding: 24px;
  text-align: right;
  background-color: #fff;
  border-radius: 4px;
  .chart-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    color: @colorText;
    font-weight: 500;
    font-size: 16px;
  }
}
