import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { USERS_STATUS } from '@/enums';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import { userGroupDelete, userGroupGet, userGroupUpdate } from '@/services/http/userGroup';
import { option2enum, queryPagingTable, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { history, useModel, useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';

const UserGroups: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<RK_API.UserGroupCreateRequest[]>([]);
  const { initialState } = useModel('@@initialState');

  const onEdit = (id?: string) => {
    history.push(`/system/users/user-groups/edit/${id}`);
  };

  // 删除
  const { run: deleteRecord } = useRequest((ids) => userGroupDelete({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: RK_API.UserGroupDTO[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.usrgrpid!);
      names.push(item.name!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除用户组“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 更新状态

  const { run: update, fetches } = useRequest((params) => userGroupUpdate(params), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success('操作成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
    fetchKey: (params) => params.usrgrpid,
  });

  const UpdateStateBtn: React.FC<{ record: RK_API.UserGroupDTO }> = ({ record }) => {
    const { users_status, usrgrpid } = record;

    // 用户不能将自己禁用
    const existOneself = record.users?.some(
      (item) => item.userid === initialState?.currentUser?.userid,
    );
    let text = '';
    switch (users_status) {
      case 0:
        text = '禁用';
        break;
      case 1:
        text = '启用';
        break;
      default:
        break;
    }

    return (
      <Button
        type="link"
        key="status"
        loading={fetches?.[usrgrpid!]?.loading}
        disabled={existOneself}
        onClick={() => {
          update({
            usrgrpid,
            users_status: users_status === 0 ? 1 : 0,
          });
        }}
      >
        {text}
      </Button>
    );
  };

  // 表格
  const columns: ProColumns<RK_API.UserGroupDTO>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      width: 200,
      ellipsis: true,
      render: (dom, record) => {
        return <a onClick={() => onEdit(record?.usrgrpid)}>{record.name}</a>;
      },
      initialValue: queryParams.get('name'),
    },
    {
      title: '用户',
      dataIndex: 'users',
      width: 300,
      render: (dom, entity) => {
        const length = entity?.users?.length;
        if (!length) return <>-</>;
        return entity?.users?.map((item, index) => (
          <React.Fragment key={item?.userid}>
            {index > 0 && <a>,</a>}
            <Button
              type="link"
              onClick={() => {
                history.push(`/system/users/list/edit/${item?.userid}`);
              }}
            >
              {item?.username}
            </Button>
          </React.Fragment>
        ));
      },
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'users_status',
      width: 100,
      valueEnum: option2enum(USERS_STATUS),
      search: {
        transform: (value, namePath) => ({ [namePath]: Number(value) }),
      },
      initialValue: queryParams.get('users_status'),
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <UpdateStateBtn record={record} />
            <a key="delete" onClick={() => handleDelete([record])}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<RK_API.UserGroupCreateRequest>
        {...defaultTableConfig}
        search={SearchOptionRender}
        onSubmit={syncToUrl}
        actionRef={tableRef}
        rowKey="usrgrpid"
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columns={columns}
        headerTitle="用户组列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                history.push('/system/users/user-groups/add');
              }}
            >
              新建用户组
            </Button>,
          ],
        }}
        request={async ({ name, ...restParams }) =>
          queryPagingTable<API.UserGroupGetReqeust>(
            {
              filter: {
                ...restParams,
              },
              search: {
                name,
              },
              selectUsers: 'extend',
              sortfield: ['name'],
              output: 'extend',
            },
            userGroupGet,
          )
        }
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
});

export default UserGroups;
