import { findGlobalConfig, updateGlobalConfig } from '@/services/http/globalConfig';
import { requiredRule } from '@/utils/setting';
import { SettingOutlined } from '@ant-design/icons';
import { ModalForm, ModalFormProps, ProFormSelect } from '@ant-design/pro-components';
import { Button, message } from 'antd';
import { FC, memo } from 'react';

const GlobalSetting: FC<ModalFormProps> = () => {
  return (
    <ModalForm
      width={520}
      title="全局设置"
      trigger={
        <Button type="primary" icon={<SettingOutlined />}>
          全景设置
        </Button>
      }
      modalProps={{
        destroyOnClose: true,
        centered: true,
      }}
      onFinish={async (values) => {
        const res = await updateGlobalConfig(values);
        const success = res?.data;
        if (success) {
          message.success('保存成功！');
        }
        return success;
      }}
      initialValues={{
        refreshSeconds: 10,
      }}
      request={async () => {
        const res = await findGlobalConfig();
        return res?.data || {};
      }}
    >
      <ProFormSelect
        label="刷新频率"
        name="refreshSeconds"
        rules={[requiredRule]}
        allowClear={false}
        options={[
          {
            label: '10秒',
            value: 10,
          },
          {
            label: '30秒',
            value: 30,
          },
          {
            label: '60秒',
            value: 60,
          },
          {
            label: '2分钟',
            value: 120,
          },
          {
            label: '5分钟',
            value: 300,
          },
        ]}
      />
    </ModalForm>
  );
};

export default memo(GlobalSetting);
