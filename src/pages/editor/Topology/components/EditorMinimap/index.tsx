import { DoubleLeftOutlined, DoubleRightOutlined } from '@ant-design/icons';
import { useBoolean } from 'ahooks';
import { Card, Space } from 'antd';
import classNames from 'classnames/bind';
import React from 'react';
import styles from './index.less';
const cx = classNames.bind(styles);
const EditorMinimap = () => {
  const [state, { toggle }] = useBoolean(false);
  const Title = React.memo(() => (
    <div onClick={toggle} className={styles.title}>
      <Space>
        {!state && <DoubleLeftOutlined />}
        Minimap
        {state && <DoubleRightOutlined />}
      </Space>
    </div>
  ));
  return (
    <Card
      title={<Title />}
      size="small"
      bordered={false}
      type="inner"
      className={cx('card', {
        expanded: state,
      })}
    >
      <div id="minimapContainer" />
    </Card>
  );
};
export default EditorMinimap;
