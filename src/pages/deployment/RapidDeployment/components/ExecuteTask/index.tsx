import TextLoader from '@/components/TextLoader';
import { HOST_USABILITY } from '@/enums';
import {
  taskModuleOrchestrationExecute,
  taskModuleOrchestrationPreTaskExecute,
  taskModuleOrchestrationResult,
} from '@/services/http/taskModuleOrchestration';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProCard, ProColumns, ProFormField, ProTable, StepsForm } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message, Modal } from 'antd';
import { memo, useEffect, useMemo, useRef, useState } from 'react';
import styles from './index.less';

const columns: ProColumns<API.HostPageVO>[] = [
  {
    title: '主机名',
    dataIndex: 'host',
  },
  {
    title: '状态',
    dataIndex: 'usability',
    valueEnum: option2enum(HOST_USABILITY),
  },
];
const ExecuteTask: React.FC<{
  id: string;
  name: string;
}> = ({ id, name }) => {
  const [visible, setVisible] = useState(false);
  const logEndRef = useRef(false);
  const [selectedRowKeys, setSelectedKeys] = useState<string[]>([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [messageApi, contextHolder] = message.useMessage();

  // 执行前置任务并获取 log
  const {
    run: getLog,
    data: result,
    cancel,
  } = useRequest(taskModuleOrchestrationResult, {
    manual: true,
    pollingInterval: 2000,
    onSuccess: (res) => {
      if (!selectedRowKeys.length) {
        const ids = res?.hostList?.map((item) => item.id!) || [];
        setSelectedKeys(ids);
      }
      if (res?.status === 'END') {
        cancel();
        logEndRef.current = true;
      }
    },
  });

  const { run: executeCheckTask } = useRequest(
    () => taskModuleOrchestrationPreTaskExecute({ id }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.id) {
          getLog({ id: res.id });
        }
      },
    },
  );

  useEffect(() => {
    if (visible) {
      executeCheckTask();
      logEndRef.current = false;
      setCurrentStep(0);
    }
  }, [id, visible]);

  const tabItems = useMemo(
    () =>
      result?.taskModuleExecuteList?.map((item) => ({
        label: `${item?.taskModule?.name}(${item.preInspectionTaskResult?.atomicTask?.name})` || '',
        key: item.preInspectionTaskResult?.id || '',
        children: (
          <div className={styles['log-content']}>
            <ProFormField
              style={{ marginBottom: 0 }}
              valueType="jsonCode"
              text={
                <>
                  {item.preInspectionTaskResult?.log || ''}
                  {!logEndRef.current && <TextLoader />}
                </>
              }
              mode="read"
              // @ts-ignore
              width="auto"
            />
          </div>
        ),
      })),
    [result, logEndRef.current],
  );

  return (
    <>
      {contextHolder}
      <Button type="link" className="ant-btn-link" onClick={() => setVisible(true)}>
        执行
      </Button>
      <StepsForm
        current={currentStep}
        onCurrentChange={setCurrentStep}
        onFinish={async () => {
          const res = await taskModuleOrchestrationExecute(
            { id },
            {
              hosts: selectedRowKeys,
            },
          );
          if (res.data) {
            messageApi.success('操作成功！');
            setVisible(false);
          }
        }}
        stepsFormRender={(dom, submitter) => {
          return (
            <Modal
              className={styles['modal-form']}
              title={`执行 ${name}`}
              onCancel={() => setVisible(false)}
              open={visible}
              footer={submitter}
              destroyOnClose
            >
              {dom}
            </Modal>
          );
        }}
        submitter={{
          render: (props) => {
            if (props.step === 0) {
              return (
                <Button
                  type="primary"
                  onClick={() => props.onSubmit?.()}
                  disabled={!logEndRef.current}
                >
                  下一步
                </Button>
              );
            }
            return [
              <Button key="back" onClick={() => props.onPre?.()}>
                上一步
              </Button>,
              <Button type="primary" key="submit" onClick={() => props.onSubmit?.()}>
                执行
              </Button>,
            ];
          },
        }}
      >
        <StepsForm.StepForm name="base" title="检查可用性">
          <ProCard
            tabs={{
              items: tabItems,
            }}
          />
        </StepsForm.StepForm>
        <StepsForm.StepForm name="checkbox" title="确认执行主机">
          <ProTable<API.HostPageVO>
            {...defaultTableConfig}
            pagination={{
              defaultPageSize: 5,
              showSizeChanger: true,
            }}
            className="inner-table"
            search={false}
            options={false}
            columns={columns}
            dataSource={result?.hostList}
            headerTitle="待执行主机列表"
            rowSelection={{
              preserveSelectedRowKeys: true,
              selectedRowKeys,
              onChange: (selectedRowKeys) => {
                setSelectedKeys(selectedRowKeys as string[]);
              },
            }}
          />
        </StepsForm.StepForm>
      </StepsForm>
    </>
  );
};

export default memo(ExecuteTask);
