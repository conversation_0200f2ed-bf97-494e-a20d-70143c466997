.container {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100vw;
  height: 100vh;
  overflow: auto;
  background-color: #11151b;
  background-image: url(/images/bg.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  .header {
    width: 100%;
    // max-width: 1920px;
    height: 80px;
    color: #a5ccf2;
    font-weight: 600;
    font-size: 36px;
    font-family: 'VF Regular';
    line-height: 80px;
    text-align: center;
    background-image: url(/images/union/header.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
  }
  .content {
    position: relative;
    width: 100vw;
    // max-width: 1920px;
    padding: 24px;
  }
}
