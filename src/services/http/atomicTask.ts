// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 此处后端没有提供注释 GET /api/v1/rump/atomic/task/list */
export async function listAtomicTask(options?: { [key: string]: any }) {
  return request<API.ResultListAtomicTaskVO>('/api/v1/rump/atomic/task/list', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/v1/rump/atomic/task/path */
export async function getAtomicTaskPath(
  body: API.AtomicTaskPathRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultAtomicTaskPathVO>('/api/v1/rump/atomic/task/path', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/v1/rump/atomic/task/upload/page */
export async function atomicTaskUploadPage(
  body: API.AtomicTaskUploadPageRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultPageVOAtomicTaskUploadVO>('/api/v1/rump/atomic/task/upload/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/v1/rump/atomic/task/upload/zip */
export async function handleAtomicTaskUpload(body: {}, options?: { [key: string]: any }) {
  return request<any>('/api/v1/rump/atomic/task/upload/zip', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
