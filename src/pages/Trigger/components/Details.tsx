import { HOST_STATUS, SEVERITIES } from '@/enums';
import { zabbix } from '@/services/zabbix';
import { option2enum } from '@/utils';
import {
  PageContainer,
  ProCard,
  ProColumns,
  ProDescriptions,
  ProTable,
} from '@ant-design/pro-components';
import { Link, useParams, useRequest } from '@umijs/max';
import React, { useState } from 'react';

const TriggerDetails: React.FC = () => {
  const { id, dependenciesId } = useParams();
  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '名称',
      dataIndex: 'description',
      render(dom, entity) {
        return (
          <Link to={`/monitor-config/host/trigger/details/${id}/${entity.triggerid}`}>{dom}</Link>
        );
      },
    },
  ];
  const [dependencies, setDependencies] = useState<Record<string, any>[]>([]);

  const { data: triggerData, loading } = useRequest(
    () =>
      zabbix({
        triggerids: dependenciesId ? [dependenciesId] : [id],
        selectTags: 'extend',
        selectItems: 'extend',
        selectDependencies: 'extend',
        method: 'trigger.get',
      }),
    {
      onSuccess: (res: RK_API.Trigger[]) => {
        setDependencies(res?.[0]?.dependencies || []);
      },
      ready: !!id,
    },
  );

  return (
    <PageContainer header={{ title: false }}>
      <ProCard>
        <ProDescriptions
          column={3}
          layout="vertical"
          dataSource={triggerData?.at(0) || {}}
          loading={loading}
        >
          <ProDescriptions.Item label="名称" dataIndex="description" />
          <ProDescriptions.Item label="事件名称" dataIndex="event_name" />
          <ProDescriptions.Item
            label="标签"
            dataIndex="groups"
            renderText={(_, record) => {
              return record?.tags
                ?.map((item: RK_API.TemplateTag) => `${item.tag}:${item.value}`)
                .join('；');
            }}
          />

          <ProDescriptions.Item
            label="启用"
            valueEnum={option2enum(HOST_STATUS)}
            dataIndex="status"
          />
          <ProDescriptions.Item
            label="监控指标"
            dataIndex="rkzl_type"
            renderText={(_, record) => {
              return record?.items?.map((item: RK_API.Item) => item.name).join('；');
            }}
          />

          <ProDescriptions.Item
            label="事件严重等级"
            valueType="radio"
            dataIndex="priority"
            fieldProps={{
              options: SEVERITIES,
            }}
          />
          <ProDescriptions.Item label="条件" dataIndex="expression" />
          <ProDescriptions.Item label="描述" dataIndex="comments" />
        </ProDescriptions>
        {!dependenciesId && (
          <ProTable
            className="inner-table"
            headerTitle="依赖项"
            options={false}
            columns={columns}
            dataSource={dependencies}
            search={false}
            pagination={false}
            size="small"
            rowKey="triggerid"
          />
        )}
      </ProCard>
    </PageContainer>
  );
};

export default TriggerDetails;
