import {
  AUTH_PROTOCOL,
  CONNECTION_TYPE,
  INTERFACE_TYPE,
  PRIV_PROTOCOL,
  SECURITY_LEVEL,
  SNMP_VERSION,
} from '@/enums';
import { getRandomId } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  ModalFormProps,
  ProFormDependency,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@ant-design/pro-components';
import React, { useEffect, useRef } from 'react';

const InterfaceSetter: React.FC<ModalFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  const formRef = useRef<ProFormInstance>();
  const handleTypeChange = (val: string) => {
    switch (val) {
      case '1':
        formRef.current?.setFieldValue('port', '10050');
        break;
      case '2':
        formRef.current?.setFieldValue('port', '161');
        break;
      case '3':
        formRef.current?.setFieldValue('port', '12345');
        break;
      case '4':
        formRef.current?.setFieldValue('port', '623');
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (initialValues) formRef.current?.setFieldsValue(initialValues);
  }, [initialValues]);
  return (
    <DrawerForm<RK_API.Interface>
      formRef={formRef}
      width={460}
      title="主机接口"
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        onFinish?.(value);
        return true;
      }}
      autoFocusFirstInput
      initialValues={{
        useip: '1',
        details: {
          bulk: '1',
          securitylevel: '0',
          authprotocol: '0',
          privprotocol: '0',
        },
        main: '0',
        key: getRandomId(),
        ip: '127.0.0.1',
        dns: '',
      }}
      drawerProps={{
        destroyOnClose: true,
      }}
    >
      {/* 不需要展示，只是为了form传值 */}
      <div className="rk-none">
        <ProFormText name="interfaceid" placeholder="请输入" />
        <ProFormText name="key" />
      </div>
      <ProFormSelect
        label="类型"
        name="type"
        options={INTERFACE_TYPE}
        rules={[requiredRule]}
        fieldProps={{
          onChange: handleTypeChange,
        }}
      />
      {/* <ProFormSwitch name="main" label="默认" transform={(val) => ({ main: val ? 1 : 0 })} /> */}
      <ProFormRadio.Group name="useip" label="连接到" options={CONNECTION_TYPE} />

      <ProFormDependency name={['useip']}>
        {({ useip }) => {
          const isIp = useip === '1';
          return (
            <>
              <ProFormText name="ip" label="IP" rules={isIp ? [requiredRule] : []} />
              <ProFormText name="dns" label="DNS名称" rules={!isIp ? [requiredRule] : []} />
            </>
          );
        }}
      </ProFormDependency>

      <ProFormText name="port" label="端口" rules={[requiredRule]} />

      <ProFormDependency name={['type']}>
        {({ type }) => {
          return (
            // 类型为SNMP
            type === '2' && (
              <ProFormSelect
                name={['details', 'version']}
                label="SNMP接口版本"
                options={SNMP_VERSION}
                rules={[requiredRule]}
              />
            )
          );
        }}
      </ProFormDependency>
      <ProFormDependency name={[['details', 'version']]}>
        {({ details }) => {
          return (
            // SNMP version等于SNMPv3
            ['1', '2'].includes(details?.version) && (
              <ProFormText name={['details', 'community']} label="SNMP community" />
            )
          );
        }}
      </ProFormDependency>
      <ProFormDependency name={['details', 'version']}>
        {({ details }) => {
          return (
            // SNMP version等于SNMPv3
            details?.version === '3' && (
              <>
                <ProFormText name={['details', 'contextname']} label="上下文名称" />
                <ProFormText name={['details', 'securityname']} label="安全名称" />
                <ProFormSelect
                  options={SECURITY_LEVEL}
                  name={['details', 'securitylevel']}
                  label="安全级别"
                  fieldProps={{
                    allowClear: false,
                  }}
                />
              </>
            )
          );
        }}
      </ProFormDependency>
      <ProFormDependency name={[['details', 'securitylevel']]}>
        {({ details }) => {
          return (
            // 安全级别等于authNoPriv或authPriv
            ['1', '2'].includes(details?.securitylevel) && (
              <>
                <ProFormSelect
                  options={AUTH_PROTOCOL}
                  name={['details', 'authprotocol']}
                  label="验证协议"
                  fieldProps={{
                    allowClear: false,
                  }}
                />
                <ProFormText name={['details', 'authpassphrase']} label="验证口令" />
              </>
            )
          );
        }}
      </ProFormDependency>
      <ProFormDependency name={[['details', 'securitylevel']]}>
        {({ details }) => {
          return (
            // 安全级别等于authPriv
            ['2'].includes(details?.securitylevel) && (
              <>
                <ProFormSelect
                  options={PRIV_PROTOCOL}
                  name={['details', 'privprotocol']}
                  label="隐私协议"
                  fieldProps={{
                    allowClear: false,
                  }}
                />
                <ProFormText name={['details', 'privpassphrase']} label="私钥" />
              </>
            )
          );
        }}
      </ProFormDependency>
      <ProFormDependency name={['type']}>
        {({ type }) => {
          return (
            // 类型为SNMP
            type === '2' && (
              <ProFormSwitch
                name={['details', 'bulk']}
                label="批量请求"
                getValueFromEvent={(val) => (val ? '1' : '0')}
                getValueProps={(value) => ({ checked: value === '1' })}
              />
            )
          );
        }}
      </ProFormDependency>
    </DrawerForm>
  );
};

export default InterfaceSetter;
