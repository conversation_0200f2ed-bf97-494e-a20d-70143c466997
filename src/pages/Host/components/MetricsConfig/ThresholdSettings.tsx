import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import { STATUS } from '@/enums';
import { option2enum, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { useLocation } from '@umijs/max';
import { Button, message, Modal, Space, Switch, Tag } from 'antd';

// 模拟标签数据
const tagOptions = [
  { label: '系统', value: '系统' },
  { label: '性能', value: '性能' },
  { label: '网络', value: '网络' },
  { label: '存储', value: '存储' },
  { label: '安全', value: '安全' },
];

// 模拟事件等级数据
const eventLevelOptions = [
  { label: '信息', value: '1', color: 'blue' },
  { label: '警告', value: '2', color: 'orange' },
  { label: '一般严重', value: '3', color: 'volcano' },
  { label: '严重', value: '4', color: 'red' },
  { label: '灾难', value: '5', color: 'purple' },
];

// 模拟阈值数据
const mockThresholdData = [
  {
    id: '1',
    name: 'CPU使用率超过80%',
    eventLevel: '2',
    metricName: 'CPU使用率',
    tags: ['系统', '性能'],
    description: 'CPU使用率超过80%时触发警告',
    status: '0',
  },
  {
    id: '2',
    name: 'CPU使用率超过90%',
    eventLevel: '3',
    metricName: 'CPU使用率',
    tags: ['系统', '性能'],
    description: 'CPU使用率超过90%时触发一般严重告警',
    status: '0',
  },
  {
    id: '3',
    name: '内存使用率超过80%',
    eventLevel: '2',
    metricName: '内存使用率',
    tags: ['系统', '性能'],
    description: '内存使用率超过80%时触发警告',
    status: '0',
  },
  {
    id: '4',
    name: '内存使用率超过90%',
    eventLevel: '3',
    metricName: '内存使用率',
    tags: ['系统', '性能'],
    description: '内存使用率超过90%时触发一般严重告警',
    status: '0',
  },
  {
    id: '5',
    name: '磁盘使用率超过85%',
    eventLevel: '2',
    metricName: '磁盘使用率',
    tags: ['系统', '存储'],
    description: '磁盘使用率超过85%时触发警告',
    status: '0',
  },
  {
    id: '6',
    name: '磁盘使用率超过95%',
    eventLevel: '4',
    metricName: '磁盘使用率',
    tags: ['系统', '存储'],
    description: '磁盘使用率超过95%时触发严重告警',
    status: '0',
  },
  {
    id: '7',
    name: '网络流入速率超过100Mbps',
    eventLevel: '1',
    metricName: '网络流入速率',
    tags: ['网络'],
    description: '网络流入速率超过100Mbps时触发信息告警',
    status: '0',
  },
  {
    id: '8',
    name: '网络流出速率超过100Mbps',
    eventLevel: '1',
    metricName: '网络流出速率',
    tags: ['网络'],
    description: '网络流出速率超过100Mbps时触发信息告警',
    status: '0',
  },
];

const ThresholdSettings: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [, setSelectedRows] = useState<any[]>([]);
  // const { id } = useParams<{ id: string }>();

  const { search } = useLocation();
  const params = new URLSearchParams(search);
  const metricName = params.get('metricName') || '';

  // 模拟删除操作
  const handleDelete = async (rows: any[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id);
      names.push(item.name);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除阈值"${names.join('、')}"吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        // 实际项目中应该调用API删除数据
        message.success('删除成功');
        tableRef.current?.reloadAndRest?.();
      },
    });
  };

  // 跳转到编辑阈值页面
  const handleEdit = (record: any) => {
    window.location.href = `/monitor-config/host/metrics-config/threshold/edit/${record.id}`;
  };

  // 模拟状态更新
  const updateStatus = async () => {
    message.success('状态更新成功');
    tableRef.current?.reloadAndRest?.();
  };

  // 表格列定义
  const columns: ProColumns<any>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      width: 200,
      // `/monitor-config/host/trigger/details/${entity.triggerid}`
    },
    {
      title: '事件等级',
      dataIndex: 'eventLevel',
      width: 100,
      valueType: 'select',
      search: false,
      valueEnum: option2enum(eventLevelOptions),
      render: (_, record) => {
        const level = eventLevelOptions.find((item) => item.value === record.eventLevel);
        return <Tag color={level?.color}>{level?.label}</Tag>;
      },
    },
    {
      title: '所属指标',
      dataIndex: 'metricName',
      width: 150,
      initialValue: metricName,
    },
    {
      title: '标签',
      dataIndex: 'tags',
      width: 150,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        options: tagOptions,
      },
      render: (_, record) => (
        <>
          {record.tags.map((tag: string) => (
            <Tag key={tag}>{tag}</Tag>
          ))}
        </>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 300,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueType: 'select',
      valueEnum: option2enum(STATUS),
      render: (_, record) => (
        <Switch
          checked={record.status === '0'}
          onChange={() => {
            updateStatus();
          }}
        />
      ),
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space>
            <a key="edit" onClick={() => handleEdit(record)}>
              编辑
            </a>
            <a key="del" onClick={() => handleDelete([record])}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer header={{ title: false }}>
      <ProTable<any>
        {...defaultTableConfig}
        search={{ ...SearchOptionRender, labelWidth: 100 }}
        onSubmit={syncToUrl}
        rowKey="id"
        actionRef={tableRef}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columns={columns}
        headerTitle={`${metricName} 阈值设置`}
        toolBarRender={() => [
          <Button key="add" type="primary">
            新增阈值
          </Button>,
        ]}
        request={async (params) => {
          const { name, metricName, tags, status } = params;

          let filteredData = [...mockThresholdData];

          if (name) {
            filteredData = filteredData.filter((item) =>
              item.name.toLowerCase().includes(name.toLowerCase()),
            );
          }

          if (metricName) {
            filteredData = filteredData.filter((item) =>
              item.metricName.toLowerCase().includes(metricName.toLowerCase()),
            );
          }

          if (tags && tags.length > 0) {
            filteredData = filteredData.filter((item) =>
              tags.some((tag: string) => item.tags.includes(tag)),
            );
          }

          if (status) {
            filteredData = filteredData.filter((item) => item.status === status);
          }

          return {
            data: filteredData,
            success: true,
            total: filteredData.length,
          };
        }}
      />
    </PageContainer>
  );
};

export default ThresholdSettings;
