import {
  ActionType,
  PageContainer,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import React, { useMemo, useRef, useState } from 'react';

import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { USERS_STATUS } from '@/enums';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import { useRoleList } from '@/hooks/useRoleList';
import { userDelete } from '@/services/http/user';
import { userGroupGet } from '@/services/http/userGroup';
import { zabbixList } from '@/services/zabbix';
import { option2enum, queryOptions, queryPagingTable, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { history, useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';

const Users: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const formRef = useRef<ProFormInstance>();

  const { roleList, loading: roleLoading } = useRoleList();

  const [selectedRows, setSelectedRows] = useState<RK_API.UserInfoResponse[]>([]);
  // 用户组

  const onEdit = (record: RK_API.UserInfoResponse) => {
    history.push(`/system/users/list/edit/${record.userid}`);
  };

  // 删除
  const { run: deleteRecord } = useRequest((ids) => userDelete({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: RK_API.UserInfoResponse[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.userid!);
      names.push(item.username!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除用户“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<RK_API.UserInfoResponse>[] = useMemo(
    () => [
      {
        title: '用户名',
        dataIndex: 'username',
        width: 150,
        ellipsis: true,
        render: (dom, record) => {
          return <a onClick={() => onEdit(record)}>{record.username}</a>;
        },
        initialValue: queryParams.get('username'),
      },
      {
        title: '角色',
        width: 150,
        dataIndex: 'roleid',
        valueType: 'select',
        fieldProps: {
          mode: 'multiple',
          options: roleList,
          loading: roleLoading,
          fieldNames: {
            value: 'roleid',
            label: 'name',
          },
        },
        initialValue: queryParams.getAll('roleid'),
      },
      {
        title: '所属群组',
        dataIndex: 'usrgrps',
        width: 200,
        render: (dom, entity) => {
          const { usrgrps = [] } = entity;
          if (!usrgrps.length) return <>-</>;
          return usrgrps.map((item, index) => (
            <React.Fragment key={item.usrgrpid}>
              {index > 0 && <a>,</a>}
              <Button
                type="link"
                danger={item?.users_status === '1'}
                onClick={() => history.push(`/system/users/user-groups/edit/${item.usrgrpid}`)}
              >
                {item.name}
              </Button>
            </React.Fragment>
          ));
        },
        hideInSearch: true,
      },
      {
        title: '用户群组',
        dataIndex: 'usrgrpids',
        width: 200,
        valueType: 'select',
        fieldProps: {
          mode: 'multiple',
          fieldNames: {
            label: 'name',
            value: 'usrgrpid',
          },
        },
        initialValue: queryParams.getAll('usrgrpids'),
        request: () => queryOptions({ output: 'extend' }, userGroupGet),
        hideInTable: true,
      },
      {
        title: '状态',
        dataIndex: 'users_status',
        width: 100,
        valueEnum: option2enum(USERS_STATUS),
        hideInSearch: true,
      },
      {
        title: '操作',
        width: 120,
        key: 'option',
        valueType: 'option',
        align: 'center',
        fixed: 'right',
        render: (text, record) => {
          return (
            <Space>
              <a key="del" onClick={() => handleDelete([record])}>
                删除
              </a>
            </Space>
          );
        },
      },
    ],
    [roleList],
  );

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<RK_API.UserInfoResponse>
        {...defaultTableConfig}
        search={SearchOptionRender}
        onSubmit={syncToUrl}
        rowKey="userid"
        actionRef={tableRef}
        formRef={formRef}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columns={columns}
        headerTitle="用户列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                history.push('/system/users/list/add');
              }}
            >
              新建用户
            </Button>,
          ],
        }}
        request={async ({ usrgrpids = [], ...restParams }) => {
          return queryPagingTable<RK_API.UserInfoRequest>(
            {
              search: {
                ...restParams,
              },
              usrgrpids: usrgrpids?.length ? usrgrpids : undefined,
              selectRole: 'extend',
              selectUsrgrps: 'extend',
              sortfield: ['username'], // 排序
              getAccess: true, // 添加关于用户权限附加信息
              method: 'user.get',
            },
            zabbixList,
          );
        }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
});

export default Users;
