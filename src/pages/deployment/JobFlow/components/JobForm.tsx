import YAMLEditor from '@/components/YAMLEditor';
import BaseContext from '@/Context/BaseContext';
import { RUN_MODE } from '@/enums';
import { requiredRule } from '@/utils/setting';
import { ProForm, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { DefaultOptionType } from 'antd/es/cascader';
import { useContext, useRef } from 'react';

const JobForm = () => {
  const editorRef = useRef();
  const { atomicTask = [], atomicTaskLoading } = useContext(BaseContext);

  return (
    <>
      <div style={{ display: 'none' }}>
        <ProFormText name="nodeId" />
        <ProFormText name="taskName" />
      </div>
      <ProFormSelect
        fieldProps={{
          options: atomicTask as DefaultOptionType[],
          loading: atomicTaskLoading,
          fieldNames: {
            value: 'id',
            label: 'name',
          },
          showSearch: true,
        }}
        required
        name="taskId"
        label="原子任务"
        allowClear={false}
        rules={[requiredRule]}
        transform={(value, namePath) => ({
          [namePath]: value,
          taskName: atomicTask.find((item) => item.id === value)?.name,
        })}
      />
      <ProFormSelect
        options={RUN_MODE}
        required
        name="runMode"
        label="运行方式"
        allowClear={false}
        rules={[requiredRule]}
      />

      <ProForm.Item
        name="yamlVariables"
        label="变量 "
        validateTrigger={false}
        rules={[
          () => ({
            validator() {
              // @ts-ignore
              const errors = editorRef.current?.getModelMarkers();
              if (!errors.length) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('请检查yaml格式'));
            },
          }),
        ]}
      >
        <YAMLEditor
          onError={(editor) => {
            editorRef.current = editor;
          }}
        />
      </ProForm.Item>
    </>
  );
};

export default JobForm;
