import { RingProgress, RingProgressConfig } from '@ant-design/charts';
import React from 'react';
export type RingProgressProps = Partial<RingProgressConfig> & {
  header?: string;
};

const RkRingProgress: React.FC<RingProgressProps> = ({ percent = 0, header = '', ...rest }) => {
  const config: RingProgressConfig = {
    autoFit: true,
    percent,
    color: ['#5B8FF9', '#E8EDF3'],
    statistic: {
      title: {
        style: {
          color: 'rgba(0, 0, 0, 0.65)',
          fontSize: '14px',
          fontWeight: 'bold',
          lineHeight: '22px',
        },
        formatter: () => header,
      },
    },

    ...rest,
  };
  return <RingProgress {...config} />;
};

export default RkRingProgress;
