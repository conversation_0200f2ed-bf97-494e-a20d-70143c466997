import RKCol from '@/components/RKCol';
import { zabbix, zabbixPost } from '@/services/zabbix';
import { onSuccessAndGoBack, queryFormData } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProColumns,
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormSwitch,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import { Button, Row } from 'antd';
import React, { useCallback, useRef, useState } from 'react';
import AssociatedUserModal from '../AssociatedUserModal';
import PermissionSetter from '../PermissionSetter';

type GroupsDetailsProps = {
  username: string;
  usrgrpid?: string;
  gui_access: number;
  name: string;
  users_status: number;
  users: RK_API.User[];
  rights: RK_API.Auth[];
  tag_filters?: GroupsDetailsProps[];
};

const GroupsDetails: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const [userList, setUserList] = useState<RK_API.User[]>([]);
  const [userModalVisit, setUserModalVisit] = useState(false);

  // 判断是否为编辑页面
  const { id } = useParams();
  const isEditPage = !!id;

  // 新建
  const { run: add, loading: addLoading } = useRequest(
    (value) => zabbixPost({ ...value, method: 'usergroup.create' }),
    {
      manual: true,
      onSuccess: onSuccessAndGoBack,
      formatResult: (res) => res,
    },
  );
  // 修改
  const { run: update, loading: editLoading } = useRequest(
    (value) => zabbixPost({ ...value, method: 'usergroup.update' }),
    {
      manual: true,
      onSuccess: onSuccessAndGoBack,
      formatResult: (res) => res,
    },
  );

  // 删除用户
  const userDelete = useCallback((id: string) => {
    const userList: RK_API.User[] = formRef.current?.getFieldValue('users');
    const list = userList.filter((item) => item.userid !== id);
    formRef.current?.setFieldsValue({
      users: list,
    });
    formRef.current?.setFieldValue('users', list);

    setUserList(list);
  }, []);

  // 用户columns
  const userColumns: ProColumns<RK_API.User>[] = [
    {
      dataIndex: 'username',
      title: '用户名称',
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => [
        <a key="userDelete" onClick={() => userDelete(record.userid!)}>
          删除
        </a>,
      ],
    },
  ];

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<GroupsDetailsProps>
        formRef={formRef}
        initialValues={{
          gui_access: 0, // 0 - (default) 使用系统默认身份验证方法; 1 - 使用内部认证; 2 - 使用LDAP认证; 3 - 禁止访问前端.
          users_status: '0', //0 - (default) 启用; 1 - 禁用.
          debug_mode: '0', // 0 - (default) 禁用; 1 - 启用.
          tag_filters: [],
        }}
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          onReset: () => {
            history.go(-1);
          },

          render: (props, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
          submitButtonProps: {
            loading: addLoading || editLoading,
          },
        }}
        onFinish={async (values) => {
          const { users = [], rights = [], tag_filters = [], ...rest } = values;
          const data = {
            ...rest,
            tag_filters,
            users: users.map((item) => ({ userid: item.userid })),
            rights: rights
              .filter((item) => item.permission && item.permission !== -1)
              .map(({ id, permission }) => ({ id, permission })),
          };

          if (isEditPage) {
            update(data);
          } else {
            add(data);
          }
        }}
        request={async () => {
          const data = await queryFormData(
            {
              usrgrpids: [id],
              selectUsers: 'extend',
              selectRights: 'extend',
              method: 'usergroup.get',
            },
            isEditPage,
            // userGroupGet,
            zabbix,
          );
          setUserList(data.users);
          return data;
        }}
      >
        {/* 不需要展示，只是为了form传值 */}
        <div className="rk-none">
          <ProFormText name="usrgrpid" />
          <ProFormDigit name="gui_access" />
          <ProFormText name="debug_mode" />
          <ProFormText name="tag_filters" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText label="名称" name="name" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormSwitch
              label="启用"
              name="users_status"
              getValueFromEvent={(val) => (val ? '0' : '1')}
              getValueProps={(value) => ({ checked: value === '0' })}
            />
          </RKCol>
        </Row>
        <ProTable<API.UserInfoResponse>
          className="inner-table"
          headerTitle="关联用户"
          search={false}
          options={false}
          pagination={{
            pageSize: 5,
          }}
          dataSource={userList}
          columns={userColumns}
          rowKey="userid"
          toolbar={{
            actions: [
              <Button
                key="primary"
                type="primary"
                onClick={() => {
                  setUserModalVisit(true);
                }}
              >
                关联用户
              </Button>,
            ],
          }}
        />
        <ProForm.Item name="users" style={{ display: 'none' }}>
          <AssociatedUserModal
            open={userModalVisit}
            onOk={(keys, selectKeys) => {
              setUserModalVisit(false);
              setUserList(selectKeys);
            }}
            onCancel={() => {
              setUserModalVisit(false);
            }}
          />
        </ProForm.Item>
        <ProForm.Item name="rights">
          <PermissionSetter />
        </ProForm.Item>
      </ProForm>
    </PageContainer>
  );
};

export default GroupsDetails;
