import { PageLoading } from '@ant-design/pro-components';
import { useEmotionCss } from '@ant-design/use-emotion-css';
import React from 'react';

const RklinkLoading: React.FC = () => {
  const containerClassName = useEmotionCss(() => {
    return {
      width: '100%',
      height: '100vh',
      paddingTop: 150,
    };
  });
  return (
    <div className={containerClassName}>
      <PageLoading />
    </div>
  );
};

export default RklinkLoading;
