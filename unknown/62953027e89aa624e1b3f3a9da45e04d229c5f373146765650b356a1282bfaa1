import MacrosSetter, { DataSourceType } from '@/components/MacrosSetter';
import RKCol from '@/components/RKCol';
import TagSetter from '@/components/TagsSetter';
import { useHostGroupList } from '@/hooks/useHostGroupList';
import { useTemplateList } from '@/hooks/useTemplateList';
import { create as createTemplate, update as updateTemplate } from '@/services/http/template';
import { zabbix } from '@/services/zabbix';
import { getRandomId, onSuccessAndGoBack } from '@/utils';
import { requiredRule } from '@/utils/setting';
import { name1Reg } from '@/utils/validator';
import {
  EditableFormInstance,
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import { Row } from 'antd';
import React, { useEffect, useRef } from 'react';

type ItemProps = RK_API.GlobalMacro &
  RK_API.HostMacro & {
    key?: string;
    inheritName?: string;
    inheritType?: string;
  };

interface MergedItem extends ItemProps {
  defaultValue?: string;
}

const mergeArrays = (inheritMacro: ItemProps[], ownMacro: ItemProps[]): MergedItem[] => {
  const newInheritMacro = inheritMacro.map((item) => ({
    ...item,
    defaultValue: item.value,
    key: getRandomId(),
  }));
  const mergedArray: MergedItem[] = newInheritMacro;
  ownMacro.forEach((item) => {
    const existingItemIndex = mergedArray.findIndex((m) => m.macro === item.macro);
    if (existingItemIndex !== -1) {
      mergedArray[existingItemIndex] = { ...mergedArray[existingItemIndex], ...item };
    } else {
      mergedArray.push(item);
    }
  });

  return mergedArray;
};

const TemplateDetails: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const editorFormRef = useRef<EditableFormInstance<DataSourceType>>(null);

  // 判断是否为编辑页面
  const { id } = useParams();
  const isEditPage = !!id;

  // 新建
  const { run: add, loading: addLoading } = useRequest((value) => createTemplate(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 修改
  const { run: update, loading: updateLoading } = useRequest((value) => updateTemplate(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });

  // 对象群组
  const { hostGroupList, loading: hostGroupLoading } = useHostGroupList();
  // 模版
  const { templateList, loading: templateLoading } = useTemplateList();

  // 继承的模版名称map
  const inheritHostMacroMap = useRef<Record<string, string>>();

  // 查询继承的全局宏
  const { data: globalMacro, run: getGlobalMacro } = useRequest(
    () =>
      zabbix({
        globalmacro: true,
        sortfield: 'macro',
        method: 'usermacro.get',
      }),
    {
      manual: true,
    },
  );
  // 查询继承的用户宏
  const { data: hostMacro, run: getHostMacro } = useRequest(
    (hostids) =>
      zabbix({
        hostids,
        sortfield: 'macro',
        method: 'usermacro.get',
      }),
    {
      manual: true,
    },
  );

  // 模版详情
  const { data } = useRequest(
    () =>
      zabbix({
        templateids: [id],
        selectGroups: 'extend',
        selectParentTemplates: 'extend',
        selectTags: 'extend',
        selectValueMaps: 'extend',
        selectMacros: 'extend',
        method: 'template.get',
      }),
    {
      ready: isEditPage,
      onSuccess: (res) => {
        const info = res?.[0] || {};
        const { parentTemplates = [] } = info;

        getGlobalMacro();
        // 如果有父默模版
        if (parentTemplates.length) {
          const map: Record<string, string> = {};
          const ids = parentTemplates.map((item: RK_API.Template) => {
            map[item.templateid!] = item.name!;
            return item.templateid;
          });
          inheritHostMacroMap.current = map;
          getHostMacro(ids);
        }
        formRef.current?.setFieldsValue?.(info);
      },
    },
  );

  // 新增页面查询全局宏
  useEffect(() => {
    if (!isEditPage) {
      getGlobalMacro();
    }
  }, []);

  useEffect(() => {
    const info = data?.[0] || {};
    // 模版自己的宏
    const { macros = [] } = info;
    // 继承宏
    const inheritMacro = [
      ...(globalMacro?.map((item: RK_API.GlobalMacro) => ({ ...item, inheritType: 'global' })) ||
        []),
      ...(hostMacro?.map((item: Record<string, any>) => ({
        ...item,
        inheritType: 'host',
        inheritName: inheritHostMacroMap.current?.[item.hostid],
      })) || []),
    ];
    const mergeMacro = mergeArrays(inheritMacro as ItemProps[], macros);
    formRef.current?.setFieldsValue?.({
      macros: mergeMacro,
    });
  }, [globalMacro, hostMacro]);

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm<RK_API.TemplateCreateCommand>
        formRef={formRef}
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          onReset: () => {
            history.go(-1);
          },

          render: (props, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
          submitButtonProps: {
            loading: addLoading || updateLoading,
          },
        }}
        onFinish={async (values) => {
          const res = {
            ...values,
            name: values.name || values.host,
          };
          // 宏配置的表单验证
          editorFormRef.current?.validateFields().then(() => {
            if (isEditPage) {
              update(res);
            } else {
              add(res);
            }
          });
        }}
      >
        {/* 不需要展示，只是为了form传值 */}
        <div className="rk-none">
          <ProFormText name="templateid" placeholder="请输入" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText
              label="模版名称"
              name="host"
              rules={[
                requiredRule,
                {
                  pattern: name1Reg,
                  message: '格式必须为字母数字、空格、点、破折号和下划线',
                },
              ]}
              transform={(value, namePath) => ({
                [namePath]: value.trim(),
              })}
            />
          </RKCol>
          <ProFormDependency name={['host']}>
            {({ host }) => {
              return (
                <RKCol>
                  <ProFormText label="可见的名称" placeholder={host} name="name" />
                </RKCol>
              );
            }}
          </ProFormDependency>

          <RKCol>
            <ProFormSelect
              label="模版"
              name="parentTemplates"
              fieldProps={{
                mode: 'multiple',
                showSearch: true,
                options: templateList,
                loading: templateLoading,
                fieldNames: {
                  label: 'name',
                  value: 'templateid',
                },
                onChange: (val, options) => {
                  getHostMacro(val);
                  const map: Record<string, string> = {};
                  options.forEach((item: RK_API.Template) => {
                    map[item.templateid!] = item.name!;
                  });
                  inheritHostMacroMap.current = map;
                },
              }}
              tooltip="将一个或多个“嵌套”监控模板链接到此监控模板"
              convertValue={(value = []) => value.map((item: any) => item?.templateid)}
              getValueFromEvent={(value = []) =>
                value.map((item: string) => ({ templateid: item }))
              }
              transform={(value: RK_API.Template[], namePath) => ({
                [namePath]: undefined,
                templates: value.map(({ templateid }) => ({ templateid })),
              })}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              label="群组"
              name="groups"
              fieldProps={{
                mode: 'multiple',
                showSearch: true,
                fieldNames: {
                  label: 'name',
                  value: 'groupid',
                },
                options: hostGroupList,
                loading: hostGroupLoading,
              }}
              rules={[requiredRule]}
              convertValue={(value = []) => value.map((item: any) => item?.groupid)}
              getValueFromEvent={(value = []) => value.map((item: string) => ({ groupid: item }))}
              transform={(value: RK_API.HostGroup[], namePath) => ({
                [namePath]: value.map(({ groupid }) => ({ groupid })),
              })}
            />
          </RKCol>
          <RKCol lg={12} md={16} sm={24}>
            <ProFormTextArea
              label="描述"
              name="description"
              fieldProps={{
                autoSize: { minRows: 1, maxRows: 3 },
              }}
            />
          </RKCol>
        </Row>
        {/* 标记 */}
        <TagSetter />
        {/* 宏设置 */}
        <ProForm.Item
          name="macros"
          transform={(value: Record<string, any>[], namePath) => ({
            [namePath]: value
              .filter((item) => {
                // if (item.inheritType) {
                //   return item.value !== item.defaultValue;
                // }
                return item;
              })
              .map(({ hostmacroid, macro, value, description }) => ({
                hostmacroid,
                macro,
                value,
                description,
              })),
          })}
        >
          <MacrosSetter editorFormRef={editorFormRef} />
        </ProForm.Item>
      </ProForm>
    </PageContainer>
  );
};

export default TemplateDetails;
