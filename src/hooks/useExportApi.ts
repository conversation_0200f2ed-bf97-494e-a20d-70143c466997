import { zabbixExport } from '@/services/zabbix';
import { useRequest } from '@umijs/max';
import { saveAs } from 'file-saver';

const useExportApi = () => {
  const saveFile = (data: string, params: Record<string, any>[] = []) => {
    const obj = params?.at(0) || {};
    const { format, fileName } = obj;
    const blob = new Blob([data], { type: 'application/octet-stream' });
    saveAs(blob, `${fileName}.${format}`);
  };
  const { error, loading, run } = useRequest((params) => zabbixExport(params), {
    manual: true,
    onSuccess: (data, params) => {
      // 请求成功后的处理逻辑
      if (data) {
        saveFile(data, params);
      }
    },
  });

  return {
    loading,
    error,
    run,
  };
};

export default useExportApi;
