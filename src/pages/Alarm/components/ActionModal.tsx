import { ALERT_COMMAND_STATUS, ALERT_MSG_STATUS, SEVERITIES } from '@/enums';
import { option2enum } from '@/utils';
import {
  ModalForm,
  ModalFormProps,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import React, { memo, useEffect, useRef } from 'react';

const ActionModal: React.FC<ModalFormProps> = ({ open, onOpenChange, initialValues }) => {
  const formRef = useRef<ProFormInstance>();
  const userMapRef = useRef<Record<string, string>>({});
  useEffect(() => {
    initialValues?.list?.forEach((item: any) => {
      item?.users?.forEach((user: Record<string, any>) => {
        userMapRef.current = {
          ...userMapRef.current,
          [user?.userid]: user?.username,
        };
      });
    });
  }, [initialValues]);
  const columns: ProColumns<RK_API.Alert & RK_API.Acknowledge>[] = [
    {
      title: '时间',
      dataIndex: 'clock',
      renderText(text) {
        return dayjs.unix(text).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '用户/接受者',
      dataIndex: 'users',
      renderText(users, entity) {
        const user = users?.at(0) || {};
        // userMapRef.current = {
        //   ...userMapRef.current,
        //   [user?.userid]: user?.username,
        // };
        if (!users && entity.userid) {
          return userMapRef.current?.[entity.userid] || entity.userid;
        }
        return user?.username;
      },
    },

    {
      title: '信息/命令',
      dataIndex: 'mediatypes',
      render(text, entity) {
        if (!entity?.mediatypes) {
          const severities = option2enum(SEVERITIES);
          const strArr = [];
          const { action, message, old_severity, new_severity } = entity as RK_API.Acknowledge;
          let count = Number(action);
          if (old_severity < new_severity) {
            count -= 8;
            strArr.push(
              `问题严重性已增加: ${severities?.[old_severity]?.text} -> ${severities?.[new_severity]?.text}`,
            );
          }
          if (old_severity > new_severity) {
            count -= 8;
            strArr.push(
              `问题严重性已降低: ${severities?.[old_severity]?.text} -> ${severities?.[new_severity]?.text}`,
            );
          }
          if (message) {
            count -= 4;
            strArr.push(`添加了注释: ${message}`);
          }
          if (count >= 2) {
            count -= 2;
            strArr.push('确认问题事件');
          }
          if (count === 1) {
            strArr.push('手动关闭问题');
          }

          return strArr.map((item, index) => <div key={index}>{item}</div>);
        }

        return entity?.mediatypes?.at(0)?.name;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      hideInSearch: true,
      render(dom, entity) {
        const options = entity.alerttype === '0' ? ALERT_MSG_STATUS : ALERT_COMMAND_STATUS;
        const label = options.find((item) => item.value === entity.status)?.label;
        return label;
      },
    },
  ];
  return (
    <ModalForm
      size="small"
      width={600}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      modalProps={{
        destroyOnClose: true,
        centered: true,
        closable: false,
      }}
      submitter={false}
    >
      <ProTable<RK_API.Alert & RK_API.Acknowledge>
        className="inner-table"
        search={false}
        options={false}
        pagination={false}
        rowKey={(record) => record?.alertid || record?.acknowledgeid}
        columns={columns}
        dataSource={initialValues?.list || []}
      />
    </ModalForm>
  );
};

export default memo(ActionModal);
