.panel {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  display: flex;
  flex-direction: column;
  width: 300px;
  height: 100%;
  padding: 24px 10px 16px;
  background-color: #fff;
  transition: all ease-in 0.5s;
  .content {
    flex: 1;
    min-height: 50px;
    overflow-y: scroll;
    :global {
      .ant-collapse-header {
        padding: 12px 0;
      }
      .ant-tooltip {
        .ant-tooltip-inner {
          word-wrap: break-word !important;
          word-break: break-all !important;
        }
      }
    }
  }
  .icon {
    width: 48px;
    height: 48px;
    font-size: 24px;
    line-height: 48px;
    text-align: center;
    background-color: #d4f7ff;
    border-radius: 50%;
  }
  .item {
    cursor: grab;
    .ic {
      margin-right: 4px;
    }
    .gray {
      filter: grayscale(100%);
    }
  }
  .collapsed-icon {
    position: absolute;
    top: 0;
    right: -32px;
    z-index: 3;
  }
  &.collapsed {
    left: -300px;
  }
}
