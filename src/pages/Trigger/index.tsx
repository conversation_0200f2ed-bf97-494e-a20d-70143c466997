import { SEVERITIES, STATUS } from '@/enums';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import { useItemList } from '@/hooks/useItemList';
import { zabbix, zabbixDelete, zabbixPost } from '@/services/zabbix';
import { getTagData, option2enum, queryPagingTable, syncToUrl } from '@/utils';
import { template } from '@/utils/column';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProConfigProvider,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { history, useLocation, useParams, useRequest } from '@umijs/max';
import { message, Modal, Space, Switch, Tag } from 'antd';
import React, { useRef } from 'react';
import { valueTypeMap } from '../HostMonitor/valueTypeMap';

const Trigger: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const formRef = useRef<ProFormInstance>();

  const { state, pathname } = useLocation();
  const { hostId, itemId } = useParams();

  // 获取主机
  const { data: host } = useRequest(
    () =>
      zabbix({
        output: 'extend',
        sortfield: ['name'],
        method: 'host.get',
        hostids: [hostId],
        preservekeys: true,
      }),
    {
      ready: !!hostId,
    },
  );

  // 获取监控项

  const { itemList, itemLoading } = useItemList({
    hostids: hostId ? [hostId] : null,
    itemids: itemId ? [itemId] : null,
  });

  const formTemplate = pathname.includes('template');

  // 返回所有  tag
  const { data: triggerTags } = useRequest(
    () =>
      zabbix({ method: 'trigger.get', output: ['tags'], selectTags: 'extend', hostids: [hostId] }),
    {
      formatResult: (res) => {
        if (res.code === 200) {
          return getTagData(res.data);
        }
      },
    },
  );

  // 更新状态
  const { run: updateStatus, fetches: updateStatusFetches } = useRequest(
    (params) =>
      zabbixPost({
        ...params,
        method: 'trigger.update',
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code !== 200) return;
        message.success('操作成功');
        tableRef.current?.reloadAndRest?.();
      },
      formatResult: (res) => res,
    },
  );

  // 删除
  const { run: deleteRecord } = useRequest(
    (ids) => zabbixDelete({ ids, method: 'trigger.delete' }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code !== 200) return;
        message.success('删除成功');
        tableRef.current?.reloadAndRest?.();
      },
      formatResult: (res) => res,
    },
  );

  const handleDelete = async (rows: RK_API.Trigger[]) => {
    const ids: string[] = [];
    rows.forEach((item) => {
      ids.push(item.triggerid!);
    });
    Modal.confirm({
      title: '确认删除',
      content: '您确定要删除该触发器吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<RK_API.Trigger>[] = [
    {
      ...template,
      initialValue: queryParams.getAll('templateids'),
      hideInSearch: !formTemplate,
    },
    // {
    //   ...host,
    //   initialValue: queryParams.getAll('hostids'),
    //   hideInSearch: formTemplate,
    // },
    {
      title: '名称',
      width: 300,
      dataIndex: 'description',
      render(dom, entity) {
        const { dependencies = [] } = entity;

        return (
          <div>
            <a
              onClick={() =>
                history.push(`/monitor-config/host/trigger/details/${entity.triggerid}`)
              }
            >
              {dom}
            </a>
            {dependencies.length > 0 && (
              <div>
                依赖于：
                {dependencies.map((item: Record<string, any>) => (
                  <p key={item.triggerid} className="inner-p">
                    <a
                      onClick={() =>
                        history.push(
                          `/monitor-config/host/trigger/details/${entity.triggerid}/${item.triggerid}`,
                        )
                      }
                    >
                      {item.description}
                    </a>
                  </p>
                ))}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: '事件等级',
      width: 80,
      dataIndex: 'priority',
      valueType: 'checkbox',
      fieldProps: {
        options: SEVERITIES,
        mode: 'multiple',
      },
      hideInSearch: true,
      render(dom, entity) {
        const obj = SEVERITIES.find((item) => entity.priority === item.value);
        return <Tag color={obj?.tagColor}>{dom}</Tag>;
      },
    },

    // { ...hostGroups, initialValue: queryParams.getAll('groupids') },

    // {
    //   title: '操作数据',
    //   width: 300,
    //   dataIndex: 'opdata',
    //   hideInSearch: true,
    // },
    {
      title: '所属指标',
      dataIndex: 'item',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        fieldNames: {
          label: 'name',
          value: 'itemid',
        },
        showSearch: true,
        options: itemList,
        loading: itemLoading,
      },
      initialValue: queryParams.get('item'),
    },
    {
      title: '所属指标',
      width: 250,
      dataIndex: 'items',
      hideInSearch: true,
      render(_, record) {
        return record?.items?.map((item: RK_API.Item) => <div key={item.itemid}>{item.name}</div>);
      },
    },
    // {
    //   title: '表达式',
    //   width: 300,
    //   dataIndex: 'expression',
    //   hideInSearch: true,
    //   render(dom, entity) {
    //     const { recovery_expression = '' } = entity;
    //     const qText = recovery_expression && '问题：';
    //     const rText = recovery_expression && '恢复：';
    //     return (
    //       <>
    //         <p>
    //           <Typography.Text type="danger">{qText}</Typography.Text>
    //           {dom}
    //         </p>
    //         <p>
    //           <Typography.Text type="success">{rText}</Typography.Text>
    //           {recovery_expression}
    //         </p>
    //       </>
    //     );
    //   },
    // },

    {
      title: '标签',
      dataIndex: 'rk_tags',
      width: 200,

      // @ts-ignore
      valueType: 'tagsSelectSetter',
      fieldProps: {
        tags: triggerTags,
      },
      render(_, entity) {
        const { tags = [] } = entity;
        if (!tags.length) return <>-</>;
        return (tags as RK_API.TemplateTag[]).map((item, index) => (
          <Tag key={index}>
            {item?.tag}: {item?.value}
          </Tag>
        ));
      },
    },
    {
      dataIndex: 'comments',
      title: '描述',
      hideInSearch: true,
      width: 200,
    },
    {
      title: '状态',
      width: 100,
      dataIndex: 'status',
      valueEnum: option2enum(STATUS),
      initialValue: queryParams.get('status'),
      hideInTable: true,
    },
    {
      title: '状态',
      width: 100,
      dataIndex: 'status',
      ellipsis: true,
      hideInSearch: true,
      render(_, entity) {
        const checked = entity.status === '0';
        console.log(entity.status, checked);

        return (
          <Switch
            defaultChecked={checked}
            loading={updateStatusFetches?.[entity.triggerid!]?.loading}
            onChange={() => {
              updateStatus({
                triggerid: entity.triggerid,
                status: checked ? 1 : 0,
              });
            }}
          />
        );
      },
    },
    {
      title: '事件等级',
      dataIndex: 'priority',
      valueType: 'checkbox',
      colSize: 3,
      fieldProps: {
        options: SEVERITIES,
        mode: 'multiple',
      },
      hideInTable: true,
      initialValue: queryParams.getAll('priority'),
    },
    {
      title: '操作',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (text, record) => {
        return (
          <Space size={16}>
            <a
              key="edit"
              onClick={() =>
                history.push(`/monitor-config/host/trigger/${hostId}/edit/${record.triggerid}`)
              }
            >
              编辑
            </a>
            <a key="del" onClick={() => handleDelete([record])}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProConfigProvider valueTypeMap={valueTypeMap}>
        <ProTable<RK_API.Trigger>
          {...defaultTableConfig}
          search={SearchOptionRender}
          onSubmit={syncToUrl}
          rowKey="triggerid"
          formRef={formRef}
          actionRef={tableRef}
          columns={columns}
          headerTitle={host?.[hostId!] && `${host?.[hostId!]?.name}阈值设置`}
          // toolbar={{
          //   actions: [
          //     <Button
          //       key="add"
          //       type="primary"
          //       icon={<PlusOutlined />}
          //       onClick={() => {
          //         const id = formTemplate
          //           ? formRef.current?.getFieldValue('templateids').at(0)
          //           : formRef.current?.getFieldValue('hostids').at(0);
          //         if (id) {
          //           history.push(
          //             `/monitor-config/${
          //               formTemplate ? 'template' : 'host'
          //             }/trigger/add?hostid=${id}`,
          //           );
          //         } else {
          //           message.info('请先选择一个模版');
          //         }
          //       }}
          //     >
          //       新建触发器
          //     </Button>,
          //   ],
          // }}
          params={{
            itemid: (state as Record<string, any>)?.itemid,
          }}
          request={async (params) => {
            const {
              rk_tags,
              evaltype,
              templateids = [],
              groupids = [],
              description,
              ...rest
            } = params;

            return queryPagingTable(
              {
                evaltype,
                tags: rk_tags,
                templateids: templateids.length ? templateids : null,
                itemids: itemId ? [itemId] : null,
                groupids: groupids.length ? groupids : null,
                hostids: [hostId],

                sortfield: 'priority',

                search: {
                  description,
                },
                filter: rest,
                selectDependencies: 'extend',
                expandExpression: true,
                selectFunctions: 'extend',
                selectTags: 'extend',
                selectItems: 'extend',
                method: 'trigger.get',
              },
              zabbix,
            );
          }}
        />
      </ProConfigProvider>
    </PageContainer>
  );
});

export default Trigger;
