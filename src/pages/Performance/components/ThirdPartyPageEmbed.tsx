import { externalMonitoringPanelById } from '@/services/http/externalMonitoring';
import { FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { history, Link, useLocation, useParams, useRequest } from '@umijs/max';
import { Card, Tooltip } from 'antd';
import React from 'react';
import styles from './index.less';

import Iframe from 'react-iframe';

const isValidUrl = (url: string) => {
  try {
    new URL(url);
    return true;
  } catch (_) {
    return false;
  }
};

const ThirdPartyPageEmbed: React.FC = () => {
  const { id = '' } = useParams();
  // 判断是否为全屏页面
  const { pathname } = useLocation();
  const isFullScreen = pathname.includes('full');

  const { data: pageInfo } = useRequest(() =>
    externalMonitoringPanelById({
      id,
    }),
  );
  const url = pageInfo?.uri || '';

  // URL验证
  if (!isValidUrl(url)) {
    return <div>Invalid URL</div>;
  }

  return (
    <PageContainer
      title={false}
      childrenContentStyle={{
        paddingBlock: 0,
        paddingInline: 0,
      }}
      breadcrumb={{
        items: [
          {
            title: '监测信息',
            onClick: () => {
              history.push('/monitor');
            },
          },
          {
            onClick: () => {
              history.push('/monitor/performance');
            },
            title: '性能信息',
          },
          {
            title: pageInfo?.name,
          },
        ],
      }}
    >
      {isFullScreen ? (
        <Link to={`/monitor/performance/${id}`}>
          <Tooltip title="退出全屏">
            <FullscreenExitOutlined className={styles.icon} />
          </Tooltip>
        </Link>
      ) : (
        <Link to={`/monitor/performance/full/${id}`}>
          <Tooltip title="全屏">
            <FullscreenOutlined className={styles.icon} />
          </Tooltip>
        </Link>
      )}
      <Card
        bodyStyle={{
          height: isFullScreen ? '100vh' : 'calc(100vh - 150px)',
          padding: 0,
        }}
        style={{}}
        bordered={false}
        type="inner"
      >
        <Iframe
          url={url}
          width="100%"
          height="100%"
          id="third-party-iframe"
          display="initial"
          allowFullScreen
          allow="fullscreen"
          sandbox={['allow-same-origin', 'allow-scripts']}
          styles={{
            border: 'none',
          }}
          scrolling="no"
          onLoad={() => {
            document
              .getElementById('third-party-iframe')
              // @ts-ignore
              ?.contentWindow?.postMessage({ action: 'disableKeyboard' }, '*');
          }}
        />
      </Card>
    </PageContainer>
  );
};

export default ThirdPartyPageEmbed;
