import RKCol from '@/components/RKCol';
import BaseContext from '@/Context/BaseContext';
import { EVAL_TYPE } from '@/enums';
import { useHostGroupList } from '@/hooks/useHostGroupList';
import { useHostList } from '@/hooks/useHostList';
import { useTemplateList } from '@/hooks/useTemplateList';
import { useTriggerList } from '@/hooks/useTriggerList';
import { useUserGroupList } from '@/hooks/useUserGroupList';
import { useUserList } from '@/hooks/useUserList';
import { zabbix, zabbixPost } from '@/services/zabbix';
import { onSuccessAndGoBack, queryFormData } from '@/utils';
import { requiredRule } from '@/utils/setting';
import { produce } from 'immer';

import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import { FormProps, Row } from 'antd';
import React, { useRef } from 'react';
import ConditionSetter from './ConditionSetter';
import Operation from './Operation';
import RecoveryOperationSetter from './RecoveryOperationSetter';

const TriggerActionDetails: React.FC = () => {
  // 用户
  const { userList, loading: userLoading } = useUserList();
  // 用户组
  const { userGroupList, loading: userGroupLoading } = useUserGroupList();
  // 对象群组
  const { hostGroupList, loading: hostGroupLoading } = useHostGroupList();
  // 主机列表
  const { hostList, loading: hostLoading } = useHostList();
  // 触发器列表
  const { triggerList, loading: triggerLoading } = useTriggerList();
  // 模版
  const { templateList, loading: templateLoading } = useTemplateList();
  const formRef = useRef<ProFormInstance>();

  // 判断是否为编辑页面
  const { id } = useParams();
  const isEditPage = !!id;

  const onValuesChange: FormProps['onValuesChange'] = ({ filter }, values) => {
    if (!filter) return;
    formRef.current?.setFieldValue(['filter', 'eval_formula'], '');
    formRef.current?.setFieldValue(['filter', 'formula'], '');
    // 只有evaltype及conditions变化才做一下操作
    const keyExists = 'evaltype' in filter || 'conditions' in filter;
    if (!keyExists) return;
    const conditionsValue = values?.filter?.conditions || [];
    const evaltypeValue = values?.filter?.evaltype;
    if (conditionsValue.length < 1) return;
    // 将数据根据conditiontype进行分组
    const categorizedConditions: string[][] = conditionsValue.reduce(
      (acc: Record<string, any>, condition: Record<string, any>) => {
        const { conditiontype, formulaid } = condition;
        if (!acc[conditiontype]) {
          acc[conditiontype] = [formulaid];
        } else {
          acc[conditiontype].push(formulaid);
        }
        return acc;
      },
      {},
    );
    //  生成表达式
    switch (evaltypeValue) {
      case '0':
        {
          const expression = Object.values(categorizedConditions)
            .map((item: string[]) => {
              if (item.length > 1) {
                return `(${item.join(' or ')})`;
              } else {
                return item[0];
              }
            })
            .join(' and ');
          formRef.current?.setFieldValue(['filter', 'eval_formula'], expression);
        }
        break;
      case '1':
        {
          const expression = Object.values(categorizedConditions)
            .map((item: string[]) => {
              if (item.length > 1) {
                return `(${item.join(' and ')})`;
              } else {
                return item[0];
              }
            })
            .join(' and ');
          formRef.current?.setFieldValue(['filter', 'eval_formula'], expression);
        }
        break;
      case '2':
        {
          const expression = Object.values(categorizedConditions)
            .map((item: string[]) => {
              if (item.length > 1) {
                return `(${item.join(' or ')})`;
              } else {
                return item[0];
              }
            })
            .join(' or ');
          formRef.current?.setFieldValue(['filter', 'eval_formula'], expression);
        }
        break;
      default:
        break;
    }
  };
  // 新建
  const { run: add, loading: addLoading } = useRequest(
    (value) => zabbixPost({ ...value, method: 'action.create' }),
    {
      manual: true,
      onSuccess: onSuccessAndGoBack,
      formatResult: (res) => res,
    },
  );
  // 修改
  const { run: update, loading: updateLoading } = useRequest(
    (value) => zabbixPost({ ...value, method: 'action.update' }),
    {
      manual: true,
      onSuccess: onSuccessAndGoBack,
      formatResult: (res) => res,
    },
  );

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <BaseContext.Provider
        value={{
          userList,
          userLoading,
          userGroupList,
          userGroupLoading,
          hostList,
          hostLoading,
          hostGroupList,
          hostGroupLoading,
          triggerList,
          triggerLoading,
          templateList,
          templateLoading,
        }}
      >
        <ProForm<RK_API.Action>
          formRef={formRef}
          initialValues={{
            status: '0',
            eventsource: 0,
            esc_period: '1h',
            filter: {
              evaltype: '0',
              // conditions: [
              //   {
              //     formulaid: 'B',
              //     conditiontype: 3,
              //     operator: 2,
              //     value: 'ee',
              //   },
              //   {
              //     formulaid: 'Z',
              //     conditiontype: 3,
              //     operator: 2,
              //     value: 'cc',
              //   },
              // ],
            },
            pause_suppressed: 1,
            notify_if_canceled: 1,
          }}
          submitter={{
            searchConfig: {
              submitText: '保存',
              resetText: '取消',
            },
            onReset: () => {
              history.go(-1);
            },

            render: (props, doms) => {
              return <FooterToolbar>{doms}</FooterToolbar>;
            },
            submitButtonProps: {
              loading: addLoading || updateLoading,
            },
          }}
          onFinish={async (values) => {
            const updatedValue = produce(values, (draft: any) => {
              const { filter, operations, recovery_operations, update_operations, ...restProps } =
                draft;
              return {
                ...restProps,
                filter: {
                  evaltype: filter?.evaltype,
                  conditions: filter?.conditions.map((item: RK_API.Condition) => {
                    return {
                      ...item,
                      formulaid: undefined,
                      value2: item?.value2 || undefined,
                      value: item?.value || undefined,
                    };
                  }),
                },
                operations: operations?.map((item: RK_API.Operation) => {
                  return {
                    ...item,
                    opmessage: {
                      ...item?.opmessage,
                      subject:
                        item.opmessage.default_msg === '1' ? undefined : item.opmessage.subject,
                      message:
                        item.opmessage.default_msg === '1' ? undefined : item.opmessage.message,
                    },
                  };
                }),
                recovery_operations: recovery_operations?.map((item: RK_API.Operation) => {
                  return {
                    ...item,
                    opmessage: {
                      ...item?.opmessage,
                      subject:
                        item.opmessage.default_msg === '1' ? undefined : item.opmessage.subject,
                      message:
                        item.opmessage.default_msg === '1' ? undefined : item.opmessage.message,
                    },
                  };
                }),
                update_operations: update_operations?.map((item: RK_API.Operation) => {
                  return {
                    ...item,
                    opmessage: {
                      ...item?.opmessage,
                      subject:
                        item.opmessage.default_msg === '1' ? undefined : item.opmessage.subject,
                      message:
                        item.opmessage.default_msg === '1' ? undefined : item.opmessage.message,
                    },
                  };
                }),
              };
            });

            if (isEditPage) {
              update(updatedValue);
            } else {
              add(updatedValue);
            }
          }}
          onValuesChange={onValuesChange}
          request={() =>
            queryFormData(
              {
                actionids: [id],
                selectOperations: 'extend',
                selectRecoveryOperations: 'extend',
                selectUpdateOperations: 'extend',
                selectFilter: 'extend',
                method: 'action.get',
              },
              isEditPage,
              zabbix,
            )
          }
        >
          {/* 不需要展示，只是为了form传值 */}
          <div className="rk-none">
            <ProFormText name="actionid" />
            <ProFormText name="eventsource" />
          </div>
          <Row gutter={24}>
            <RKCol>
              <ProFormText label="名称" name="name" rules={[requiredRule]} />
            </RKCol>

            <RKCol>
              <ProFormSelect
                name={['filter', 'evaltype']}
                label="计算方法"
                options={EVAL_TYPE}
                rules={[requiredRule]}
                fieldProps={{
                  allowClear: false,
                }}
              />
            </RKCol>
            <ProFormDependency
              name={[
                ['filter', 'evaltype'],
                ['filter', 'conditions'],
              ]}
            >
              {({ filter }) => {
                const length = filter?.conditions?.length || 0;
                if (length < 2) return;
                const custom = filter.evaltype === '3';
                return (
                  <RKCol>
                    <ProFormTextArea
                      label="表达式"
                      name={custom ? ['filter', 'formula'] : ['filter', 'eval_formula']}
                      fieldProps={{
                        autoSize: { minRows: 1, maxRows: 3 },
                      }}
                      placeholder={custom ? 'A or (B and C)' : ''}
                      disabled={!custom}
                      rules={custom ? [requiredRule] : []}
                    />
                  </RKCol>
                );
              }}
            </ProFormDependency>
            <RKCol>
              <ProFormSwitch
                label="启用"
                name="status"
                getValueFromEvent={(val) => (val ? '0' : '1')}
                getValueProps={(value) => ({ checked: value === '0' })}
              />
            </RKCol>
          </Row>
          {/* 条件 */}
          <ProForm.Item
            name={['filter', 'conditions']}
            rules={[requiredRule]}
            getValueProps={(val: RK_API.Condition[]) => ({
              value: val?.sort((a, b) => {
                if (a?.formulaid[0] !== b.formulaid[0]) {
                  return a.formulaid.localeCompare(b.formulaid);
                } else {
                  return a.formulaid.localeCompare(b.formulaid, undefined, { sensitivity: 'base' });
                }
              }),
            })}
          >
            <ConditionSetter />
          </ProForm.Item>
          {/* 操作配置 */}
          <Operation />
          {/* 恢复操作 */}
          <ProForm.Item
            name={'recovery_operations'}
            rules={[requiredRule]}
            transform={(value: RK_API.Operation[], namePath) => ({
              [namePath]: value.map((item) => ({
                ...item,
                operationid: undefined,
                actionid: undefined,
                opconditions: undefined,
                evaltype: undefined,
                opcommand_grp: item?.opcommand_grp?.map(({ groupid }) => ({ groupid })),
                opmessage_usr: item?.opmessage_usr?.map(({ userid }) => ({ userid })),
                opcommand_hst: item?.opcommand_hst?.map(({ hostid }) => ({ hostid })),
                opmessage_grp: item?.opmessage_grp?.map(({ usrgrpid }) => ({ usrgrpid })),
              })),
            })}
          >
            <RecoveryOperationSetter />
          </ProForm.Item>
          {/* 更新操作 */}
          <ProForm.Item
            name={'update_operations'}
            rules={[requiredRule]}
            transform={(value: RK_API.Operation[], namePath) => ({
              [namePath]: value.map((item) => ({
                ...item,
                operationid: undefined,
                actionid: undefined,
                opconditions: undefined,
                evaltype: undefined,
                opcommand_grp: item?.opcommand_grp?.map(({ groupid }) => ({ groupid })),
                opmessage_usr: item?.opmessage_usr?.map(({ userid }) => ({ userid })),
                opcommand_hst: item?.opcommand_hst?.map(({ hostid }) => ({ hostid })),
                opmessage_grp: item?.opmessage_grp?.map(({ usrgrpid }) => ({ usrgrpid })),
              })),
            })}
          >
            <RecoveryOperationSetter type="update" />
          </ProForm.Item>
        </ProForm>
      </BaseContext.Provider>
    </PageContainer>
  );
};

export default TriggerActionDetails;
