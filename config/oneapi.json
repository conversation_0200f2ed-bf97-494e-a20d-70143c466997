{"openapi": "3.0.1", "info": {"title": "Rklink Union Monitor Platform Swagger Docs", "description": "Rklink Union Monitor Platform Swagger Docs", "version": "0.0.1-SNAPSHOT"}, "servers": [{"url": "http://rump.rkzl.com/rump", "description": "Generated server url"}], "tags": [{"name": "count-view", "description": "统计视图"}, {"name": "atomicTask", "description": "原子任务"}, {"name": "host", "description": "主机"}, {"name": "api", "description": "公共api"}, {"name": "userGroup", "description": "用户组"}, {"name": "user", "description": "用户"}, {"name": "template", "description": "监控模板"}], "paths": {"/v1/host/{id}/update": {"put": {"tags": ["host"], "operationId": "hostUpdateById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HostCURequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK"}}}}, "/v1/user/update": {"post": {"tags": ["user"], "summary": "修改", "operationId": "userUpdate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserIdsResponse"}}}}}}}, "/v1/user/unblock": {"post": {"tags": ["user"], "summary": "解锁用户", "operationId": "unblock", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchIdRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserIdsResponse"}}}}}}}, "/v1/user/logout": {"post": {"tags": ["user"], "summary": "注销", "operationId": "userLogout", "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}}}}, "/v1/user/login": {"post": {"tags": ["user"], "summary": "登录", "operationId": "userLogin", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/v1/user/group/update": {"post": {"tags": ["userGroup"], "summary": "修改", "operationId": "userGroupUpdate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGroupUpdateRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserGroupIdDTO"}}}}}}}, "/v1/user/group/get": {"post": {"tags": ["userGroup"], "summary": "查询", "operationId": "userGroupGet", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGroupGetReqeust"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListUserGroupDTO"}}}}}}}, "/v1/user/group/delete": {"post": {"tags": ["userGroup"], "summary": "删除", "operationId": "userGroupDelete", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchIdRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserGroupIdDTO"}}}}}}}, "/v1/user/group/create": {"post": {"tags": ["userGroup"], "summary": "创建", "operationId": "userGroupCreate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGroupCreateRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserGroupIdDTO"}}}}}}}, "/v1/user/group/count": {"post": {"tags": ["userGroup"], "summary": "统计", "operationId": "userGroupCount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGroupGetReqeust"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultInteger"}}}}}}}, "/v1/user/get": {"post": {"tags": ["user"], "summary": "查询", "operationId": "userGet", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInfoRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListUserInfoResponse"}}}}}}}, "/v1/user/delete": {"post": {"tags": ["user"], "summary": "删除", "operationId": "userDelete", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchIdRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserIdsResponse"}}}}}}}, "/v1/user/create": {"post": {"tags": ["user"], "summary": "创建", "operationId": "userCreate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreateRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserIdsResponse"}}}}}}}, "/v1/user/count": {"post": {"tags": ["user"], "summary": "统计", "operationId": "userCount", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInfoRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultString"}}}}}}}, "/v1/user/checkAuthentication": {"post": {"tags": ["user"], "summary": "检查认证", "operationId": "checkAuthentication", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAuthenticationRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUserAuthenticationResponse"}}}}}}}, "/v1/template/update": {"post": {"tags": ["template"], "summary": "修改", "operationId": "update", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateUpdateCommand"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultTemplateIdDTO"}}}}}}}, "/v1/template/massre/update": {"post": {"tags": ["template"], "summary": "批量修改", "operationId": "massreUpdate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateMassreUpdateCommand"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultTemplateIdDTO"}}}}}}}, "/v1/template/massre/move": {"post": {"tags": ["template"], "summary": "批量删除", "operationId": "massreMove", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateMassreMoveCommand"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultTemplateIdDTO"}}}}}}}, "/v1/template/massre/add": {"post": {"tags": ["template"], "summary": "批量新增", "operationId": "massreAdd", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateMassreAddCommand"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultTemplateIdDTO"}}}}}}}, "/v1/template/get": {"post": {"tags": ["template"], "summary": "查询", "operationId": "get", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateGetCommand"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListTemplateGetDTO"}}}}}}}, "/v1/template/delete": {"post": {"tags": ["template"], "summary": "删除", "operationId": "delete", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchIdRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultTemplateIdDTO"}}}}}}}, "/v1/template/create": {"post": {"tags": ["template"], "summary": "创建", "operationId": "create", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplateCreateCommand"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultTemplateIdDTO"}}}}}}}, "/v1/host/testConnection": {"post": {"tags": ["host"], "operationId": "hostTestConnection", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HostTestConnectionRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK"}}}}, "/v1/host/page": {"post": {"tags": ["host"], "operationId": "hostPage", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HostPageRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultPageVOHostPageVO"}}}}}}}, "/v1/host/delete": {"post": {"tags": ["host"], "operationId": "hostDeleteByIds", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchIdRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK"}}}}, "/v1/host/create": {"post": {"tags": ["host"], "operationId": "hostCreate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HostCURequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK"}}}}, "/v1/atomic/task/path": {"post": {"tags": ["atomicTask"], "operationId": "getAtomicTaskPath", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AtomicTaskPathRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultAtomicTaskPathVO"}}}}}}}, "/api/v1/rump/api/common": {"post": {"tags": ["api"], "summary": "api", "operationId": "api", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonRequest"}}}, "required": true}, "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultJsonNode"}}}}}}}, "/v1/statistics/view/statsView": {"get": {"tags": ["count-view"], "operationId": "statsView", "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultStatisticsVO"}}}}}}}, "/v1/host/byId/{id}": {"get": {"tags": ["host"], "operationId": "hostFindById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "503": {"description": "Service Unavailable", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultError"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultHostVO"}}}}}}}}, "components": {"schemas": {"Error": {"type": "object", "properties": {"code": {"type": "string", "description": "错误代码"}, "message": {"type": "string", "description": "一个简短的错误摘要"}, "data": {"type": "string", "description": "更详细的错误消息"}}, "description": "错误信息"}, "ResultError": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "error": {"$ref": "#/components/schemas/Error"}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}, "HostCURequest": {"type": "object", "properties": {"type": {"type": "string"}, "host": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "username": {"type": "string"}, "password": {"type": "string"}, "description": {"type": "string"}}}, "Media": {"type": "object", "properties": {"mediatypeid": {"type": "string", "description": "被使用的媒介类型ID."}, "sendto": {"type": "array", "description": "地址, 用户名或者其他接收标识符.\n如果 媒介类型 是邮件, 值被定义为数组. 如果 媒介类型 是其他类型, 值被定义为字符串.\n", "items": {"type": "string", "description": "地址, 用户名或者其他接收标识符.\n如果 媒介类型 是邮件, 值被定义为数组. 如果 媒介类型 是其他类型, 值被定义为字符串.\n"}}, "active": {"type": "integer", "description": "是否启用媒介.\n可用值:\n0 - (default) 启用;\n1 - 禁用.\n", "format": "int32"}, "severity": {"type": "integer", "description": "触发媒介发送告警的告警级别.\n每一位数字代表一个告警级别，并以二进制形式存储. 例如, 12 相当于二进制的 1100, 它表示告警级别为警告和一般严重的告警将触发告警媒介.\n参阅 触发器对象 查看告警级别列表.\n默认: 63\n", "format": "int32"}, "period": {"type": "string", "description": "时间窗口: 能够发送告警通知的 时间段 或者以分号分隔的用户宏.\n默认: 1-7,00:00-24:00\n"}}, "description": "要创建的用户 media"}, "UserUpdateRequest": {"type": "object", "properties": {"userid": {"type": "string", "description": "用户id"}, "username": {"type": "string", "description": "用户名称"}, "autologin": {"type": "integer", "description": "是否允许自动登录.\n可用值:\n0 - (default) 禁止自动登录;\n1 - 允许自动登录.\n", "format": "int32"}, "autologout": {"type": "string", "description": "会话过期时长. 接受具有后缀的秒或时间单位. 如果设置为 0s, 会话将永不过期.\n默认: 15m.\n"}, "lang": {"type": "string", "description": "用户语言代码, 示例, en_GB.\n默认: default - 系统默认语言.\n"}, "refresh": {"type": "string", "description": "自动刷新间隔. 接受具有后缀的秒或时间单位.\n默认: 30s.\n"}, "rowsPerPage": {"type": "integer", "description": "每页显示的对象条目.\n默认: 50.\n", "format": "int32"}, "surname": {"type": "string", "description": "姓."}, "theme": {"type": "string", "description": "用户的主题.\n可用值:\ndefault - (default) 系统默认主题;\nblue-theme - 蓝主题;\ndark-theme - 黑主题.\n"}, "url": {"type": "string", "description": "用户登录后重定向页面的URL.\n"}, "timezone": {"type": "string", "description": "用户时区, 示例, Europe/London, UTC.\n默认: default - 系统默认时区.\n"}, "roleid": {"type": "string", "description": "用户的角色ID."}, "passwd": {"type": "string", "description": "用户的密码。\n如果用户仅添加到具有 LDAP 访问权限的组，则可以省略。\n"}, "usrgrps": {"type": "array", "description": "要将用户添加到的用户 组。\n用户组必须具有已定义的 usrgrpid 属性。\n", "items": {"$ref": "#/components/schemas/Usrgrp"}}, "medias": {"type": "array", "description": "要创建的用户 media", "items": {"$ref": "#/components/schemas/Media"}}}}, "Usrgrp": {"type": "object", "properties": {"usrgrpid": {"type": "string"}}, "description": "要将用户添加到的用户 组。\n用户组必须具有已定义的 usrgrpid 属性。\n"}, "ResultUserIdsResponse": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "result": {"$ref": "#/components/schemas/UserIdsResponse"}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}, "UserIdsResponse": {"type": "object", "properties": {"userids": {"type": "array", "items": {"type": "string"}}}, "description": "方法返回的数据"}, "BatchIdRequest": {"type": "object", "properties": {"ids": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}}, "ResultBoolean": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "result": {"type": "boolean", "description": "方法返回的数据"}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}, "UserLoginRequest": {"type": "object", "properties": {"username": {"type": "string", "description": "用户名"}, "password": {"type": "string", "description": "密码"}}, "description": "登录请求对象"}, "ResultString": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "result": {"type": "string", "description": "方法返回的数据"}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}, "Auth": {"type": "object", "properties": {"id": {"type": "string"}, "permission": {"type": "integer", "format": "int32"}}}, "TagAuth": {"type": "object", "properties": {"groupid": {"type": "string"}, "tag": {"type": "string"}, "value": {"type": "string"}}}, "User": {"type": "object", "properties": {"userid": {"type": "string"}}}, "UserGroupUpdateRequest": {"type": "object", "properties": {"usrgrpid": {"type": "string"}, "name": {"type": "string"}, "debug_mode": {"type": "integer", "format": "int32"}, "gui_access": {"type": "integer", "format": "int32"}, "users_status": {"type": "integer", "format": "int32"}, "rights": {"type": "array", "items": {"$ref": "#/components/schemas/Auth"}}, "tag_filters": {"type": "array", "items": {"$ref": "#/components/schemas/TagAuth"}}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}, "ResultUserGroupIdDTO": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "result": {"$ref": "#/components/schemas/UserGroupIdDTO"}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}, "UserGroupIdDTO": {"type": "object", "properties": {"usrgrpids": {"type": "array", "items": {"type": "string"}}}, "description": "方法返回的数据"}, "UserGroupGetReqeust": {"type": "object", "properties": {"countOutput": {"type": "boolean"}, "editable": {"type": "boolean"}, "excludeSearch": {"type": "boolean"}, "filter": {"type": "object"}, "limit": {"type": "integer", "format": "int32"}, "output": {"type": "string"}, "preservekeys": {"type": "boolean"}, "search": {"type": "object"}, "searchByAny": {"type": "boolean"}, "searchWildcardsEnabled": {"type": "boolean"}, "sortorder": {"type": "string"}, "startSearch": {"type": "boolean"}, "status": {"type": "integer", "format": "int32"}, "userids": {"type": "array", "items": {"type": "string"}}, "usrgrpids": {"type": "array", "items": {"type": "string"}}, "selectTagFilters": {"type": "string"}, "selectUsers": {"type": "string"}, "selectRights": {"type": "string"}, "limitSelects": {"type": "integer", "format": "int32"}, "sortfield": {"type": "array", "items": {"type": "string"}}}}, "MediasItem": {"type": "object", "properties": {"severity": {"type": "string"}, "period": {"type": "string"}, "sendto": {"type": "array", "items": {"type": "string"}}, "mediatypeid": {"type": "string"}, "active": {"type": "string"}, "mediaid": {"type": "string"}, "userid": {"type": "string"}}}, "MediatypesItem": {"type": "object", "properties": {"smtp_authentication": {"type": "string"}, "mediatypeid": {"type": "string"}, "maxsessions": {"type": "string"}, "smtp_verify_host": {"type": "string"}, "description": {"type": "string"}, "maxattempts": {"type": "string"}, "type": {"type": "string"}, "timeout": {"type": "string"}, "exec_path": {"type": "string"}, "content_type": {"type": "string"}, "event_menu_url": {"type": "string"}, "exec_params": {"type": "string"}, "gsm_modem": {"type": "string"}, "script": {"type": "string"}, "smtp_security": {"type": "string"}, "smtp_verify_peer": {"type": "string"}, "smtp_email": {"type": "string"}, "smtp_helo": {"type": "string"}, "show_event_menu": {"type": "string"}, "smtp_port": {"type": "string"}, "smtp_server": {"type": "string"}, "passwd": {"type": "string"}, "attempt_interval": {"type": "string"}, "event_menu_name": {"type": "string"}, "name": {"type": "string"}, "process_tags": {"type": "string"}, "parameters": {"type": "array", "items": {"type": "object"}}, "username": {"type": "string"}, "status": {"type": "string"}}}, "ResultListUserGroupDTO": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "result": {"type": "array", "description": "方法返回的数据", "items": {"$ref": "#/components/schemas/UserGroupDTO"}}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}, "Role": {"type": "object", "properties": {"readonly": {"type": "string"}, "roleid": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}}}, "UserGroupDTO": {"type": "object", "properties": {"usrgrpid": {"type": "string"}, "name": {"type": "string"}, "debug_mode": {"type": "integer", "format": "int32"}, "gui_access": {"type": "integer", "format": "int32"}, "users_status": {"type": "integer", "format": "int32"}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/UserInfoDTO"}}, "rights": {"type": "array", "items": {"$ref": "#/components/schemas/Auth"}}}, "description": "方法返回的数据"}, "UserInfoDTO": {"type": "object", "properties": {"autologin": {"type": "string"}, "role": {"$ref": "#/components/schemas/Role"}, "timezone": {"type": "string"}, "users_status": {"type": "string"}, "roleid": {"type": "string"}, "attempt_clock": {"type": "string"}, "refresh": {"type": "string"}, "attempt_failed": {"type": "string"}, "userid": {"type": "string"}, "gui_access": {"type": "string"}, "debug_mode": {"type": "string"}, "attempt_ip": {"type": "string"}, "url": {"type": "string"}, "usrgrps": {"type": "array", "items": {"$ref": "#/components/schemas/UsrgrpsItem"}}, "medias": {"type": "array", "items": {"$ref": "#/components/schemas/MediasItem"}}, "rows_per_page": {"type": "string"}, "surname": {"type": "string"}, "autologout": {"type": "string"}, "name": {"type": "string"}, "theme": {"type": "string"}, "lang": {"type": "string"}, "username": {"type": "string"}, "mediatypes": {"type": "array", "items": {"$ref": "#/components/schemas/MediatypesItem"}}}}, "UsrgrpsItem": {"type": "object", "properties": {"debug_mode": {"type": "string"}, "usrgrpid": {"type": "string"}, "userdirectoryid": {"type": "string"}, "name": {"type": "string"}, "users_status": {"type": "string"}, "gui_access": {"type": "string"}}}, "UserGroupCreateRequest": {"type": "object", "properties": {"usrgrpid": {"type": "string"}, "name": {"type": "string"}, "debug_mode": {"type": "integer", "format": "int32"}, "gui_access": {"type": "integer", "format": "int32"}, "users_status": {"type": "integer", "format": "int32"}, "rights": {"type": "array", "items": {"$ref": "#/components/schemas/Auth"}}, "tag_filters": {"type": "array", "items": {"$ref": "#/components/schemas/TagAuth"}}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}, "ResultInteger": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "result": {"type": "integer", "description": "方法返回的数据", "format": "int32"}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}, "UserInfoRequest": {"type": "object", "properties": {"countOutput": {"type": "boolean"}, "editable": {"type": "boolean"}, "excludeSearch": {"type": "boolean"}, "filter": {"type": "object"}, "limit": {"type": "integer", "format": "int32"}, "output": {"type": "string"}, "preservekeys": {"type": "boolean"}, "search": {"type": "object"}, "searchByAny": {"type": "boolean"}, "searchWildcardsEnabled": {"type": "boolean"}, "sortorder": {"type": "string"}, "startSearch": {"type": "boolean"}, "mediaids": {"type": "array", "items": {"type": "string"}}, "mediatypeids": {"type": "array", "items": {"type": "string"}}, "userids": {"type": "array", "items": {"type": "string"}}, "usrgrpids": {"type": "array", "items": {"type": "string"}}, "getAccess": {"type": "boolean"}, "selectMedias": {"type": "string"}, "selectMediatypes": {"type": "string"}, "selectUsrgrps": {"type": "string"}, "selectRole": {"type": "string"}, "sortfield": {"type": "array", "items": {"type": "string"}}}}, "ResultListUserInfoResponse": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "result": {"type": "array", "description": "方法返回的数据", "items": {"$ref": "#/components/schemas/UserInfoResponse"}}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}, "UserInfoResponse": {"type": "object", "properties": {"autologin": {"type": "string"}, "role": {"$ref": "#/components/schemas/Role"}, "timezone": {"type": "string"}, "users_status": {"type": "string"}, "roleid": {"type": "string"}, "attempt_clock": {"type": "string"}, "refresh": {"type": "string"}, "attempt_failed": {"type": "string"}, "userid": {"type": "string"}, "gui_access": {"type": "string"}, "debug_mode": {"type": "string"}, "attempt_ip": {"type": "string"}, "url": {"type": "string"}, "usrgrps": {"type": "array", "items": {"$ref": "#/components/schemas/UsrgrpsItem"}}, "medias": {"type": "array", "items": {"$ref": "#/components/schemas/MediasItem"}}, "rows_per_page": {"type": "string"}, "surname": {"type": "string"}, "autologout": {"type": "string"}, "name": {"type": "string"}, "theme": {"type": "string"}, "lang": {"type": "string"}, "username": {"type": "string"}, "mediatypes": {"type": "array", "items": {"$ref": "#/components/schemas/MediatypesItem"}}}, "description": "方法返回的数据"}, "UserCreateRequest": {"type": "object", "properties": {"username": {"type": "string", "description": "用户名称"}, "autologin": {"type": "integer", "description": "是否允许自动登录.\n可用值:\n0 - (default) 禁止自动登录;\n1 - 允许自动登录.\n", "format": "int32"}, "autologout": {"type": "string", "description": "会话过期时长. 接受具有后缀的秒或时间单位. 如果设置为 0s, 会话将永不过期.\n默认: 15m.\n"}, "lang": {"type": "string", "description": "用户语言代码, 示例, en_GB.\n默认: default - 系统默认语言.\n"}, "refresh": {"type": "string", "description": "自动刷新间隔. 接受具有后缀的秒或时间单位.\n默认: 30s.\n"}, "rowsPerPage": {"type": "integer", "description": "每页显示的对象条目.\n默认: 50.\n", "format": "int32"}, "surname": {"type": "string", "description": "姓."}, "theme": {"type": "string", "description": "用户的主题.\n可用值:\ndefault - (default) 系统默认主题;\nblue-theme - 蓝主题;\ndark-theme - 黑主题.\n"}, "url": {"type": "string", "description": "用户登录后重定向页面的URL.\n"}, "timezone": {"type": "string", "description": "用户时区, 示例, Europe/London, UTC.\n默认: default - 系统默认时区.\n"}, "roleid": {"type": "string", "description": "用户的角色ID."}, "passwd": {"type": "string", "description": "用户的密码。\n如果用户仅添加到具有 LDAP 访问权限的组，则可以省略。\n"}, "usrgrps": {"type": "array", "description": "要将用户添加到的用户 组。\n用户组必须具有已定义的 usrgrpid 属性。\n", "items": {"$ref": "#/components/schemas/Usrgrp"}}, "medias": {"type": "array", "description": "要创建的用户 media", "items": {"$ref": "#/components/schemas/Media"}}}}, "UserAuthenticationRequest": {"type": "object", "properties": {"extend": {"type": "boolean"}, "sessionid": {"type": "string"}}}, "ResultUserAuthenticationResponse": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "result": {"$ref": "#/components/schemas/UserAuthenticationResponse"}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}, "UserAuthenticationResponse": {"type": "object", "properties": {"autologin": {"type": "string"}, "debug_mode": {"type": "integer", "format": "int32"}, "timezone": {"type": "string"}, "roleid": {"type": "string"}, "userdirectoryid": {"type": "string"}, "attempt_clock": {"type": "string"}, "refresh": {"type": "string"}, "attempt_failed": {"type": "string"}, "sessionid": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "userid": {"type": "string"}, "attempt_ip": {"type": "string"}, "url": {"type": "string"}, "gui_access": {"type": "integer", "format": "int32"}, "rows_per_page": {"type": "string"}, "surname": {"type": "string"}, "autologout": {"type": "string"}, "name": {"type": "string"}, "userip": {"type": "string"}, "theme": {"type": "string"}, "lang": {"type": "string"}, "username": {"type": "string"}}, "description": "方法返回的数据"}, "HostGroup": {"type": "object", "properties": {"groupid": {"type": "string"}, "name": {"type": "string"}, "flags": {"type": "integer", "format": "int32"}, "internal": {"type": "integer", "format": "int32"}, "uuid": {"type": "string"}}}, "HostMacro": {"type": "object", "properties": {"hostmacroid": {"type": "string"}, "hostid": {"type": "string"}, "macro": {"type": "string"}, "value": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "description": {"type": "string"}}}, "Template": {"type": "object", "properties": {"templateid": {"type": "string"}, "host": {"type": "string"}, "description": {"type": "string"}, "name": {"type": "string"}, "uuid": {"type": "string"}}}, "TemplateTag": {"type": "object", "properties": {"tag": {"type": "string"}, "value": {"type": "string"}}}, "TemplateUpdateCommand": {"type": "object", "properties": {"templateid": {"type": "string"}, "host": {"type": "string"}, "description": {"type": "string"}, "name": {"type": "string"}, "uuid": {"type": "string"}, "groups": {"type": "array", "items": {"$ref": "#/components/schemas/HostGroup"}}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/TemplateTag"}}, "macros": {"type": "array", "items": {"$ref": "#/components/schemas/HostMacro"}}, "templates": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}, "templates_clear": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}}}, "ResultTemplateIdDTO": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "result": {"$ref": "#/components/schemas/TemplateIdDTO"}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}, "TemplateIdDTO": {"type": "object", "properties": {"templateids": {"type": "array", "items": {"type": "string"}}}, "description": "方法返回的数据"}, "TemplateMassreUpdateCommand": {"type": "object", "properties": {"templates": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}, "groups": {"type": "array", "items": {"$ref": "#/components/schemas/HostGroup"}}, "macros": {"type": "array", "items": {"$ref": "#/components/schemas/HostMacro"}}, "templates_clear": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}, "templates_link": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}}}, "TemplateMassreMoveCommand": {"type": "object", "properties": {"templateids": {"type": "array", "items": {"type": "string"}}, "groupids": {"type": "array", "items": {"type": "string"}}, "macros": {"type": "array", "items": {"type": "string"}}, "templateids_clear": {"type": "array", "items": {"type": "string"}}, "templateids_link": {"type": "array", "items": {"type": "string"}}}}, "TemplateMassreAddCommand": {"type": "object", "properties": {"templates": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}, "groups": {"type": "array", "items": {"$ref": "#/components/schemas/HostGroup"}}, "macros": {"type": "array", "items": {"$ref": "#/components/schemas/HostMacro"}}, "templates_link": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}}}, "TemplateGetCommand": {"type": "object", "properties": {"countOutput": {"type": "boolean"}, "editable": {"type": "boolean"}, "excludeSearch": {"type": "boolean"}, "filter": {"type": "object"}, "limit": {"type": "integer", "format": "int32"}, "output": {"type": "string"}, "preservekeys": {"type": "boolean"}, "search": {"type": "object"}, "searchByAny": {"type": "boolean"}, "searchWildcardsEnabled": {"type": "boolean"}, "sortorder": {"type": "string"}, "startSearch": {"type": "boolean"}, "templateids": {"type": "array", "items": {"type": "string"}}, "groupids": {"type": "array", "items": {"type": "string"}}, "parentTemplateids": {"type": "array", "items": {"type": "string"}}, "hostids": {"type": "array", "items": {"type": "string"}}, "graphids": {"type": "array", "items": {"type": "string"}}, "itemids": {"type": "array", "items": {"type": "string"}}, "triggerids": {"type": "array", "items": {"type": "string"}}, "with_items": {"type": "boolean"}, "with_triggers": {"type": "boolean"}, "with_graphs": {"type": "boolean"}, "with_httptests": {"type": "boolean"}, "evaltype": {"type": "integer", "format": "int32"}, "tags": {"type": "array", "items": {"type": "string"}}, "selectGroups": {"type": "string"}, "selectTags": {"type": "string"}, "selectHosts": {"type": "string"}, "selectTemplates": {"type": "string"}, "selectParentTemplates": {"type": "string"}, "selectHttpTests": {"type": "string"}, "selectItems": {"type": "string"}, "selectDiscoveries": {"type": "string"}, "selectTriggers": {"type": "string"}, "selectGraphs": {"type": "string"}, "selectMacros": {"type": "string"}, "selectDashboards": {"type": "string"}, "selectValueMaps": {"type": "string"}, "limitSelects": {"type": "integer", "format": "int32"}, "sortfield": {"type": "array", "items": {"type": "string"}}}}, "ResultListTemplateGetDTO": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "result": {"type": "array", "description": "方法返回的数据", "items": {"$ref": "#/components/schemas/TemplateGetDTO"}}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}, "TemplateGetDTO": {"type": "object", "properties": {"ipmi_privilege": {"type": "string"}, "maintenance_status": {"type": "string"}, "tls_psk_identity": {"type": "string"}, "flags": {"type": "string"}, "description": {"type": "string"}, "tls_issuer": {"type": "string"}, "uuid": {"type": "string"}, "auto_compress": {"type": "string"}, "proxy_hostid": {"type": "string"}, "maintenanceid": {"type": "string"}, "maintenance_from": {"type": "string"}, "ipmi_authtype": {"type": "string"}, "ipmi_username": {"type": "string"}, "host": {"type": "string"}, "tls_psk": {"type": "string"}, "custom_interfaces": {"type": "string"}, "proxy_address": {"type": "string"}, "maintenance_type": {"type": "string"}, "tls_accept": {"type": "string"}, "templateid": {"type": "string"}, "lastaccess": {"type": "string"}, "ipmi_password": {"type": "string"}, "name": {"type": "string"}, "tls_connect": {"type": "string"}, "tls_subject": {"type": "string"}, "status": {"type": "string"}, "jmx_available": {"type": "string"}, "errors_from": {"type": "string"}, "available": {"type": "string"}, "snmp_errors_from": {"type": "string"}, "error": {"type": "string"}, "jmx_errors_from": {"type": "string"}, "snmp_disable_until": {"type": "string"}, "jmx_error": {"type": "string"}, "jmx_disable_until": {"type": "string"}, "disable_until": {"type": "string"}, "ipmi_errors_from": {"type": "string"}, "snmp_error": {"type": "string"}, "snmp_available": {"type": "string"}, "ipmi_available": {"type": "string"}, "ipmi_error": {"type": "string"}, "ipmi_disable_until": {"type": "string"}}, "description": "方法返回的数据"}, "TemplateCreateCommand": {"type": "object", "properties": {"templateid": {"type": "string"}, "host": {"type": "string"}, "description": {"type": "string"}, "name": {"type": "string"}, "uuid": {"type": "string"}, "groups": {"type": "array", "items": {"$ref": "#/components/schemas/HostGroup"}}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/TemplateTag"}}, "templates": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}, "macros": {"type": "array", "items": {"$ref": "#/components/schemas/HostMacro"}}}}, "HostTestConnectionRequest": {"type": "object", "properties": {"type": {"type": "string"}, "host": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "username": {"type": "string"}, "password": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "string"}}}, "HostPageRequest": {"type": "object", "properties": {"filter": {"type": "object", "additionalProperties": {"type": "string", "description": "筛选"}, "description": "筛选"}, "search": {"type": "object", "additionalProperties": {"type": "string", "description": "搜索"}, "description": "搜索"}, "page": {"$ref": "#/components/schemas/PageRequest"}}}, "PageRequest": {"type": "object", "properties": {"size": {"type": "integer", "description": "数量", "format": "int32"}, "page": {"type": "integer", "description": "页码", "format": "int32"}}, "description": "分页请求对象"}, "HostPageVO": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "host": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "usability": {"type": "integer", "format": "int32"}, "username": {"type": "string"}, "description": {"type": "string"}}, "description": "数据"}, "PageVOHostPageVO": {"type": "object", "properties": {"data": {"type": "array", "description": "数据", "items": {"$ref": "#/components/schemas/HostPageVO"}}, "page": {"type": "integer", "description": "页码", "format": "int32"}, "size": {"type": "integer", "description": "数量", "format": "int32"}, "total": {"type": "integer", "description": "总数", "format": "int64"}, "pageTotal": {"type": "integer", "description": "总页", "format": "int32"}}, "description": "分页VO"}, "ResultPageVOHostPageVO": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "result": {"$ref": "#/components/schemas/PageVOHostPageVO"}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}, "AtomicTaskPathRequest": {"type": "object", "properties": {"path": {"type": "string"}}}, "AtomicTaskPathVO": {"type": "object", "properties": {"name": {"type": "string"}, "path": {"type": "string"}, "content": {"type": "string"}, "isDirectory": {"type": "boolean"}, "isAtomicTask": {"type": "boolean"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/AtomicTaskPathVO"}}}, "description": "方法返回的数据"}, "ResultAtomicTaskPathVO": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "result": {"$ref": "#/components/schemas/AtomicTaskPathVO"}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}, "CommonRequest": {"type": "object", "properties": {"method": {"type": "string"}, "params": {"$ref": "#/components/schemas/JsonNode"}}}, "JsonNode": {"type": "object"}, "ResultJsonNode": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "result": {"$ref": "#/components/schemas/JsonNode"}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}, "DeviceStatistics": {"type": "object", "properties": {"group": {"type": "string"}, "count": {"type": "integer", "format": "int64"}}}, "Healthy": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "warning": {"type": "integer", "format": "int32"}, "normal": {"type": "integer", "format": "int32"}}}, "ResultStatisticsVO": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "result": {"$ref": "#/components/schemas/StatisticsVO"}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}, "StatisticsVO": {"type": "object", "properties": {"healthy": {"$ref": "#/components/schemas/Healthy"}, "todayWarning": {"$ref": "#/components/schemas/TodayWarning"}, "warningCount": {"$ref": "#/components/schemas/WarningCount"}, "warningTops": {"type": "array", "items": {"$ref": "#/components/schemas/WarningTop"}}, "deviceStatistics": {"type": "array", "items": {"$ref": "#/components/schemas/DeviceStatistics"}}}, "description": "方法返回的数据"}, "TodayWarning": {"type": "object", "properties": {"count": {"type": "integer", "format": "int32"}, "warning": {"type": "integer", "format": "int32"}, "unresolvedWarning": {"type": "integer", "format": "int32"}}}, "WarningCount": {"type": "object", "properties": {"notClassified": {"type": "integer", "format": "int32"}, "information": {"type": "integer", "format": "int32"}, "warning": {"type": "integer", "format": "int32"}, "average": {"type": "integer", "format": "int32"}, "high": {"type": "integer", "format": "int32"}, "disaster": {"type": "integer", "format": "int32"}}}, "WarningTop": {"type": "object", "properties": {"warning": {"type": "integer", "format": "int32"}, "hostGroup": {"type": "string"}}}, "HostVO": {"type": "object", "properties": {"id": {"type": "string"}, "host": {"type": "string"}, "port": {"type": "integer", "format": "int32"}, "usability": {"type": "integer", "format": "int32"}, "username": {"type": "string"}}, "description": "方法返回的数据"}, "ResultHostVO": {"type": "object", "properties": {"jsonrpc": {"type": "string", "description": "JSON-RPC 协议的版本"}, "result": {"$ref": "#/components/schemas/HostVO"}, "id": {"type": "string", "description": "对应请求的标识符"}}, "description": "通用返回结果"}}}}