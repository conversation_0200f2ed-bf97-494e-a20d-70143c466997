:root {
  /* 渐变边框色 */
  --line-bg: linear-gradient(180deg, #1e74bd, #32b7d8, #3eded2);
  /* 内容背景色 */
  --content-bg: linear-gradient(180deg, #11151c 0%, #1a396e 100%);
  /* clip-path裁剪 */
  --path: polygon(0 0, calc(100% - 10px) 0, 100% 10px, 100% 100%, 10px 100%, 0 calc(100% - 10px));
}
.card {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 24px;
  background: var(--line-bg);
  border-radius: 4px;
  &:after {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 1;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    background: var(--content-bg);
    border-radius: 4px;
    box-shadow: inset 0px 0px 50px 1px #145983;
    transform: translate(-50%, -50%);
    content: '';
  }
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    .title {
      display: inline-flex;
      align-items: center;
    }
    .title {
      margin-bottom: 0;
      color: #fff;
    }
    .icon {
      position: relative;
      width: 4px;
      height: 4px;
      margin-right: 16px;
      background-color: #30b2b8;
      &::before,
      &::after {
        position: absolute;
        width: 4px;
        height: 4px;
        content: '';
      }
      &::before {
        top: -8px;
        left: 0;
        background-color: #3eded2;
      }
      &::after {
        bottom: -8px;
        left: 0;
        background-color: #22859d;
      }
    }
  }
  .box,
  .header {
    position: relative;
    z-index: 3;
  }
}
