import '@/utils/theme';
import { Liquid, LiquidConfig } from '@ant-design/charts';

export type LiquidProps = Partial<LiquidConfig> & {
  header?: string;
  color?: string;
  innerTitle?: string;
  innerContent?: string;
  statisticColor?: string;
};
const RkLiquid: React.FC<LiquidProps> = ({
  percent = 0,
  color,
  innerTitle = '',
  innerContent = '',
  statisticColor = 'rgba(0,0,0,0.45)',
  ...rest
}) => {
  const config: LiquidConfig = {
    percent,
    outline: {
      border: 6,
      distance: 8,
    },

    wave: {
      length: 128,
    },
    color,
    statistic: {
      title: {
        content: innerTitle,
        style: {
          fontSize: '14px',
          fontWeight: 400,
          color: statisticColor,
        },
      },
      content: {
        content: innerContent,
        style: {
          fontSize: '16px',
          fontWeight: 400,
          color: statisticColor,
        },
      },
    },
    ...rest,
  };
  return <Liquid {...config} />;
};

export default RkLiquid;
