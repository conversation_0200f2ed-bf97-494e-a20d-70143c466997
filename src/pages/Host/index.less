.table-warp {
  display: flex;
  align-items: stretch;
  width: 100%;
  .left {
    display: flex;
    flex: 0 0 256px;
    flex-direction: column;
    height: 698px;
    overflow: hidden;
    background-color: #fff;
    border-right: rgba(5, 5, 5, 0.06) 1px solid;
  }
  .header {
    height: 90px;
    padding: 16px 16px 0 28px;
    background-color: #fff;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    :global(.ant-btn-link) {
      padding: 0 !important;
    }
  }
  .content {
    max-height: calc(100% - 90px);
    overflow: auto;
  }
  .tree {
    padding: 16px 0 16px 16px;
    overflow: auto;
    :global {
      .ant-tree-switcher-noop {
        display: none !important;
      }
      // .ant-tree-node-selected {
      //   .ant-tree-title {

      //   }
      // }
    }
  }
  .table-content {
    flex: 1;
    min-width: calc(100% - 256px);
    min-height: 100%;
    background-color: #fff;
  }
}
