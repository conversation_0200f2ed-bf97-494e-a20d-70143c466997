import { Column, ColumnConfig } from '@ant-design/charts';

export type ColumnProps = Partial<ColumnConfig>;
const RKColumn: React.FC<ColumnProps> = ({ data = [], ...rest }) => {
  const config: ColumnConfig = {
    data,
    xField: 'type',
    yField: 'count',
    label: false,
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: false,
      },
    },
    maxColumnWidth: 32,
    ...rest,
  };
  return <Column {...config} />;
};

export default RKColumn;
