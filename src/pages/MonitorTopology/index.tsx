import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import { deleteTopology, pageTopology } from '@/services/http/topology';
import { queryRkPagingTable, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { history, useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import CreateUpdateModalForm from './components/CreateUpdateModalForm';

const MonitorTopology: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.TopologyPageVO[]>([]);

  const reload = () => tableRef.current?.reloadAndRest?.();

  // 删除
  const { run: deleteRecord } = useRequest((ids) => deleteTopology({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success('删除成功');
      reload();
    },
    formatResult: (res) => res,
  });

  const handleDelete = async (rows: API.TopologyPageVO[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.name!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除拓扑“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  const columns: ProColumns<API.PanoramicGroupPageVO>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      width: 180,
      render: (dom, record) => {
        return <CreateUpdateModalForm key="add" initialValues={record} reload={reload} />;
      },
      initialValue: queryParams.get('name'),
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 180,
      initialValue: queryParams.get('description'),
      hideInSearch: true,
    },

    {
      title: '操作',
      fixed: 'right',
      width: 150,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space size={16}>
            <Button
              key="details"
              type="link"
              onClick={() => history.push(`/monitor/topology/details/${record?.id}`)}
            >
              详情
            </Button>
            <Button
              key="edit"
              type="link"
              onClick={() => history.push(`/monitor/topology/edit/${record?.id}`)}
            >
              编辑
            </Button>

            <Button key="del" type="link" onClick={() => handleDelete([record])}>
              删除
            </Button>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.TopologyPageVO>
        {...defaultTableConfig}
        search={SearchOptionRender}
        onSubmit={syncToUrl}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columns={columns}
        headerTitle="拓扑列表"
        toolbar={{
          actions: [<CreateUpdateModalForm key="add" reload={reload} />],
        }}
        request={async (params) => queryRkPagingTable(params, pageTopology)}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
});

export default MonitorTopology;
