import { requiredRule } from '@/utils/setting';
import { IPReg, PortReg } from '@/utils/validator';
import { EditableProTable, ProCard, ProColumns } from '@ant-design/pro-components';
import { FC, useState } from 'react';

const AgentSetter: FC<{
  value?: readonly RK_API.Interface[];
  onChange?: (val: readonly RK_API.Interface[]) => void;
}> = ({ onChange, value }) => {
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(
    () => value?.map((item) => item.interfaceid!) ?? [],
  );
  const [dataSource, setDataSource] = useState<readonly RK_API.Interface[]>(value ?? []);

  const columns: ProColumns<RK_API.Interface>[] = [
    {
      dataIndex: 'interfaceid',
      hideInTable: true,
    },
    {
      dataIndex: 'useip',
      hideInTable: true,
    },
    {
      dataIndex: 'main',
      hideInTable: true,
    },
    {
      dataIndex: 'dns',
      hideInTable: true,
      initialValue: '',
    },
    {
      title: 'AgentIP地址',
      dataIndex: 'ip',
      width: '50%',

      formItemProps: {
        rules: [
          requiredRule,
          {
            pattern: IPReg,
            message: '请输入正确的 IP 地址',
          },
        ],
      },
    },
    {
      title: '端口',
      dataIndex: 'port',
      formItemProps: {
        rules: [
          requiredRule,
          {
            pattern: PortReg,
            message: '请输入正确的端口号',
          },
        ],
      },
    },
    {
      title: '操作',
      valueType: 'option',
    },
  ];
  return (
    <ProCard
      title="Agent配置"
      headStyle={{
        paddingInline: 0,
      }}
      bodyStyle={{
        paddingInline: 0,
      }}
    >
      <EditableProTable<RK_API.Interface>
        rowKey="interfaceid"
        className="inner-table"
        recordCreatorProps={{
          newRecordType: 'dataSource',
          position: 'bottom',
          record: () => ({
            interfaceid: (Math.random() * 1000000).toFixed(0),
            useip: '1', //使用主机接口的主机IP进行连接
            main: '0', // 0 - 不是默认; 1 - 默认。
            dns: '',
            type: '1', // 1 - agent; 2 - SNMP; 3 - IPMI; 4 - JMX
            port: '',
            ip: '',
          }),
          creatorButtonText: '添加 Agent 连接',
        }}
        value={dataSource}
        onChange={(val) => {
          setDataSource(val);
          onChange?.(val);
        }}
        onValuesChange={(recordList) => {
          onChange?.(recordList);
        }}
        columns={columns}
        editable={{
          type: 'multiple',
          editableKeys,
          actionRender: (row) => {
            return [
              <a
                key="delete"
                onClick={() => {
                  setDataSource((origin) =>
                    origin?.filter((item) => {
                      return item.interfaceid !== row.interfaceid;
                    }),
                  );
                }}
              >
                删除
              </a>,
            ];
          },

          onChange: setEditableRowKeys,
        }}
      />
    </ProCard>
  );
};

export default AgentSetter;
