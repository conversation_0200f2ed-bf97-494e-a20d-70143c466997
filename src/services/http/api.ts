// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 此处后端没有提供注释 POST /api/v1/rump/api/common */
export async function api(body: API.CommonRequest, options?: { [key: string]: any }) {
  return request<API.ResultJsonNode>('/api/v1/rump/api/common', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
