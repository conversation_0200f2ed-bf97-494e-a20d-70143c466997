import '@/utils/theme';
import { Line, LineConfig } from '@ant-design/charts';

export type LineProps = Partial<LineConfig> & {
  data: LineConfig['data'];
};
const RkLine: React.FC<LineProps> = ({ data, ...rest }) => {
  const config: LineConfig = {
    data,
    padding: 'auto',
    xAxis: {
      // type: 'timeCat',
      tickCount: 5,
    },
    ...rest,
  };

  return <Line {...config} />;
};

export default RkLine;
