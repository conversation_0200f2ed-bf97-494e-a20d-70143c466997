import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import { zabbix, zabbixDelete } from '@/services/zabbix';
import { queryPagingTable, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { history, useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import HostGroupModalForm from './components/HostGroupModalForm';

const HostMonitor: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<RK_API.HostGroup[]>([]);
  const [initialValues, setInitialValues] = useState<RK_API.HostGroup>();

  const [modalVisit, setModalVisit] = useState(false);

  // 删除
  const { run: deleteRecord } = useRequest(
    (ids) => zabbixDelete({ ids, method: 'hostgroup.delete' }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.code !== 200) return;
        message.success('删除成功');
        tableRef.current?.reloadAndRest?.();
      },
      formatResult: (res) => res,
    },
  );

  const handleDelete = async (rows: RK_API.HostGroup[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.groupid!);
      names.push(item.name!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除对象群组“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<RK_API.HostGroup>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      width: 180,
      render: (dom, record) => {
        return (
          <a
            onClick={() => {
              setModalVisit(true);
              setInitialValues(record);
            }}
          >
            {record.name}
          </a>
        );
      },
      initialValue: queryParams.get('name'),
    },
    {
      title: '主机',
      hideInSearch: true,
      dataIndex: 'hosts',
      render: (dom, entity) => {
        const length = entity?.hosts?.length;
        if (!length) return <>-</>;
        return entity?.hosts?.map((item, index) => (
          <React.Fragment key={item?.hostid}>
            {index > 0 && <a>,</a>}
            <Button
              type="link"
              danger={item.status === '1'}
              onClick={() => {
                history.push(`/monitor-config/host/details/${item?.hostid}`);
              }}
            >
              {item?.name}
            </Button>
          </React.Fragment>
        ));
      },
    },
    {
      title: '操作',
      fixed: 'right',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <a key="del" onClick={() => handleDelete([record])}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<RK_API.HostGroup>
        {...defaultTableConfig}
        search={SearchOptionRender}
        onSubmit={syncToUrl}
        scroll={{ x: '100%' }}
        rowKey="groupid"
        actionRef={tableRef}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columns={columns}
        headerTitle="对象群组列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisit(true);
                setInitialValues({});
              }}
            >
              新建对象群组
            </Button>,
          ],
        }}
        request={async (params) => {
          const { name } = params;
          return queryPagingTable<RK_API.TemplateGetDTO>(
            {
              search: {
                name,
              },
              sortfield: 'name',
              selectHosts: 'extend',
              method: 'hostgroup.get',
            },
            zabbix,
          );
        }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
      {/* 新建/编辑 */}
      <HostGroupModalForm
        initialValues={initialValues}
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
});

export default HostMonitor;
