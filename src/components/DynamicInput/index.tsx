import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { nanoid } from '@ant-design/pro-components';
import { Input, Space } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';

export type InputItems = {
  id: React.Key;
  value: string;
};

type DynamicInputProps = {
  value?: InputItems[];
  onChange?: (val: InputItems[]) => void;
};

const DynamicInput: React.FC<DynamicInputProps> = ({ value, onChange }) => {
  const [inputs, setInputs] = useState<InputItems[]>([]);

  useEffect(() => {
    if (value?.length) setInputs(value);
    if (typeof value === 'string') setInputs([{ id: nanoid(), value }]);
  }, [value]);

  const handleAddInput = () => {
    const newInputId = nanoid();
    const newInput = { id: newInputId, value: '' };
    const arr = [...inputs, newInput];
    setInputs(arr);
    onChange?.(arr);
  };

  const handleRemoveInput = (id: React.Key) => {
    if (inputs.length === 1) {
      return;
    }
    const updatedInputs = inputs.filter((input) => input.id !== id);
    setInputs(updatedInputs);
    onChange?.(updatedInputs);
  };

  const handleInputChange = (id: React.Key, value: string) => {
    const updatedInputs = inputs.map((input) => (input.id === id ? { ...input, value } : input));
    setInputs(updatedInputs);
    onChange?.(updatedInputs);
  };

  return (
    <div>
      {inputs.map((input) => (
        <div key={input.id} className={styles['dynamic-input']}>
          <Space>
            <Input
              value={input.value}
              onChange={(e) => handleInputChange(input.id, e.target.value)}
            />
            {inputs.length >= 1 && <PlusCircleOutlined onClick={handleAddInput} />}
            {inputs.length > 1 && (
              <MinusCircleOutlined onClick={() => handleRemoveInput(input.id)} />
            )}
          </Space>
        </div>
      ))}
    </div>
  );
};

export default DynamicInput;
