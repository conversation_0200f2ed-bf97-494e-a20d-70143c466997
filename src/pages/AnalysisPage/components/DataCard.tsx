import { zabbixPost } from '@/services/zabbix';
import { groupedData } from '@/utils';
import { useRequest } from '@umijs/max';
import { ColProps, Row } from 'antd';
import { FC, useMemo } from 'react';
import LineChartCard from './LineChartCard';

type DataType = 'CPU' | 'Memory' | 'Disk';
export type UsageRateProps = RK_API.Item & {
  title: string;
  hostName: string;
  color: string;
  dataType: DataType;
};

export const covertData = (arr: RK_API.Item[], title: string, type: DataType, color: string) => {
  if (!arr?.length || !title) return [];
  return arr.map((item) => ({
    ...item,
    title,
    hostName: item?.hosts?.[0]?.name || '',
    dataType: type,
    color,
  }));
};

const DataCard: FC<{
  data?: API.PanoramicGroupProblemPageVO;
  span?: ColProps['span'];
  type?: 'card' | 'list';
}> = ({ data, span, type = 'card' }) => {
  // 获取 cpu使用率(CPU) 内存使用率(MEM) 磁盘使用率(Filesystem)
  const { data: cpuUsageArray = [] } = useRequest(
    () =>
      zabbixPost({
        method: 'item.get',
        hostids: data?.hostIds,
        tags: [{ tag: 'rkmon_view_type_rate', value: 'CPU', operator: '1' }],
        selectHosts: 'extend',

        output: ['itemid', 'name', 'hostid', 'value_type'],
      }),
    {
      ready: !!data?.id,
      refreshDeps: [data?.id],
    },
  );

  const { data: memoryUsageArray = [] } = useRequest(
    () =>
      zabbixPost({
        method: 'item.get',
        hostids: data?.hostIds,
        tags: [{ tag: 'rkmon_view_type_rate', value: 'MEM', operator: '1' }],
        selectHosts: 'extend',
        output: ['itemid', 'name', 'hostid', 'value_type'],
      }),
    {
      ready: !!data?.id,
      refreshDeps: [data?.id],
    },
  );
  const { data: diskUsageArray = [] } = useRequest(
    () =>
      zabbixPost({
        method: 'item.get',
        hostids: data?.hostIds,
        tags: [{ tag: 'rkmon_view_type_rate', value: 'Filesystem /', operator: '1' }],
        selectHosts: 'extend',
        output: ['itemid', 'name', 'hostid', 'value_type'],
      }),
    {
      ready: !!data?.id,
      refreshDeps: [data?.id],
    },
  );
  const usageRateData = useMemo(() => {
    const arr = [
      ...covertData(cpuUsageArray, 'cpu使用率', 'CPU', '#38CCE1'),
      ...covertData(memoryUsageArray, '内存使用率', 'Memory', '#EA22FD'),
      ...covertData(diskUsageArray, '磁盘使用率', 'Disk', '#A66CFE'),
    ];
    return groupedData(arr, 'hostid');
  }, [cpuUsageArray, memoryUsageArray, diskUsageArray]);

  return (
    <Row gutter={[24, 16]} wrap style={{ alignItems: 'stretch' }}>
      {Object.entries(usageRateData).map(([hostid, value]) => {
        // @ts-ignore
        const title = value?.at(0)?.hostName || '';
        return <LineChartCard key={hostid} title={title} value={value} span={span} type={type} />;
      })}
    </Row>
  );
};

export default DataCard;
