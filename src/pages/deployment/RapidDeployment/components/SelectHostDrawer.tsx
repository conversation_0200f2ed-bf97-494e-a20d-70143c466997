import { HOST_TYPE, HOST_USABILITY } from '@/enums';
import { hostPage } from '@/services/http/host';
import { hostGroupList } from '@/services/http/hostGroup';
import { option2enum } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { DrawerForm, DrawerFormProps, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button } from 'antd';
import { FC, memo, useState } from 'react';

const SelectHostDrawer: FC<
  DrawerFormProps & {
    handleSelect?: (selectedRows: API.HostPageVO[]) => void;
  }
> = ({ handleSelect, initialValues }) => {
  const [selectedRows, setSelectedRows] = useState<API.HostPageVO[]>([]);
  const onFinish = async () => {
    handleSelect?.(selectedRows);
    return true;
  };

  //主机组列表
  const { data: hostGroups, loading: hostGroupsLoading } = useRequest(hostGroupList);

  const columns: ProColumns<API.HostPageVO>[] = [
    {
      title: '主机名',
      dataIndex: 'host',
      hideInSearch: true,
    },
    {
      title: '主机组',
      dataIndex: 'groupIds',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        options: hostGroups,
        loading: hostGroupsLoading,
        fieldNames: {
          value: 'id',
          label: 'name',
        },
      },
      hideInTable: true,
    },
    {
      title: '主机类型',
      dataIndex: 'type',
      valueEnum: option2enum(HOST_TYPE),
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'usability',
      valueEnum: option2enum(HOST_USABILITY),
      hideInSearch: true,
    },
    {
      title: '描述',
      width: 200,
      ellipsis: true,
      dataIndex: 'description',
      hideInSearch: true,
    },
  ];

  return (
    <DrawerForm
      width={600}
      title="添加主机"
      trigger={
        <Button type="primary">
          <PlusOutlined />
          添加主机
        </Button>
      }
      autoFocusFirstInput
      drawerProps={{
        destroyOnClose: true,
      }}
      onFinish={onFinish}
    >
      <ProTable<API.HostPageVO>
        className="inner-table"
        {...defaultTableConfig}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          getCheckboxProps: (record) => ({
            disabled: initialValues?.hostList?.includes(record.id),
          }),
        }}
        search={SearchOptionRender}
        options={false}
        columns={columns}
        headerTitle="主机列表"
        request={async ({ usability, type, groupIds, current, pageSize, ...params }) => {
          const data = {
            groupIds: groupIds ? [groupIds] : undefined,
            page: {
              page: current,
              size: pageSize,
            },
            filter: {
              usability,
              type,
            },
            search: {
              ...params,
            },
          };
          const msg = await hostPage(data);
          const result = msg?.data;
          return {
            data: result?.data || [],
            success: true,
            total: (result?.total as unknown as number) || 0,
          };
        }}
      />
    </DrawerForm>
  );
};

export default memo(SelectHostDrawer);
