import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { Key, useCallback, useRef, useState } from 'react';

import OperateFooterToolbar from '@/components/OperateFooterToolbar';

import { HOST_AGENT_STATUS, HOST_TYPE, HOST_USABILITY } from '@/enums';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import {
  hostBatchTestConnection,
  hostDeleteByIds,
  hostDeployAgent,
  hostPage,
} from '@/services/http/host';
import { hostGroupList } from '@/services/http/hostGroup';
import { option2enum, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import HostDrawerForm from './components/HostDrawerForm';

const Host: React.FC<AuthProps> = withStorageToUrl(({ queryParams }) => {
  const [drawerVisit, setDrawerVisit] = useState(false);
  const [drawerTitle, setDrawerTitle] = useState('新建主机');
  const [initialValues, setInitialValues] = useState<Record<string, any>>({});
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.HostPageVO[]>([]);
  const hostMap = useRef<Record<string, string>>({});

  const onEdit = (record: API.HostPageVO) => {
    setInitialValues(record);
    setDrawerVisit(true);
    setDrawerTitle('编辑主机');
  };

  const { run: deleteRecord } = useRequest((ids) => hostDeleteByIds({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  // 删除
  const handleDelete = useCallback((rows: API.HostPageVO[]) => {
    const ids: Key[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.host!);
    });

    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除主机“${names.join(',')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        deleteRecord(ids);
      },
    });
  }, []);

  // 测试连接
  const { run: testConnection } = useRequest((ids) => hostBatchTestConnection({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      const errorHosts = res?.data
        ?.filter((item: API.HostBatchTestConnectionVO) => ['-1', '0'].includes(item.usability!))
        .map((item: API.HostBatchTestConnectionVO) => hostMap.current[item.id!]);

      if (errorHosts.length) {
        message.error(`主机“${errorHosts.join(', ')}”未连接`);
      } else {
        message.success('主机已连接');
      }
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  // 重新安装
  const { run: reDeploy } = useRequest((id) => hostDeployAgent({ id }), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success('操作成功!');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });

  //主机组列表
  const { data: hostGroups, loading: hostGroupsLoading } = useRequest(hostGroupList);

  const handleConnection = useCallback((rows: API.HostPageVO[]) => {
    const ids: Key[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.host!);
      hostMap.current[item.id!] = item.host!;
    });

    Modal.confirm({
      title: '测试连接',
      content: `测试连接主机“${names.join(',')}”`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        testConnection(ids);
      },
    });
  }, []);

  const onOperation = (key: string | number) => {
    if (key === 'connect') {
      handleConnection(selectedRows);
    }
  };

  // 表格
  const columns: ProColumns<API.HostPageVO>[] = [
    {
      title: '主机名',
      dataIndex: 'host',
      render: (dom, entity) => {
        return <a onClick={() => onEdit(entity)}>{dom}</a>;
      },
      initialValue: queryParams.get('host'),
    },
    {
      title: '主机组',
      dataIndex: 'groupIds',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        options: hostGroups,
        loading: hostGroupsLoading,
        fieldNames: {
          value: 'id',
          label: 'name',
        },
      },
      initialValue: queryParams.get('groupIds'),
      hideInTable: true,
    },
    {
      title: '主机类型',
      dataIndex: 'type',
      valueEnum: option2enum(HOST_TYPE),
    },
    {
      title: '状态',
      dataIndex: 'usability',
      valueEnum: option2enum(HOST_USABILITY),
      search: {
        transform: (value) => ({
          usability: parseInt(value),
        }),
      },
    },
    {
      title: 'agent状态',
      dataIndex: ['agent', 'status'],
      valueEnum: option2enum(HOST_AGENT_STATUS),
      hideInSearch: true,
    },
    {
      title: 'agent版本',
      dataIndex: ['agent', 'version'],
      hideInSearch: true,
    },
    {
      title: '描述',
      width: 200,
      ellipsis: true,
      dataIndex: 'description',
      hideInSearch: true,
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        const { agent } = record;
        return (
          <Space>
            <a key="connection" onClick={() => handleConnection([record])}>
              测试连接
            </a>
            {agent?.status === 'Fail' && (
              <a
                key="reinstall"
                onClick={() => {
                  reDeploy(record?.id);
                }}
              >
                重新安装
              </a>
            )}
            <a key="del" onClick={() => handleDelete([record])}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.HostPageVO>
        {...defaultTableConfig}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        search={SearchOptionRender}
        onSubmit={syncToUrl}
        columns={columns}
        headerTitle="主机列表"
        actionRef={tableRef}
        toolbar={{
          actions: [
            // <Upload
            //   key="upload"
            //   action="/api/host/batchImport"
            //   method="post"
            //   headers={{
            //     token: localStorage.getItem('RKLINK_OPM_TOKEN') + '',
            //   }}
            //   showUploadList={false}
            //   onChange={(info) => {
            //     if (info.file.status === 'done') {
            //       // 文件上传成功
            //       message.success(`${info.file.name} 上传成功`);
            //       tableRef.current?.reload();
            //     } else if (info.file.status === 'error') {
            //       // 文件上传失败
            //       message.error(`${info.file.name}上传失败`);
            //     }
            //   }}
            // >
            //   <Button key="primary" type="primary" disabled>
            //     <UploadOutlined />
            //     批量导入
            //   </Button>
            // </Upload>,
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setDrawerVisit(true);
                setDrawerTitle('新建主机');
                setInitialValues({});
              }}
            >
              新建主机
            </Button>,
          ],
        }}
        polling={10000}
        request={async ({ usability, type, groupIds, current, pageSize, ...params }) => {
          const data = {
            groupIds: groupIds ? [groupIds] : undefined,
            page: {
              page: current,
              size: pageSize,
            },
            filter: {
              usability,
              type,
            },
            search: {
              ...params,
            },
          };
          const msg = await hostPage(data);
          const result = msg?.data;
          return {
            data: result?.data || [],
            success: true,
            total: (result?.total as unknown as number) || 0,
          };
        }}
      />
      <OperateFooterToolbar
        selectedRows={selectedRows}
        onDelete={handleDelete}
        actions={[
          {
            key: 'connect',
            label: '批量测试连接',
          },
        ]}
        onOperation={onOperation}
      />

      {/* 新建/编辑主机 */}
      <HostDrawerForm
        initialValues={initialValues}
        title={drawerTitle}
        open={drawerVisit}
        onOpenChange={(visible) => {
          setDrawerVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
});

export default Host;
