import { Button } from 'antd';
import { setValuesToUndefined, syncToUrl } from '.';

const SearchOptionRender = {
  defaultCollapsed: false,
  // @ts-ignore
  optionRender: (searchConfig, formProps, dom) => [
    <Button
      key="out"
      onClick={() => {
        const values = searchConfig?.form?.getFieldsValue();
        syncToUrl({});
        setValuesToUndefined(values);
        searchConfig?.form?.setFieldsValue(setValuesToUndefined(values));
        searchConfig?.form?.submit();
      }}
    >
      清空
    </Button>,
    dom?.at(1),
  ],
};

export default SearchOptionRender;
