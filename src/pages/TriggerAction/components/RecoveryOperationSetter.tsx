import BaseContext from '@/Context/BaseContext';
import { RECOVERY_OPERATION_TYPE, UPDATE_OPERATION_TYPE } from '@/enums';
import { zabbix } from '@/services/zabbix';
import { getRandomId } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  ProColumns,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, Space, Typography } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import React, { memo, useCallback, useContext, useRef, useState } from 'react';

type OperationSetterProps = {
  value?: RK_API.RecoveryOperations[];
  onChange?: (value: RK_API.RecoveryOperations[]) => void;
  type?: string;
};
const { Text } = Typography;

const RecoveryOperationSetter: React.FC<OperationSetterProps> = ({
  value = [],
  onChange,
  type,
}) => {
  const drawerFormRef = useRef<ProFormInstance>();
  const [open, setOpen] = useState(false);
  const initialValuesRef = useRef<RK_API.RecoveryOperations>();

  const {
    userList = [],
    userLoading,
    userGroupList,
    userGroupLoading,
    hostGroupList = [],
    hostGroupLoading,
    hostList = [],
    hostLoading,
  } = useContext(BaseContext);

  // 媒介类型
  const { data: mediaTypeList = [], loading: mediaTypeLoading } = useRequest(() =>
    zabbix({ method: 'mediatype.get' }),
  );

  const renderDetails = useCallback(
    (_: any, record: RK_API.RecoveryOperations) => {
      const {
        operationtype,
        opmessage_grp = [],
        opmessage_usr = [],
        opmessage,
        opcommand,
        opcommand_hst = [],
        opcommand_grp = [],
      } = record;
      if (['11', '12'].includes(operationtype)) {
        return <Text strong>通知所有参与者</Text>;
      }
      // 发送信息
      if (operationtype === '0') {
        const arr = [...opmessage_usr, ...opmessage_grp];
        const mediaType = [{ mediatypeid: '0', name: '所有介质' }, ...mediaTypeList].find(
          (item) => item.mediatypeid === opmessage.mediatypeid,
        )?.name;
        return (
          <Space direction="vertical" size={2}>
            {arr.map((item, index) => {
              const str = item.userid ? '用户' : '用户组';
              const receiver = item.userid
                ? userList?.find((user) => user.userid === item?.userid)?.username
                : userGroupList?.find((group) => group.usrgrpid === item?.usrgrpid)?.name;
              return (
                <div key={index}>
                  <Text strong>
                    通过{mediaType}发送信息给{str}：
                  </Text>
                  <Text>{receiver}</Text>
                </div>
              );
            })}
          </Space>
        );
      }
      // 脚本信息
      if (operationtype === '1') {
        const opera = opcommand?.scriptid === '1' ? '运行“Ping”脚本' : '运行“Traceroute”脚本';
        const arr = [...opcommand_hst, ...opcommand_grp];
        return (
          <Space direction="vertical" size={2}>
            {arr.map((item, index) => {
              const receiver = item.hostid
                ? item?.hostid === '0'
                  ? '在当前主机上'
                  : `在主机${hostList?.find((host) => host.hostid === item?.hostid)?.name}`
                : `在对象群组${
                    hostGroupList?.find((group) => group.groupid === item?.groupid)?.name
                  }`;
              return (
                <div key={index}>
                  <Text>{receiver}</Text>
                  <Text strong>{opera}</Text>
                </div>
              );
            })}
          </Space>
        );
      }
      return '';
    },
    [userLoading, userGroupLoading, hostLoading, hostGroupLoading, mediaTypeLoading],
  );

  const onFinish = async (val: RK_API.Operation) => {
    const isItemExists = value.some((item) => item.operationid === val.operationid);

    const dataSource = isItemExists
      ? value.map((item) => (item.operationid === val.operationid ? val : item))
      : [...value, val];

    onChange?.(dataSource);

    return true;
  };

  // 表格
  const columns: ProColumns<RK_API.RecoveryOperations>[] = [
    {
      title: '细节',
      dataIndex: 'details',
      render: renderDetails,
    },
    {
      title: '操作',
      width: 80,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (_, record) => {
        return (
          <Space>
            <a
              onClick={() => {
                setOpen(true);
                initialValuesRef.current = record;
              }}
            >
              编辑
            </a>
            <a
              onClick={() => {
                const arr = value.filter((item) => item.operationid !== record.operationid);
                onChange?.(arr);
              }}
            >
              移除
            </a>
          </Space>
        );
      },
    },
  ];
  // 操作类型 数据处理 operationtype
  const transformOperationType = useCallback((val: string) => {
    const arr = val.split('-');
    const type = arr?.[0];
    const scriptid = arr?.[1] || '';
    return {
      operationtype: type,
      opcommand: scriptid
        ? {
            scriptid: scriptid,
          }
        : undefined,
    };
  }, []);

  const getOperationType = (val: string) => {
    const opcommand = drawerFormRef.current?.getFieldValue('opcommand');
    if (val === '1') {
      return `${val}-${opcommand?.scriptid}`;
    }
    return val;
  };

  return (
    <>
      <ProTable<RK_API.RecoveryOperations>
        {...defaultTableConfig}
        search={false}
        options={false}
        className="inner-table"
        rowKey="operationid"
        columns={columns}
        headerTitle={type === 'update' ? '更新操作' : '恢复操作'}
        dataSource={value}
        toolbar={{
          actions: [
            <Button key="add" type="primary" onClick={() => setOpen(true)}>
              新建
            </Button>,
          ],
        }}
      />
      <DrawerForm
        width={460}
        initialValues={{
          operationid: getRandomId(),
          operationtype: '0',
          opmessage: {
            default_msg: '1', // 使用 媒介类型中的数据。
            mediatypeid: '0',
          },
          opcommand_hst: [
            {
              hostid: '0',
            },
          ],
          ...initialValuesRef.current,
        }}
        title="操作细节"
        open={open}
        onOpenChange={setOpen}
        autoFocusFirstInput
        formRef={drawerFormRef}
        drawerProps={{
          destroyOnClose: true,
        }}
        onFinish={onFinish}
      >
        <div className="rk-none ">
          <ProFormText name={'operationid'} />
        </div>

        <ProFormSelect
          name="operationtype"
          label="操作类型"
          rules={[requiredRule]}
          fieldProps={{
            allowClear: false,
          }}
          options={type === 'update' ? UPDATE_OPERATION_TYPE : RECOVERY_OPERATION_TYPE}
          convertValue={getOperationType}
          getValueFromEvent={getOperationType}
          transform={transformOperationType}
        />
        <ProFormDependency name={['operationtype']}>
          {({ operationtype }) => {
            // 0 发送信息
            return (
              operationtype === '0' && (
                <>
                  <ProFormSelect
                    label="用户组"
                    name="opmessage_grp"
                    options={userGroupList as DefaultOptionType[]}
                    fieldProps={{
                      showSearch: true,
                      mode: 'multiple',
                      loading: userGroupLoading,
                      fieldNames: {
                        value: 'usrgrpid',
                        label: 'name',
                      },
                    }}
                    getValueFromEvent={(value = []) =>
                      value.map((item: string) => ({ usrgrpid: item }))
                    }
                    convertValue={(value = []) =>
                      value.map((item: RK_API.UsrgrpsItem) => item?.usrgrpid)
                    }
                    transform={(value: RK_API.UsrgrpsItem[], namePath) => ({
                      [namePath]: value.map(({ usrgrpid }) => ({ usrgrpid })),
                    })}
                  />
                  <ProFormSelect
                    label="用户"
                    name="opmessage_usr"
                    options={userList as DefaultOptionType[]}
                    fieldProps={{
                      showSearch: true,
                      mode: 'multiple',
                      loading: userLoading,
                      fieldNames: {
                        value: 'userid',
                        label: 'username',
                      },
                    }}
                    rules={[
                      ({ getFieldValue }) => ({
                        validator: (_, value) => {
                          const op_message_grp = getFieldValue('opmessage_grp');
                          if (!value && !op_message_grp) {
                            return Promise.reject(new Error('用户和用户组至少选择一个'));
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                    getValueFromEvent={(value = []) =>
                      value.map((item: string) => ({ userid: item }))
                    }
                    convertValue={(value = []) => value.map((item: RK_API.User) => item?.userid)}
                    transform={(value: RK_API.User[], namePath) => ({
                      [namePath]: value.map(({ userid }) => ({ userid })),
                    })}
                  />
                  <ProFormSelect
                    label="仅送到"
                    name={['opmessage', 'mediatypeid']}
                    options={[{ mediatypeid: '0', name: '所有介质' }, ...mediaTypeList]}
                    fieldProps={{
                      fieldNames: {
                        value: 'mediatypeid',
                        label: 'name',
                      },
                      loading: mediaTypeLoading,
                    }}
                  />
                </>
              )
            );
          }}
        </ProFormDependency>
        <ProFormDependency name={['operationtype']}>
          {({ operationtype }) => {
            // 11 通知所有相关人员
            return (
              ['11', '12', '0'].includes(operationtype) && (
                <>
                  <ProFormSwitch
                    label="自定义消息"
                    name={['opmessage', 'default_msg']}
                    getValueFromEvent={(val) => (val ? '0' : '1')}
                    getValueProps={(value) => ({ checked: value === '0' })}
                  />
                  <ProFormDependency name={['opmessage', 'default_msg']}>
                    {({ opmessage }) => {
                      return opmessage.default_msg === '1' ? (
                        <></>
                      ) : (
                        <>
                          <ProFormText name={['opmessage', 'subject']} label="主题" />
                          <ProFormTextArea
                            name={['opmessage', 'message']}
                            label="消息"
                            fieldProps={{ autoSize: { minRows: 3, maxRows: 3 } }}
                          />
                        </>
                      );
                    }}
                  </ProFormDependency>
                </>
              )
            );
          }}
        </ProFormDependency>
        <ProFormDependency name={['operationtype']}>
          {({ operationtype }) => {
            // 1 全局脚本
            return (
              operationtype === '1' && (
                <>
                  <ProFormSelect
                    label="对象群组"
                    name="opcommand_grp"
                    options={hostGroupList as DefaultOptionType[]}
                    fieldProps={{
                      showSearch: true,
                      mode: 'multiple',
                      fieldNames: {
                        value: 'groupid',
                        label: 'name',
                      },
                      loading: hostGroupLoading,
                    }}
                    getValueFromEvent={(value = []) =>
                      value.map((item: string) => ({ groupid: item }))
                    }
                    convertValue={(value = []) =>
                      value.map((item: RK_API.HostGroup) => item?.groupid)
                    }
                    transform={(value: RK_API.HostGroup[], namePath) => ({
                      [namePath]: value.map(({ groupid }) => ({ groupid })),
                    })}
                  />
                  <ProFormSelect
                    label="主机"
                    name="opcommand_hst"
                    options={
                      [{ hostid: '0', name: '本机' }, ...hostList] as unknown as DefaultOptionType[]
                    }
                    fieldProps={{
                      showSearch: true,
                      mode: 'multiple',
                      loading: hostLoading,
                      fieldNames: {
                        value: 'hostid',
                        label: 'name',
                      },
                    }}
                    getValueFromEvent={(value = []) =>
                      value.map((item: string) => ({ hostid: item }))
                    }
                    convertValue={(value = []) => value.map((item: RK_API.Host) => item?.hostid)}
                    transform={(value: RK_API.Host[], namePath) => ({
                      [namePath]: value.map(({ hostid }) => ({ hostid })),
                    })}
                    rules={[
                      ({ getFieldValue }) => ({
                        validator: (_, value) => {
                          const opcommand_grp = getFieldValue('opcommand_grp');
                          if (!value && !opcommand_grp) {
                            return Promise.reject(new Error('主机和对象群组至少选择一个'));
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                  />
                </>
              )
            );
          }}
        </ProFormDependency>
      </DrawerForm>
    </>
  );
};
export default memo(RecoveryOperationSetter);
