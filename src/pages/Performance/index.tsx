import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import {
  externalMonitoringPanelRemoveByIds,
  externalMonitoringPanelRemoveByIdsPage,
} from '@/services/http/externalMonitoring';
import { queryRkPagingTable, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Access, history, useAccess, useRequest } from '@umijs/max';
import { message, Modal, Space } from 'antd';
import React, { Key, useCallback, useRef, useState } from 'react';
import PerformanceModalForm from './components/PerformanceModalForm';

const Performance: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.ExternalMonitoringPanelPageVO[]>([]);
  const { isSuperAdmin = false } = useAccess();

  const { run: deleteRecord } = useRequest((ids) => externalMonitoringPanelRemoveByIds({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  // 删除
  const handleDelete = useCallback((rows: API.ExternalMonitoringPanelPageVO[]) => {
    const ids: Key[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.name!);
    });

    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除该信息“${names.join(',')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        deleteRecord(ids);
      },
    });
  }, []);

  const columns: ProColumns<API.ExternalMonitoringPanelPageVO>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      width: 300,
      initialValue: queryParams.get('name'),
      render(dom, entity) {
        return (
          <a
            onClick={() => {
              history.push(`/monitor/performance/${entity.id}`);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      initialValue: queryParams.get('taskName'),
      hideInSearch: true,
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      valueType: 'option',
      align: 'center',
      hideInTable: !isSuperAdmin,
      render: (text, record) => {
        return (
          <Space>
            <PerformanceModalForm
              isEdit
              onSuccess={() => tableRef.current?.reloadAndRest?.()}
              initialValues={record}
            />
            <a key="del" onClick={() => handleDelete([record])}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.ExternalMonitoringPanelPageVO>
        {...defaultTableConfig}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        search={SearchOptionRender}
        onSubmit={syncToUrl}
        actionRef={tableRef}
        columns={columns}
        toolbar={{
          actions: [
            <Access accessible={isSuperAdmin} key="create">
              <PerformanceModalForm onSuccess={() => tableRef.current?.reloadAndRest?.()} />
            </Access>,
          ],
        }}
        headerTitle="监控列表"
        request={async (params) =>
          queryRkPagingTable({ ...params }, externalMonitoringPanelRemoveByIdsPage)
        }
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
});

export default Performance;
