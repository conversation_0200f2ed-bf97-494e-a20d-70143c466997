import Footer from '@/components/Footer';
import { userLogin } from '@/services/http/user';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { LoginForm, ProFormText } from '@ant-design/pro-components';
import { useEmotionCss } from '@ant-design/use-emotion-css';
import { Helmet, history, useModel } from '@umijs/max';
import { message } from 'antd';
import classNames from 'classnames/bind';
import defaultSettings from 'config/defaultSettings';
import React from 'react';
import { flushSync } from 'react-dom';

import styles from './index.less';

const cx = classNames.bind(styles);

const Login: React.FC = () => {
  const { setInitialState } = useModel('@@initialState');

  const containerClassName = useEmotionCss(() => {
    return {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage: "url('/images/loginBg.jpeg')",
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
    };
  });

  // 自动登录
  // const credentialsCookie = getCredentialsCookie();
  // const { account, password } = credentialsCookie || {};
  // if (account && password) return <AutoLogin account={account} password={password} />;

  const handleSubmit = async (values: any) => {
    try {
      // 登录
      const res = await userLogin(values);
      const { code, data } = res || {};
      if (code === 200) {
        message.success('登录成功');
        localStorage.setItem(defaultSettings.TOKEN_KEY, data?.token || '');
        const urlParams = new URL(window.location.href).searchParams;
        flushSync(() => {
          setInitialState((s = {}) => ({
            ...s,
            currentUser: {},
          }));
        });

        setTimeout(() => {
          history.push(urlParams.get('redirect') || '/');
          // 解决登录成功后，initialState值还没生效，导致页面跳转异常
          window.location.reload();
        }, 500);
        return;
      }
    } catch (error) {
      console.log('🚗 🚗 🚗 ~ file: index.tsx:47 ~ handleSubmit ~ error:', error);
    }
  };

  return (
    <div className={containerClassName}>
      <Helmet>
        <title>登录 - {defaultSettings.title}</title>
      </Helmet>
      <div className={cx('login-content')}>
        <div className={cx('login-bg')} />
        <div className={cx('login-form')}>
          <LoginForm
            contentStyle={{
              minWidth: 280,
              maxWidth: '75vw',
            }}
            logo={<img alt="logo" src="/images/logo_simple.png" />}
            title="融科小E"
            initialValues={{
              autoLogin: true,
            }}
            onFinish={async (values) => {
              await handleSubmit(values as any);
            }}
          >
            <ProFormText
              name="account"
              fieldProps={{
                size: 'large',
                prefix: <UserOutlined />,
              }}
              placeholder="请输入用户名"
              rules={[
                {
                  required: true,
                  message: '请输入用户名!',
                },
              ]}
            />
            <ProFormText.Password
              name="password"
              fieldProps={{
                size: 'large',
                prefix: <LockOutlined />,
              }}
              placeholder="请输入密码"
              rules={[
                {
                  required: true,
                  message: '请输入密码！',
                },
              ]}
            />
          </LoginForm>
          <Footer />
        </div>
      </div>
    </div>
  );
};

export default Login;
