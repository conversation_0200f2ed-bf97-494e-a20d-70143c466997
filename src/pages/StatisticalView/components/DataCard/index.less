.parent {
  // height: 160px;
  height: 100%;
  margin-bottom: 16px;
  perspective: 1000px;
  cursor: pointer;
  // --card-bg: linear-gradient(135deg, rgb(0, 255, 214) 0%, rgb(8, 226, 96) 100%);
  --card-bg: linear-gradient(135deg, #fff 0%, rgba(0, 0, 0, 0.15) 100%);
  &:hover {
    .card {
      box-shadow: rgba(5, 68, 71, 0.3) 30px 50px 25px -40px, rgba(5, 61, 71, 0.1) 0px 25px 30px 0px;
      transform: rotate3d(1, 1, 0, 30deg);
      .logo {
        .circle2 {
          transform: translate3d(0, 0, 60px);
        }
        .circle3 {
          transform: translate3d(0, 0, 80px);
        }
        .circle4 {
          transform: translate3d(0, 0, 100px);
        }
        .circle5 {
          transform: translate3d(0, 0, 120px);
        }
      }
    }
  }
}

.card {
  height: 100%;
  background: var(--card-bg);
  border-radius: 50px;
  box-shadow: rgba(0, 0, 0, 0.15) 40px 50px 25px -40px, rgba(0, 0, 0, 0.1) 0px 25px 25px -5px;
  transform-style: preserve-3d;
  transition: all 0.5s ease-in-out;
}

.glass {
  position: absolute;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.349) 0%, rgba(255, 255, 255, 0.815) 100%);
  border-bottom: 1px solid #fff;
  border-left: 1px solid #fff;
  border-radius: 55px;
  border-top-right-radius: 100%;
  transform: translate3d(0px, 0px, 25px);
  transform-style: preserve-3d;
  transition: all 0.5s ease-in-out;
  inset: 8px;
}

.content {
  padding: 36px;
  padding-bottom: 16px;
  transform: translate3d(0, 0, 26px);
}
