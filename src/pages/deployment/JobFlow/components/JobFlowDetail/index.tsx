import R<PERSON><PERSON>ol from '@/components/RKCol';
import YAMLEditor from '@/components/YAMLEditor';
import BaseContext from '@/Context/BaseContext';
import { HOST_MERGE_TYPE, RUN_MODE } from '@/enums';
import { listAtomicTask } from '@/services/http/atomicTask';
import {
  taskModuleChangeHistory,
  taskModuleCreate,
  taskModuleFindById,
  taskModuleUpdateById,
} from '@/services/http/taskModule';
import { onSuccessAndGoBack, onSuccessAndRefresh } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { Cell, Edge, Graph, Node } from '@antv/x6';
import { history, useParams, useRequest } from '@umijs/max';
import { Form, message, Row, Spin } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import classNames from 'classnames/bind';
import React, { useRef, useState } from 'react';
import Flow from '../Flow';
import JobForm from '../JobForm';
import styles from './index.less';

const cx = classNames.bind(styles);

export type DataProps = {
  nodes: Node.Metadata[];
  edges: Edge.Metadata[];
};

const formatFlowData = (arr?: Cell.Properties[]) => {
  const result: DataProps = {
    edges: [],
    nodes: [],
  };
  if (!arr?.length) return result;
  for (const item of arr) {
    if (item.shape === 'edge') {
      result.edges.push(item);
    } else {
      result.nodes.push(item);
    }
  }
  return result;
};

// 判断是不是空节点
export const hasEmptyWorkId = (dataArray: Node.Metadata[] = []) => {
  return dataArray.filter((item) => item.id !== 'start').some((item) => !item?.data?.taskId);
};

// 变更历史 column
const columns = [
  {
    title: '操作人',
    dataIndex: 'operatorName',
  },
  {
    title: '变更时间',
    dataIndex: 'changeTime',
  },
];

const JobFlowDetail: React.FC = () => {
  const editorRef = useRef();

  const [drawerForm] = Form.useForm();
  const formRef = useRef<ProFormInstance>();
  const graphRef = useRef<Graph>();
  // 判断是否为编辑页面
  const { id } = useParams();
  const isEditPage = !!id;

  const { data: taskList = [], loading: taskLoading } = useRequest(() => listAtomicTask());
  const [drawerVisit, setDrawerVisit] = useState(false);

  const { loading } = useRequest(() => taskModuleFindById({ id: id! }), {
    ready: isEditPage,
    onSuccess: (res) => {
      // @ts-ignore
      const { edges = [], nodes = [] } = res?.taskModuleFront || [];
      graphRef.current?.fromJSON({
        cells: [...edges, ...nodes],
      });
      formRef.current?.setFieldsValue(res);
    },
  });

  // 查询变更记录
  const { data: historyList, refresh } = useRequest(() => taskModuleChangeHistory({ id: id! }), {
    ready: isEditPage,
  });

  // 新建
  const { run: add, loading: addLoading } = useRequest((value) => taskModuleCreate(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 修改
  const { run: update, loading: editLoading } = useRequest(
    ({ id, ...rest }) => taskModuleUpdateById({ id }, { ...rest }),
    {
      manual: true,
      onSuccess: (res) => onSuccessAndRefresh(res, refresh()),
      formatResult: (res) => res,
    },
  );

  return (
    <ProForm
      formRef={formRef}
      submitter={{
        searchConfig: {
          submitText: '保存',
          resetText: '取消',
        },
        onReset: () => {
          history.go(-1);
        },
        render: (props, doms) => {
          return <FooterToolbar>{doms}</FooterToolbar>;
        },
        submitButtonProps: {
          loading: addLoading || editLoading,
        },
      }}
      onFinish={async (values) => {
        const taskModuleFront = formatFlowData(graphRef.current?.toJSON()?.cells);

        if (hasEmptyWorkId(taskModuleFront?.nodes)) {
          message.error('节点数据不完整！');
          return;
        }
        const res = {
          taskModuleFront,
          ...values,
        };
        if (isEditPage) {
          update(res);
        } else {
          add(res);
        }
      }}
    >
      <PageContainer
        header={{
          title: false,
        }}
        className={cx('detail-container')}
      >
        <Spin spinning={loading}>
          <div className={cx('container')}>
            <div className={cx('rklink-form-box')}>
              {/* 不需要展示，只是为了form传值 */}
              <div style={{ display: 'none' }}>
                <ProFormText name="id" label="id" />
              </div>
              <Row gutter={24}>
                <RKCol>
                  <ProFormText name="name" label="任务流名称" rules={[requiredRule]} />
                </RKCol>
                <RKCol>
                  <ProFormSelect
                    name="preInspectionTaskId"
                    label="前置检查任务"
                    rules={[requiredRule]}
                    fieldProps={{
                      options: taskList as DefaultOptionType[],
                      loading: taskLoading,
                      fieldNames: {
                        value: 'id',
                        label: 'name',
                      },
                      showSearch: true,
                    }}
                  />
                </RKCol>
                <RKCol>
                  <ProFormSelect
                    rules={[requiredRule]}
                    name="paramsMergeType"
                    label="主机及变量合并方式"
                    options={HOST_MERGE_TYPE}
                    allowClear={false}
                    initialValue="OVERWRITE"
                    fieldProps={{
                      onChange: () => {
                        formRef.current?.setFieldValue('yamlList', []);
                      },
                    }}
                  />
                </RKCol>
                <RKCol>
                  <ProFormTextArea
                    name="description"
                    label="描述"
                    fieldProps={{
                      autoSize: { minRows: 1, maxRows: 3 },
                    }}
                  />
                </RKCol>
              </Row>
              {/* <RKCol lg={24} md={24} sm={24}> */}
              <ProForm.Item
                name="params"
                label="变量 "
                validateTrigger={false}
                rules={[
                  () => ({
                    validator() {
                      // @ts-ignore
                      const errors = editorRef.current?.getModelMarkers();
                      if (!errors.length) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('请检查yaml格式'));
                    },
                  }),
                ]}
              >
                <YAMLEditor
                  onError={(editor) => {
                    editorRef.current = editor;
                  }}
                />
              </ProForm.Item>
              {/* </RKCol> */}
            </div>
            <div className={cx('flow-content')}>
              <Flow
                onMount={(graph) => {
                  graphRef.current = graph;
                }}
                onNodeDBLClick={(node) => {
                  setDrawerVisit(true);
                  const data = node.getData();
                  drawerForm.setFieldsValue({
                    ...data,
                    nodeId: node.id,
                  });
                }}
              />
            </div>
            <ProTable<API.TaskModuleChangeHistoryVO>
              {...defaultTableConfig}
              pagination={{
                defaultPageSize: 5,
                showSizeChanger: true,
              }}
              search={false}
              dataSource={historyList || []}
              columns={columns}
              headerTitle="变更历史"
              size="small"
              options={{
                density: false,
                setting: false,
                reload: refresh,
              }}
            />
            <BaseContext.Provider value={{ atomicTask: taskList, atomicTaskLoading: taskLoading }}>
              <DrawerForm
                width={560}
                open={drawerVisit}
                title="添加作业"
                form={drawerForm}
                autoFocusFirstInput
                drawerProps={{
                  mask: true,
                  destroyOnClose: true,
                }}
                className={styles.drawer}
                onOpenChange={setDrawerVisit}
                submitTimeout={2000}
                onFinish={async (values) => {
                  const node = graphRef.current?.getCellById(values.nodeId);
                  // 获取输出边
                  if (node) {
                    node?.setData(values);
                    const edge = graphRef.current?.getConnectedEdges(node, {
                      outgoing: true,
                    });

                    edge?.forEach((item) => {
                      item.setLabels([
                        {
                          attrs: {
                            label: {
                              text: RUN_MODE.find((item) => item.value === values.runMode)?.label,
                            },
                          },
                        },
                      ]);
                    });
                  }

                  return true;
                }}
              >
                <JobForm />
              </DrawerForm>
            </BaseContext.Provider>
          </div>
        </Spin>
      </PageContainer>
    </ProForm>
  );
};

export default JobFlowDetail;
