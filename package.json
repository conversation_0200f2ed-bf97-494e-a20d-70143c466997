{"name": "union-monitor-platform", "version": "6.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "clean-cache": "rimraf node_modules/.cache", "deploy": "npm run build && npm run gh-pages", "dev": "npm run clean-cache && npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "husky install", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js  && npm run tsc", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/charts": "1.4.2", "@ant-design/icons": "^5.0.1", "@ant-design/pro-components": "2.6.5", "@ant-design/pro-field": "^2.2.9", "@ant-design/use-emotion-css": "1.0.4", "@antv/x6": "^2.15.6", "@antv/x6-plugin-dnd": "^2.1.1", "@antv/x6-plugin-keyboard": "^2.2.1", "@antv/x6-plugin-minimap": "^2.0.6", "@antv/x6-plugin-scroller": "^2.0.10", "@antv/x6-plugin-selection": "^2.2.1", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-react-shape": "^2.2.2", "@types/crypto-js": "^4.2.2", "@types/insert-css": "^2.0.1", "@umijs/route-utils": "^2.1.3", "ahooks": "^3.7.4", "antd": "5.5.2", "caniuse-lite": "^1.0.30001614", "cherry-markdown": "0.8.40", "classnames": "^2.3.2", "crypto-js": "^4.2.0", "file-saver": "^2.0.5", "immer": "^10.0.2", "insert-css": "^2.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "monaco-editor": "0.35.0", "monaco-editor-webpack-plugin": "7.0.1", "monaco-yaml": "4.0.4", "omit.js": "^2.0.2", "rc-menu": "^9.6.4", "rc-util": "^5.24.4", "react": "^18.0.0", "react-dev-inspector": "^1.8.1", "react-dom": "^18.0.0", "react-helmet-async": "1.0.9", "react-iframe": "^1.8.5", "react-monaco-editor": "0.51.0", "uuid": "^9.0.0"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.0", "@commitlint/cli": "^17.4.2", "@commitlint/config-conventional": "^17.4.2", "@testing-library/react": "^13.4.0", "@types/classnames": "^2.3.1", "@types/express": "^4.17.14", "@types/history": "^4.7.11", "@types/jest": "^29.2.1", "@types/lodash": "^4.14.186", "@types/react": "^18.0.27", "@types/react-dom": "^17.0.0", "@types/react-helmet": "^6.1.6", "@umijs/lint": "^4.0.34", "@umijs/max": "^4.0.33", "cross-env": "^7.0.3", "eslint": "^8.33.0", "express": "^4.18.2", "gh-pages": "^3.2.0", "husky": "^7.0.4", "jest": "^29.2.2", "jest-environment-jsdom": "^29.2.2", "lint-staged": "^13.1.0", "mockjs": "^1.1.0", "prettier": "^2.8.3", "swagger-ui-dist": "^4.14.2", "ts-node": "^10.9.1", "typescript": "^4.8.4", "umi-presets-pro": "^2.0.0"}, "engines": {"node": ">=12.0.0"}}