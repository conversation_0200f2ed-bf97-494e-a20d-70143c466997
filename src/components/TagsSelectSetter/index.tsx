import { ProFieldFCRenderProps } from '@ant-design/pro-components';
import { Select, SelectProps } from 'antd';
import { useMemo } from 'react';

const TagsSelectSetter: React.FC<ProFieldFCRenderProps> = (props) => {
  const { tags = [] } = props.fieldProps;
  const options = useMemo(() => {
    return tags.map((tag: Record<string, any>) => ({
      label: tag.title,
      title: tag.title,
      options: tag.children,
    }));
  }, [tags]);
  const onchange: SelectProps['onChange'] = (_, options) => {
    const val =
      options?.map((item: Record<string, any>) => ({
        tag: item.tag,
        value: item.value,
        operator: 1,
      })) || [];
    props?.fieldProps?.onChange(val);
  };

  return (
    <Select
      options={options}
      mode="multiple"
      value={props?.fieldProps?.value}
      onChange={onchange}
      allowClear
    />
  );
};

export default TagsSelectSetter;
