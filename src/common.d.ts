declare type AntTableParams = {
  pageSize?: number;
  current?: number;
  keyword?: string;
};

declare type BaseResponse = {
  code: number;
  data: Record<string, any>;
  message: string;
};

declare type AuthProps = {
  isAdmin: boolean;
};

declare type BaseListRequest = {
  pageSize?: number;
  pageNum?: number;
  keyword?: string;
};

declare module CommonAPI {
  export interface Host {
    id: number;
    hostName: string;
    hostUserName: string;
    hostPassword: string;
    proType: number;
    pubKey?: any;
    priKey?: any;
    hostStatus: string;
    description?: any;
  }

  export interface Template {
    id: number;
    temName: string;
    temDescription?: any;
    proName: string;
    proVersion: string;
    proPlayBook: string;
    proPath: string;
    yamlChange?: any;
  }
  export interface UserGroup {
    id: number;
    cliqueName: string;
    cliqueDescription?: any;
  }

  export interface HostGroup {
    id: number;
    groupName: string;
    description?: any;
  }
  export interface User extends API.User {
    id: number;
  }
  export interface UserInfo {
    hostList: HostList[];
    templetList: Template[];
    groupList: HostGroup[];
    user: API.User;
  }

  export interface UserGroupInfo {
    clique: API.Clique;
    /** 对象群组集合 */
    groupList?: HostGroup[];
    /** 主机集合 */
    hostList?: HostList[];
    /** 监控模板集合 */
    templetList?: Template[];
    /** 用户集合 */
    userList?: API.User[];
  }

  export interface Task {
    id: number;
    tasksId: number;
    tasksName: string;
    missionName: string;
    tasksDescription?: any;
    expirationDate: number;
    timeUnit: string;
    deadline?: any;
    auditTime?: any;
    status: string;
    applicationUserId: number;
    applicationUserName: string;
    auditUserId: number;
    taskRange: string;
  }

  export interface ExecutionHistory {
    id: number;
    startTime: string;
    endTime: string;
    missionId: number;
    missionName?: any;
    execUserId: number;
    execUserName: string;
    execStatus: boolean;
    execInfo: string;
    taskRange: string;
  }

  export interface ExecutionDetails {
    template: string;
    applicationUserName: string;
    execStatus: string;
    auditUserName: string;
    missionId: number;
    hostList: HostList[];
    yamlChange?: any;
    execUserName: string;
    startTime: string;
    endTime: string;
    execInfo: string;
    playbook: string;
  }
}
