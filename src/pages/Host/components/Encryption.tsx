import HeadTitle from '@/components/HeadTitle';
import RKCol from '@/components/RKCol';
import { TLS_ACCEPT, TLS_CONNECT } from '@/enums';
import { requiredRule } from '@/utils/setting';
import { ProFormDependency, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { Row } from 'antd';

export default function Encryption() {
  return (
    <>
      <HeadTitle>加密</HeadTitle>
      <Row gutter={24}>
        <RKCol>
          <ProFormSelect
            label="连接到主机"
            name="tls_accept"
            options={TLS_ACCEPT}
            fieldProps={{
              allowClear: false,
            }}
          />
        </RKCol>
        <RKCol>
          <ProFormSelect label="从主机连接" name="tls_connect" options={TLS_CONNECT} />
        </RKCol>
        <ProFormDependency name={['tls_accept', 'tls_connect']}>
          {({ tls_accept, tls_connect }) => {
            return (
              // 如果 tls_connect 或 tls_accept启用了PSK ，则需要
              ([2].includes(tls_accept) || tls_connect.includes(2)) && (
                <>
                  <RKCol>
                    <ProFormText
                      label="共享密钥一致性"
                      name="tls_psk_identity"
                      rules={[requiredRule]}
                    />
                  </RKCol>
                  <RKCol>
                    <ProFormText label="共享密钥一致性" name="tls_psk" rules={[requiredRule]} />
                  </RKCol>
                </>
              )
            );
          }}
        </ProFormDependency>
        <ProFormDependency name={['tls_accept', 'tls_connect']}>
          {({ tls_accept, tls_connect }) => {
            return (
              // 如果 tls_connect 或 tls_accept选择了证书
              ([4].includes(tls_accept) || tls_connect?.includes(4)) && (
                <>
                  <RKCol>
                    <ProFormText label="发行者" name="tls_subject" />
                  </RKCol>
                  <RKCol>
                    <ProFormText label="主体" name="tls_issuer" />
                  </RKCol>
                </>
              )
            );
          }}
        </ProFormDependency>
      </Row>
    </>
  );
}
