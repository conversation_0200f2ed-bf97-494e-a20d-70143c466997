import withStorageToUrl from '@/hoc/withSyncToUrl';
import { taskModuleOrchestrationPage } from '@/services/http/taskModuleOrchestration';
import { queryRkPagingTable, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, Space } from 'antd';
import React, { useRef } from 'react';
import ExecuteHistoryDrawerForm from './components/ExecuteHistoryDrawerForm';
import ExecuteTask from './components/ExecuteTask';

const RapidDeployment: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const columns: ProColumns[] = [
    {
      title: '规则名称',
      dataIndex: 'name',
      fieldProps: {
        autoComplete: 'off',
      },
      width: 300,
      initialValue: queryParams.get('name'),
      render: (dom, record) => {
        return (
          <a
            onClick={() => {
              history.push(`/deployment/rapid-deployment/edit/${record.id}`);
            }}
          >
            {record.name}
          </a>
        );
      },
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (_, entity) => {
        return (
          <Space>
            <ExecuteTask id={entity.id} name={entity.name} />
            <ExecuteHistoryDrawerForm id={entity.id} name={entity.name} />
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable
        {...defaultTableConfig}
        search={SearchOptionRender}
        onSubmit={syncToUrl}
        actionRef={tableRef}
        columns={columns}
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              onClick={() => history.push('/deployment/rapid-deployment/add')}
            >
              新建部署规则
            </Button>,
          ],
        }}
        headerTitle="部署规则"
        request={async (params) => queryRkPagingTable(params, taskModuleOrchestrationPage)}
      />
    </PageContainer>
  );
});

export default RapidDeployment;
