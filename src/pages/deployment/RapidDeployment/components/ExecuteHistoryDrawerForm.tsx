import { EXECUTE_STATUS } from '@/enums';
import { taskModuleOrchestrationResultPage } from '@/services/http/taskModuleOrchestration';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { DrawerForm, ProColumns, ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button } from 'antd';
import { FC, memo } from 'react';

const ExecuteHistoryDrawerForm: FC<{
  id: string;
  name: string;
}> = ({ id, name }) => {
  const columns: ProColumns<API.TaskModuleOrchestrationExecutePageVO>[] = [
    {
      title: '执行时间',
      dataIndex: 'executeTime',
      width: 180,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueEnum: option2enum(EXECUTE_STATUS),
    },
    {
      dataIndex: 'hostList',
      width: 300,
      title: '执行主机',
      renderText: (text) => {
        const names = text?.map((item: API.HostPageVO) => item.host);
        return names.join(',');
      },
    },
    {
      dataIndex: 'taskModuleExecuteList',
      title: '监控模块',
      width: 200,
      renderText: (text) => {
        const names = text?.map((item: API.TaskModuleExecutePageVO) => item.taskModule?.name);
        return names.join(',');
      },
    },

    {
      title: '操作',
      fixed: 'right',
      width: 100,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (_, entity) => {
        return (
          <Button
            type="link"
            onClick={() =>
              history.push(`/deployment/rapid-deployment/execute-details/${entity.id}`)
            }
          >
            详情
          </Button>
        );
      },
    },
  ];
  return (
    <DrawerForm
      width={800}
      title={`${name}-执行历史`}
      trigger={<Button type="link">执行历史</Button>}
      drawerProps={{
        destroyOnClose: true,
      }}
      submitter={false}
    >
      <ProTable<API.TaskModuleChangeHistoryVO>
        {...defaultTableConfig}
        className="inner-table"
        search={false}
        columns={columns}
        scroll={{
          x: '100%',
        }}
        // @ts-ignore
        request={async ({ current, pageSize }) => {
          const res = await taskModuleOrchestrationResultPage(
            {
              id,
            },
            {
              page: {
                page: current,
                size: pageSize,
              },
            },
          );
          const result = res?.data;
          return {
            data: result?.data || [],
            success: true,
            total: result?.total || 0,
          };
        }}
      />
    </DrawerForm>
  );
};

export default memo(ExecuteHistoryDrawerForm);
