import RKCol from '@/components/RKCol';
import { useHostGroupList } from '@/hooks/useHostGroupList';
import { useTemplateList } from '@/hooks/useTemplateList';
import { zabbix } from '@/services/zabbix';
import { queryOptions } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  ProFormDependency,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Row } from 'antd';

export default function Base() {
  // 对象群组
  const { hostGroupList, loading: hostGroupLoading } = useHostGroupList();
  // 模版
  const { templateList, loading: templateLoading } = useTemplateList();
  return (
    <Row gutter={24}>
      <RKCol>
        <ProFormText label="主机名称" name="host" rules={[requiredRule]} />
      </RKCol>
      <ProFormDependency name={['host']}>
        {({ host }) => {
          return (
            <RKCol>
              <ProFormText label="可见的名称" placeholder={host} name="name" />
            </RKCol>
          );
        }}
      </ProFormDependency>
      <RKCol>
        <ProFormSelect
          label="模版"
          name="parentTemplates"
          fieldProps={{
            mode: 'multiple',
            showSearch: true,
            fieldNames: {
              label: 'name',
              value: 'templateid',
            },
            options: templateList,
            loading: templateLoading,
          }}
          convertValue={(value = []) => value.map((item: any) => item?.templateid)}
          getValueFromEvent={(value = []) => value.map((item: string) => ({ templateid: item }))}
          transform={(value: RK_API.Template[], namePath) => ({
            [namePath]: undefined,
            templates: value.map(({ templateid }) => ({ templateid })),
          })}
        />
      </RKCol>
      <RKCol>
        <ProFormSelect
          label="群组"
          name="groups"
          fieldProps={{
            mode: 'multiple',
            showSearch: true,
            fieldNames: {
              label: 'name',
              value: 'groupid',
            },
            options: hostGroupList,
            loading: hostGroupLoading,
          }}
          rules={[requiredRule]}
          convertValue={(value = []) => value.map((item: any) => item?.groupid)}
          getValueFromEvent={(value = []) => value.map((item: string) => ({ groupid: item }))}
          transform={(value: RK_API.HostGroup[], namePath) => ({
            [namePath]: value.map(({ groupid }) => ({ groupid })),
          })}
        />
      </RKCol>

      <RKCol>
        <ProFormSelect
          label="由agent代理程序监测"
          name="proxy_hostid"
          fieldProps={{
            fieldNames: {
              label: 'host',
              value: 'proxyid',
            },
          }}
          request={async () => {
            const defaultArr = [
              {
                proxyid: '0',
                host: '无agent代理程序',
              },
            ];
            const res = await queryOptions(
              {
                method: 'proxy.get',
              },
              zabbix,
            );

            return [...defaultArr, ...res];
          }}
        />
      </RKCol>
      <RKCol>
        <ProFormTextArea
          label="描述"
          name="description"
          fieldProps={{
            autoSize: { minRows: 1, maxRows: 6 },
          }}
        />
      </RKCol>
      <RKCol>
        <ProFormSwitch
          label="启用"
          name="status"
          getValueFromEvent={(val) => (val ? '0' : '1')}
          getValueProps={(value) => ({ checked: value === '0' })}
        />
      </RKCol>
    </Row>
  );
}
