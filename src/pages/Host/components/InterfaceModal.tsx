import { AVAILABLE, INTERFACE_TYPE } from '@/enums';
import { option2enum } from '@/utils';
import {
  ModalForm,
  ModalFormProps,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import React, { useRef } from 'react';

const columns: ProColumns<RK_API.Interface>[] = [
  {
    title: '接口',
    width: 150,
    dataIndex: 'ip',
    ellipsis: true,
    renderText(text, record) {
      return `${text}:${record?.port}`;
    },
  },
  {
    title: '状态',
    width: 80,
    dataIndex: 'available',
    valueEnum: option2enum(AVAILABLE),
  },
  {
    title: '接口类型',
    dataIndex: 'type',
    width: 80,
    valueEnum: option2enum(INTERFACE_TYPE),
  },
  {
    title: '错误',
    dataIndex: 'error',
    ellipsis: true,
  },
];

const InterfaceModal: React.FC<ModalFormProps> = ({ open, onOpenChange, initialValues }) => {
  const formRef = useRef<ProFormInstance>();

  return (
    <ModalForm
      size="small"
      width={500}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      modalProps={{
        destroyOnClose: true,
        centered: true,
        closable: false,
      }}
      submitter={false}
    >
      <ProTable<RK_API.Interface>
        className="inner-table"
        search={false}
        options={false}
        pagination={false}
        rowKey="interfaceid"
        columns={columns}
        dataSource={initialValues?.interfaces || []}
      />
    </ModalForm>
  );
};

export default InterfaceModal;
