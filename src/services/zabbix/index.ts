import { request } from '@umijs/max';
const settingStr = localStorage.getItem('RKLINK_SETTINGS') || '{}';

const setting = JSON.parse(settingStr);

export async function zabbix(body: Record<string, any>, options?: { [key: string]: any }) {
  const { method = '', ...rest } = body;
  return request<Record<string, any>>('/api/v1/rump/api/common', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      method,
      params: {
        output: 'extend',
        ...rest,
      },
    },
    ...(options || {}),
  });
}

export async function zabbixList(body: Record<string, any>, options?: { [key: string]: any }) {
  const { method = '', ...rest } = body;
  return request<Record<string, any>>('/api/v1/rump/api/common', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      method,
      params: {
        output: 'extend',
        ...rest,
        limit: Number(setting.search_limit),
      },
    },
    ...(options || {}),
  });
}

export async function zabbixPost(body: Record<string, any>, options?: { [key: string]: any }) {
  const { method = '', ...rest } = body;
  return request<Record<string, any>>('/api/v1/rump/api/common', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      method,
      params: rest,
    },
    ...(options || {}),
  });
}
export async function zabbixDelete(body: Record<string, any>, options?: { [key: string]: any }) {
  const { method = '', ids = [] } = body;
  return request<Record<string, any>>('/api/v1/rump/api/common', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      method,
      params: ids,
    },
    ...(options || {}),
  });
}

export async function zabbixExport(body: Record<string, any>, options?: { [key: string]: any }) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { method = 'configuration.export', fileName = '', ...rest } = body;
  return request<Record<string, any>>('/api/v1/rump/api/common', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      method,
      params: rest,
    },
    ...(options || {}),
  });
}
