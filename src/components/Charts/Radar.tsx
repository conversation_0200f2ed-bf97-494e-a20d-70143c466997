import '@/utils/theme';
import { Radar, RadarConfig } from '@ant-design/charts';

export type RadarProps = RadarConfig;
const RkRadar: React.FC<RadarProps> = (props) => {
  const config: RadarConfig = {
    xAxis: {
      line: null,
      tickLine: null,
      grid: {
        line: {
          style: {
            lineWidth: 1,
          },
        },
      },
    },
    yAxis: {
      line: null,
      tickLine: null,
      grid: {
        line: {
          type: 'line',
          style: {
            lineDash: null,
          },
        },
      },
    },

    lineStyle: {
      lineWidth: 1,
    },
    // 开启面积
    area: {},
    // 开启辅助点
    point: {
      size: 2,
    },
    legend: {
      layout: 'vertical',
      position: 'right',
    },
    ...props,
  };

  return <Radar {...config} />;
};

export default RkRadar;
