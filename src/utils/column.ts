import { zabbix } from '@/services/zabbix';
import { queryOptions } from '.';

export const hostGroups: any = {
  title: '主机群组',
  dataIndex: 'groupids',
  hideInTable: true,
  valueType: 'select',
  fieldProps: {
    mode: 'multiple',
    fieldNames: {
      label: 'name',
      value: 'groupid',
    },
    showSearch: true,
  },
  request: () =>
    queryOptions(
      {
        method: 'hostgroup.get',
        sortfield: 'name',
      },
      zabbix,
    ),
};
export const template: any = {
  title: '模版',
  dataIndex: 'templateids',
  hideInTable: true,
  valueType: 'select',
  fieldProps: {
    mode: 'multiple',
    fieldNames: {
      label: 'name',
      value: 'templateid',
    },
    showSearch: true,
  },
  request: () =>
    queryOptions(
      {
        method: 'template.get',
        sortfield: 'name',
      },
      zabbix,
    ),
};

export const host: any = {
  title: '主机',
  dataIndex: 'hostids',
  hideInTable: true,
  valueType: 'select',
  fieldProps: {
    mode: 'multiple',
    fieldNames: {
      label: 'name',
      value: 'hostid',
    },
    showSearch: true,
  },
  request: () =>
    queryOptions(
      {
        method: 'host.get',
        sortfield: 'name',
        output: ['hostid', 'name'],
      },
      zabbix,
    ),
};
