// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as alarm from './alarm';
import * as api from './api';
import * as atomicTask from './atomicTask';
import * as countView from './countView';
import * as externalMonitoring from './externalMonitoring';
import * as globalConfig from './globalConfig';
import * as host from './host';
import * as hostGroup from './hostGroup';
import * as hostSshKey from './hostSshKey';
import * as monitoringObject from './monitoringObject';
import * as panoramic from './panoramic';
import * as taskModule from './taskModule';
import * as taskModuleOrchestration from './taskModuleOrchestration';
import * as template from './template';
import * as topology from './topology';
import * as user from './user';
import * as userGroup from './userGroup';
export default {
  topology,
  taskModule,
  panoramic,
  monitoringObject,
  host,
  hostSshKey,
  hostGroup,
  globalConfig,
  externalMonitoring,
  user,
  userGroup,
  template,
  taskModuleOrchestration,
  atomicTask,
  api,
  alarm,
  countView,
};
