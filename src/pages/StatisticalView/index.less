.setting-container {
  position: absolute;
  right: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  .icon {
    display: inline-block;
    width: 36px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    cursor: pointer;
    // opacity: 0;
    // transition: opacity 1s ease;
  }
  // &:hover {
  //   .icon {
  //     opacity: 1;
  //   }
  // }
}
.container {
  padding-top: 10px;
  .icon {
    margin-right: 8px;
  }
}
.grid-container {
  display: flex;
  flex-wrap: wrap;
  grid-auto-flow: dense;
  gap: calc(1vw + 1vh); /* 自适应间距，可根据屏幕大小调整 */
  justify-content: center; /* 居中对齐 */
  padding: 0 calc(1vw + 1vh);
  .card {
    width: max-content;
    min-width: 250px;
    max-width: 300px;
  }
}

.card {
  height: 100%;
  overflow: hidden;
  background: radial-gradient(
      178.94% 106.41% at 26.42% 106.41%,
      #fff 0%,
      rgba(255, 255, 255, 0) 71.88%
    )
    #ffffff;
  // box-shadow: 0 5px 15px 0 #00000026;
  box-shadow: rgba(0, 0, 0, 0.15) 40px 50px 25px -40px, rgba(0, 0, 0, 0.1) 0px 25px 25px -5px;

  &::before {
    position: absolute;
    width: 4px;
    height: 100%;
    background-image: linear-gradient(180deg, rgb(0, 183, 255), rgb(255, 48, 255));
    content: '';
  }
  &::after {
    position: absolute;
    width: 4px;
    height: 20%;
    background-clip: content-box;
    border-radius: 6px;
    filter: blur(0.7px);
    animation: steam 4s linear infinite 1s;
    content: '';
  }
  @keyframes steam {
    0% {
      top: 0;
      height: 20%;
      background-color: rgb(255, 48, 255);
    }

    50% {
      top: 40%;
      height: 20%;
      background-image: linear-gradient(180deg rgb(255, 48, 255), rgb(0, 183, 255));
    }

    100% {
      top: 100%;
      height: 20%;
      background-color: rgb(0, 183, 255);
    }
  }
}
