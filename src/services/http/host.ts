// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 修改 PUT /api/v1/rump/host/${param0}/update */
export async function hostUpdateById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.hostUpdateByIdParams,
  body: API.HostCURequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/api/v1/rump/host/${param0}/update`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 批量测试连接 POST /api/v1/rump/host/batch/testConnection */
export async function hostBatchTestConnection(
  body: API.BatchIdRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultListHostBatchTestConnectionVO>(
    '/api/v1/rump/host/batch/testConnection',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 根据id查询 GET /api/v1/rump/host/byId/${param0} */
export async function hostFindById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.hostFindByIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultHostVO>(`/api/v1/rump/host/byId/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 创建 POST /api/v1/rump/host/create */
export async function hostCreate(body: API.HostCURequest, options?: { [key: string]: any }) {
  return request<any>('/api/v1/rump/host/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除 POST /api/v1/rump/host/delete */
export async function hostDeleteByIds(body: API.BatchIdRequest, options?: { [key: string]: any }) {
  return request<any>('/api/v1/rump/host/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 部署Agent POST /api/v1/rump/host/deploy/agent */
export async function hostDeployAgent(
  body: API.HostDeployAgentRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/host/deploy/agent', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 部署密钥 POST /api/v1/rump/host/deploy/key */
export async function hostDeployKey(
  body: API.HostDeployKeyRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/host/deploy/key', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页 POST /api/v1/rump/host/page */
export async function hostPage(body: API.HostPageRequest, options?: { [key: string]: any }) {
  return request<API.ResultPageVOHostPageVO>('/api/v1/rump/host/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 测试连接 POST /api/v1/rump/host/testConnection */
export async function hostTestConnection(
  body: API.HostTestConnectionRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/host/testConnection', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
