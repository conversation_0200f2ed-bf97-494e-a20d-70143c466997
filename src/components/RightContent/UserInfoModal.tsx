import { requiredRule } from '@/utils/setting';
import { phoneReg } from '@/utils/validator';
import {
  ModalForm,
  ModalFormProps,
  ProForm,
  ProFormInstance,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import React, { useRef } from 'react';

const HostModalForm: React.FC<ModalFormProps & { userId?: number }> = ({
  open,
  onOpenChange,
  // userId,
}) => {
  const formRef = useRef<ProFormInstance>();
  // const { setInitialState } = useModel('@@initialState');

  return (
    <ModalForm
      width="736px"
      formRef={formRef}
      title="修改用户信息"
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async () => {
        // const msg = await modifyUserUsingPUT({ user: value });
        // const success = msg.code === 100;
        // if (success) {
        //   message.success('操作成功！');
        //   setInitialState((s = {}) => ({
        //     ...s,
        //     currentUser: { ...(s.currentUser || {}), ...value },
        //   }));
        // }
        // return success;
      }}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        centered: true,
      }}
      request={async () => {
        // if (userId) {
        //   const { data } = await getUserInfoUsingGET({ id: userId });
        //   return (data?.user as API.User) || {};
        // }
        return {};
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText width="md" name="id" />
      </div>
      <ProForm.Group>
        <ProFormText width="md" name="userName" disabled label="用户名" placeholder="请输入" />
        <ProFormText
          width="md"
          name="realName"
          label="姓名"
          placeholder="请输入"
          rules={[requiredRule]}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText
          width="md"
          name="email"
          label="电子邮件"
          placeholder="请输入"
          rules={[
            requiredRule,
            {
              type: 'email',
              message: '电子邮件格式错误',
            },
          ]}
        />
        <ProFormText
          width="md"
          name="phone"
          label="手机号"
          placeholder="请输入"
          rules={[
            requiredRule,
            {
              pattern: phoneReg,
              message: '手机号格式错误',
            },
          ]}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormTextArea
          width="md"
          name="description"
          label="描述"
          fieldProps={{ autoSize: { minRows: 3, maxRows: 3 } }}
        />
      </ProForm.Group>
    </ModalForm>
  );
};

export default HostModalForm;
