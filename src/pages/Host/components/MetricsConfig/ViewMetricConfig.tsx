import { HOST_STATUS, MONITOR_DATA_TYPE, MONITOR_TYPE } from '@/enums';
import { zabbix } from '@/services/zabbix';
import { option2enum } from '@/utils';
import { PageContainer, ProDescriptions } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Card } from 'antd';
import React from 'react';

const ViewMetricConfig: React.FC = () => {
  const { id } = useParams();

  return (
    <PageContainer header={{ title: false }}>
      <Card>
        <ProDescriptions
          column={3}
          layout="vertical"
          request={async () => {
            const res = await zabbix({
              itemids: [id],
              selectTags: 'extend',
              method: 'item.get',
            });
            console.log(res);

            if (res?.code === 200) {
              return {
                data: res?.data?.at(0),
                success: true,
              };
            }
            return {
              success: false,
            };
          }}
        >
          <ProDescriptions.Item label="指标名称" dataIndex="name" />
          <ProDescriptions.Item
            label="类型"
            dataIndex="type"
            valueEnum={option2enum(MONITOR_TYPE)}
          />
          <ProDescriptions.Item label="键值" dataIndex="key_" />
          <ProDescriptions.Item
            label="数据类型"
            dataIndex="value_type"
            valueEnum={option2enum(MONITOR_DATA_TYPE)}
          />
          <ProDescriptions.Item label="单位" dataIndex="units" />
          <ProDescriptions.Item
            label="标签"
            dataIndex="tags"
            render={(_, entity) => {
              const arr: string[] = entity?.tags?.map(
                (item: RK_API.TemplateTag) => `${item.tag}:${item.value}`,
              );
              return (
                <div>
                  {arr?.map((item, index) => (
                    <div key={index}>{item}</div>
                  ))}
                </div>
              );
            }}
          />

          <ProDescriptions.Item
            label="启用"
            valueEnum={option2enum(HOST_STATUS)}
            dataIndex="status"
          />
          <ProDescriptions.Item label="更新间隔" dataIndex="delay" />
          <ProDescriptions.Item label="历史数据保留时长" dataIndex="history" />

          <ProDescriptions.Item label="趋势存储时间" dataIndex="trends" />
          <ProDescriptions.Item label="描述" span={2} dataIndex="description" />
        </ProDescriptions>
      </Card>
    </PageContainer>
  );
};

export default ViewMetricConfig;
