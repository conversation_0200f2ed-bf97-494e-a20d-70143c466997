import TagsSelectSetter from '@/components/TagsSelectSetter';
import { ProRenderFieldPropsType } from '@ant-design/pro-components';
import ConditionSetter from './components/ConditionSetter';

/**
 * 自定义 valueMap
 */
export const valueTypeMap: Record<string, ProRenderFieldPropsType> = {
  conditionSetter: {
    renderFormItem(text, props) {
      return <ConditionSetter value={text} {...props} />;
    },
  },
  tagsSelectSetter: {
    renderFormItem(_, props) {
      return <TagsSelectSetter {...props} />;
    },
  },
};
