// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 修改 PUT /api/v1/rump/host/ssh/key/${param0}/update */
export async function hostSshKeyUpdateById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.hostSshKeyUpdateByIdParams,
  body: API.HostSshKeyCURequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<any>(`/api/v1/rump/host/ssh/key/${param0}/update`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 根据id查询 GET /api/v1/rump/host/ssh/key/byId/${param0} */
export async function hostSshKeyFindById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.hostSshKeyFindByIdParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultHostSshKeyVO>(`/api/v1/rump/host/ssh/key/byId/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 创建 POST /api/v1/rump/host/ssh/key/create */
export async function hostSshKeyCreate(
  body: API.HostSshKeyCURequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/host/ssh/key/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除 POST /api/v1/rump/host/ssh/key/delete */
export async function hostSshKeyDeleteByIds(
  body: API.BatchIdRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/rump/host/ssh/key/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页 POST /api/v1/rump/host/ssh/key/page */
export async function hostSshKeyPage(
  body: API.HostSshKeyPageRequest,
  options?: { [key: string]: any },
) {
  return request<API.ResultPageVOHostSshKeyPageVO>('/api/v1/rump/host/ssh/key/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
