import { Pic<PERSON>enterOutlined, ZoomInOutlined, ZoomOutOutlined } from '@ant-design/icons';
import { Space, Tooltip } from 'antd';
import React from 'react';
import styles from './index.less';

interface ToolBarProps {
  onClick?: (key: string) => void;
}

const FlowToolbar: React.FC<ToolBarProps> = ({ onClick }) => {
  return (
    <div className={styles.toolbar}>
      <Space>
        <Tooltip title="居中">
          <PicCenterOutlined
            className={styles.icon}
            onClick={() => {
              onClick?.('center');
            }}
          />
        </Tooltip>
        <Tooltip title="放大">
          <ZoomInOutlined
            className={styles.icon}
            onClick={() => {
              onClick?.('zoomIn');
            }}
          />
        </Tooltip>
        <Tooltip title="缩小">
          <ZoomOutOutlined
            className={styles.icon}
            onClick={() => {
              onClick?.('zoomOut');
            }}
          />
        </Tooltip>
      </Space>
    </div>
  );
};

export default FlowToolbar;
