import { Settings as LayoutSettings } from '@ant-design/pro-components';

// microservice 暂不支持单独访问，请配合 console web 项目启动

const APP_MODE = process.env.APP_MODE || 'microservice'; // microservice 微服务版本 、 standalone 单机版

export const isMicroservice = APP_MODE === 'microservice';

/**
 * @name layout默认配置
 */
const Settings: LayoutSettings & {
  pwa?: boolean;
  logo?: string;
  target?: string;
  API_ADDRESS?: string; // openApi地址
  REQUEST_ADDRESS?: string; // 请求地址
  BASE_URL?: string;
  APP_MODE: string;
  TOKEN_KEY: string;
} = {
  navTheme: 'light',
  layout: 'mix',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  colorPrimary: '#13c2c2',
  title: '融科小E',
  pwa: false,
  logo: '/images/logo.png',
  iconfontUrl: '',
  menu: {
    autoClose: false,
    locale: false,
    collapsedShowTitle: false,
    type: 'sub',
  },
  API_ADDRESS: isMicroservice ? 'http://***************:32368' : 'http://***************:30330', // 单机版 30330 微服务 32368
  REQUEST_ADDRESS: isMicroservice ? 'http://***************:32333' : 'http://***************:30330', // 单机版 30330 微服务 32333
  APP_MODE,
  TOKEN_KEY: isMicroservice ? 'RKLINK_CONSOLE_TOKEN' : 'RKLINK_RUMP',
};

export default Settings;
