import IconFont from '@/components/Icon';
import { Pic<PERSON>enterOutlined, ZoomInOutlined, ZoomOutOutlined } from '@ant-design/icons';
import { Button, Space, Tooltip } from 'antd';
import React from 'react';
import styles from './index.less';

interface ToolBarProps {
  onClick?: (key: string) => void;
}

const EditorToolbar: React.FC<ToolBarProps> = ({ onClick }) => {
  return (
    <div className={styles.toolbar}>
      <Space>
        <Tooltip title="居中">
          <PicCenterOutlined
            onClick={() => {
              onClick?.('center');
            }}
          />
        </Tooltip>
        <Tooltip title="放大">
          <ZoomInOutlined
            onClick={() => {
              onClick?.('zoomIn');
            }}
          />
        </Tooltip>
        <Tooltip title="缩小">
          <ZoomOutOutlined
            onClick={() => {
              onClick?.('zoomOut');
            }}
          />
        </Tooltip>
        <Tooltip title="缩放原始比例">
          <IconFont
            type="icon-actual-size"
            onClick={() => {
              onClick?.('actualSize');
            }}
          />
        </Tooltip>
        {/* <Divider
          type="vertical"
          style={{
            borderColor: '#d9d9d9',
          }}
        />
        <EdgeSelector /> */}
      </Space>
      <Space>
        <Button
          onClick={() => {
            onClick?.('clean');
          }}
        >
          清空画布
        </Button>
        <Button
          type="primary"
          onClick={() => {
            onClick?.('save');
          }}
        >
          保存
        </Button>
      </Space>
    </div>
  );
};

export default EditorToolbar;
