import { updateGroupIcon, updateObjectIcon } from '@/services/http/monitoringObject';
import { requiredRule } from '@/utils/setting';
import { ModalForm, ModalFormProps, ProFormItem, ProFormText } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import { FC } from 'react';
import IconSelector from './IconSelector';

const IconModalForm: FC<
  ModalFormProps & {
    onSuccess?: () => void;
  }
> = ({ onSuccess, ...props }) => {
  // 更新图标
  const { run: updateHostGroupIcon } = useRequest(
    (groupId, iconProps) => updateGroupIcon({ groupId }, iconProps),
    {
      manual: true,
      formatResult: (res) => res,
    },
  );
  const { run: updateHostIcon } = useRequest(
    (objectId, iconProps) => updateObjectIcon({ objectId }, iconProps),
    {
      manual: true,
      formatResult: (res) => res,
    },
  );

  return (
    <ModalForm
      width={520}
      {...props}
      modalProps={{
        destroyOnClose: true,
      }}
      onFinish={async (values) => {
        const res = values.groupId
          ? await updateHostGroupIcon(values.groupId, values.icon)
          : await updateHostIcon(values.hostId, values.icon);
        const success = res?.data;
        if (success) {
          message.success('操作成功！');
          onSuccess?.();
        }
        return success;
      }}
    >
      <div className="rk-none">
        <ProFormText name="groupId" />
        <ProFormText name="hostId" />
      </div>
      <ProFormText label="图标类型" name={['icon', 'name']} width="lg" rules={[requiredRule]} />
      <ProFormItem label="图标选择" required rules={[requiredRule]} name={['icon', 'icon']}>
        <IconSelector />
      </ProFormItem>
    </ModalForm>
  );
};

export default IconModalForm;
