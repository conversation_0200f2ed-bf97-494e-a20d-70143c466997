import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import { batchDeleteTaskModule, taskModulePage } from '@/services/http/taskModule';
import { queryRkPagingTable, syncToUrl } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import React, { Key, useCallback, useRef, useState } from 'react';

const JobFlow: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.TaskModulePageVO[]>([]);

  const { run: deleteRecord } = useRequest((ids) => batchDeleteTaskModule({ ids }), {
    manual: true,
    onSuccess: (res) => {
      if (res?.code !== 200) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  // 删除
  const handleDelete = useCallback((rows: API.TaskModulePageVO[]) => {
    const ids: Key[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.name!);
    });

    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除任务流“${names.join(',')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        deleteRecord(ids);
      },
    });
  }, []);

  const columns: ProColumns<API.TaskModulePageVO>[] = [
    {
      title: '任务流名称',
      dataIndex: 'name',
      width: 300,
      initialValue: queryParams.get('name'),
      render: (dom, record) => {
        return (
          <a
            onClick={() => {
              history.push(`/deployment/jobs-flow/edit/${record.id}`);
            }}
          >
            {record.name}
          </a>
        );
      },
    },
    {
      title: '描述',
      dataIndex: 'description',
      initialValue: queryParams.get('taskName'),
      hideInSearch: true,
    },
    {
      title: '操作',
      width: 120,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <a key="del" onClick={() => handleDelete([record])}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.TaskModulePageVO>
        {...defaultTableConfig}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        search={SearchOptionRender}
        onSubmit={syncToUrl}
        actionRef={tableRef}
        columns={columns}
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                history.push('/deployment/jobs-flow/add');
              }}
            >
              新建监控项
            </Button>,
          ],
        }}
        headerTitle="任务列表"
        request={async (params) => queryRkPagingTable({ ...params }, taskModulePage)}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
});

export default JobFlow;
