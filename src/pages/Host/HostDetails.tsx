import { HOST_STATUS } from '@/enums';
import { zabbix } from '@/services/zabbix';
import { option2enum } from '@/utils';
import {
  PageContainer,
  ProCard,
  ProColumns,
  ProDescriptions,
  ProTable,
} from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import React, { useState } from 'react';

const HostDetails: React.FC = () => {
  const columns: ProColumns<RK_API.Interface>[] = [
    {
      title: 'AgentIP地址',
      dataIndex: 'ip',
      width: '50%',
    },
    {
      title: '端口',
      dataIndex: 'port',
    },
  ];
  const { id } = useParams();
  const [userMacros, setUserMacros] = useState<RK_API.HostMacro[]>([]);
  const [interfaces, setInterfaces] = useState<RK_API.Interface[]>([]);

  const { data: hostData, loading } = useRequest(
    () =>
      zabbix({
        hostids: [id],
        selectGroups: 'extend',
        selectMacros: 'extend',
        selectInterfaces: 'extend',
        method: 'host.get',
      }),
    {
      onSuccess: (res) => {
        setUserMacros(
          res
            ?.at(0)
            ?.macros?.sort(
              (a: RK_API.HostMacro, b: RK_API.HostMacro) =>
                Number(a.hostmacroid) - Number(b.hostmacroid),
            ) || [],
        );
        setInterfaces(
          res
            ?.at(0)
            ?.interfaces?.filter((item: RK_API.Interface) => item?.ip && item?.type === '1'),
        );
      },
    },
  );

  return (
    <PageContainer header={{ title: false }}>
      <ProCard title="基础信息" style={{ borderRadius: 0 }} loading={loading}>
        <ProDescriptions column={3} layout="vertical" dataSource={hostData?.at(0) || {}}>
          <ProDescriptions.Item label="主机名称" dataIndex="name" />
          <ProDescriptions.Item label="可见的名称" dataIndex="host" />
          <ProDescriptions.Item
            label="对象群组"
            dataIndex="groups"
            renderText={(_, record) => {
              return record?.groups?.map((item: RK_API.HostGroup) => item.name).join(',');
            }}
          />

          <ProDescriptions.Item
            label="启用"
            valueEnum={option2enum(HOST_STATUS)}
            dataIndex="status"
          />
          <ProDescriptions.Item
            label="类型"
            dataIndex="rkzl_type"
            renderText={(_, record) => {
              return record?.rkzl_type
                ? `${record?.rkzl_type?.name}-${record?.rkzl_subtype?.name}`
                : '-';
            }}
          />

          <ProDescriptions.Item label="描述" dataIndex="description" />
        </ProDescriptions>
      </ProCard>
      <ProCard title="Agent配置" style={{ borderRadius: 0 }} loading={loading}>
        <ProTable
          className="inner-table"
          options={false}
          columns={columns}
          dataSource={interfaces}
          search={false}
          pagination={false}
          size="small"
        />
      </ProCard>
      <ProCard title="通用变量" style={{ borderRadius: 0 }} loading={loading}>
        <ProDescriptions column={3} layout="vertical">
          {userMacros?.map((item, index) => (
            <ProDescriptions.Item key={index} label={item?.description}>
              {item?.value}
            </ProDescriptions.Item>
          ))}
        </ProDescriptions>
      </ProCard>
    </PageContainer>
  );
};

export default HostDetails;
