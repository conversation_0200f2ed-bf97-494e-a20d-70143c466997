import RKCol from '@/components/RKCol';
import { taskModulePage } from '@/services/http/taskModule';
import {
  taskModuleOrchestrationCreate,
  taskModuleOrchestrationDetail,
  taskModuleOrchestrationUpdate,
} from '@/services/http/taskModuleOrchestration';
import { onSuccessAndGoBack, onSuccessAndRefresh } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import { Row } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import { memo, useRef } from 'react';
import TargetHost from './TargetHost';

const DeploymentRules = () => {
  const formRef = useRef<ProFormInstance>();
  const hostListRef = useRef<API.HostPageVO[]>();

  // 判断是否为编辑页面
  const { id } = useParams();
  const isEditPage = !!id;
  // 获取详情
  useRequest(() => taskModuleOrchestrationDetail({ id: id! }), {
    ready: isEditPage,
    onSuccess: (res) => {
      const { taskModuleList = [], name = '', hostList = [], id } = res || {};
      formRef.current?.setFieldsValue({
        taskModuleList: taskModuleList.map((item) => item.id),
        name,
        hosts: hostList.map((item) => item.id),
        id,
      });
      hostListRef.current = hostList;
    },
  });

  // 获取监控任务
  const { data: taskModuleList, loading: taskModuleListLoading } = useRequest(() =>
    taskModulePage({
      page: {
        page: 1,
        size: 1000,
      },
    }),
  );

  // 新建
  const { run: create, loading: createLoading } = useRequest(taskModuleOrchestrationCreate, {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });

  // 修改
  const { run: update, loading: updateLoading } = useRequest(taskModuleOrchestrationUpdate, {
    manual: true,
    onSuccess: (res) => onSuccessAndRefresh(res),
    formatResult: (res) => res,
  });

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm
        formRef={formRef}
        onFinish={async ({ id, ...values }) => {
          if (isEditPage) {
            update({ id }, values);
          } else {
            create(values);
          }
        }}
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          onReset: () => {
            history.go(-1);
          },
          render: (props, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
          submitButtonProps: {
            loading: createLoading || updateLoading,
          },
        }}
      >
        <div style={{ display: 'none' }}>
          <ProFormText name="id" label="id" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText name="name" label="名称" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="taskModuleList"
              label="监控模块"
              options={(taskModuleList?.data || []) as DefaultOptionType[]}
              rules={[requiredRule]}
              mode="multiple"
              fieldProps={{
                fieldNames: {
                  label: 'name',
                  value: 'id',
                },
                loading: taskModuleListLoading,
              }}
            />
          </RKCol>
        </Row>
        <ProForm.Item name="hosts">
          <TargetHost hostList={hostListRef.current} />
        </ProForm.Item>
      </ProForm>
    </PageContainer>
  );
};

export default memo(DeploymentRules);
