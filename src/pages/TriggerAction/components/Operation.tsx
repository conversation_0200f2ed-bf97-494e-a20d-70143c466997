import HeadTitle from '@/components/HeadTitle';
import RKCol from '@/components/RKCol';
import UnitInput from '@/components/UnitInput';
import { DATE_UNIT } from '@/enums';
import { requiredRule } from '@/utils/setting';
import { ProForm, ProFormSwitch } from '@ant-design/pro-components';
import { Row } from 'antd';
import OperationSetter from './OperationSetter';

export default function Operation() {
  return (
    <>
      <HeadTitle>操作配置</HeadTitle>
      <Row gutter={24}>
        <RKCol>
          <ProForm.Item label="默认操作步骤持续时间" name="esc_period" rules={[requiredRule]}>
            <UnitInput options={DATE_UNIT} />
          </ProForm.Item>
        </RKCol>
        <RKCol>
          <ProFormSwitch
            label="维护期不执行任何动作"
            name="pause_suppressed"
            getValueFromEvent={(val) => (val ? '1' : '0')}
            getValueProps={(value) => ({ checked: value === '1' })}
          />
        </RKCol>
        <RKCol>
          <ProFormSwitch
            label="依赖的对象状态异常时发送通知"
            tooltip="如：禁用监控项、触发器时"
            name="notify_if_canceled"
            getValueFromEvent={(val) => (val ? '1' : '0')}
            getValueProps={(value) => ({ checked: value === '1' })}
          />
        </RKCol>
      </Row>
      <ProForm.Item
        name={'operations'}
        rules={[requiredRule]}
        transform={(value: RK_API.Operation[], namePath) => ({
          [namePath]: value.map((item) => ({
            ...item,
            operationid: undefined,
            actionid: undefined,
            opconditions: item?.opconditions?.map((op) => ({
              ...op,
              operationid: undefined,
              opconditionid: undefined,
            })),
            opcommand_grp: item?.opcommand_grp?.map(({ groupid }) => ({ groupid })),
            opmessage_usr: item?.opmessage_usr?.map(({ userid }) => ({ userid })),
            opcommand_hst: item?.opcommand_hst?.map(({ hostid }) => ({ hostid })),
            opmessage_grp: item?.opmessage_grp?.map(({ usrgrpid }) => ({ usrgrpid })),
          })),
        })}
      >
        <OperationSetter />
      </ProForm.Item>
    </>
  );
}
