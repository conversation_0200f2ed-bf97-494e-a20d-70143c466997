import RkLine from '@/components/Charts/Line';
import { zabbix } from '@/services/zabbix';
import { Card, Col, ColProps, Empty, Typography } from 'antd';
import dayjs from 'dayjs';
import { FC, memo, useEffect, useMemo, useState } from 'react';
import { UsageRateProps } from './DataCard';
import styles from './index.less';

const ColorMap = {
  CPU: '#38CCE1',
  Memory: '#EA22FD',
  Disk: '#A66CFE',
};

const LineChartCard: FC<{
  title: string;
  value: UsageRateProps[];
  span?: ColProps['span'];
  type?: 'card' | 'list';
}> = ({ title, value, span, type }) => {
  const COL_SPAN = span ? { span } : { xs: 24, sm: 12, md: 8, lg: 8, xl: 8, xxl: 8 };
  const [allData, setAllData] = useState<RK_API.History[][]>([]);

  const fetchHistoryForItems = async (items: UsageRateProps[]) => {
    // 使用Promise.all并发处理所有请求
    const responses = await Promise.all(
      items.map(async ({ itemid, value_type }) => {
        const response = await zabbix({
          itemids: itemid,
          time_from: dayjs().add(-1, 'hour').unix(),
          time_till: dayjs().unix(),
          history: value_type,
          method: 'history.get',
        });
        return response?.data || [];
      }),
    );
    return responses;
  };

  useEffect(() => {
    if (!value?.length) return;
    fetchHistoryForItems(value)
      .then((res) => {
        // 收集所有请求的结果
        setAllData(res);
      })
      .catch((error) => {
        // 错误处理逻辑
        console.error('Error fetching data:', error);
      });
  }, [value]);

  const chatMap = useMemo(() => {
    const map = new Map();
    value.forEach((item) => {
      map.set(item.itemid, { name: item.name, color: ColorMap[item.dataType] });
    });
    return map;
  }, [value]);

  return (
    <Col {...COL_SPAN}>
      {/* <div className={styles.circle1} /> */}
      {type === 'card' && (
        <Card
          title={title || '-'}
          className={styles.card}
          bodyStyle={{ height: '100%', paddingTop: 0 }}
        >
          {!value?.length && (
            <Empty
              imageStyle={{
                marginTop: 130,
              }}
            />
          )}
          {allData?.map((value, index) => {
            if (!value.length) return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;
            const itemid = value?.at(0)?.itemid;
            return (
              <RkLine
                key={index}
                height={100}
                data={(value as RK_API.History[]).map((item) => ({
                  ...item,
                  value: Number((parseFloat(item.value) as number).toFixed(2)),
                }))}
                color={chatMap.get(itemid)?.color}
                xField="clock"
                yField="value"
                xAxis={{
                  label: {
                    formatter(text) {
                      if (!text) return '';
                      return dayjs.unix(Number(text)).format('HH:mm:ss');
                    },
                  },
                }}
                yAxis={{
                  title: {
                    text: chatMap.get(itemid)?.name,
                  },

                  grid: {
                    line: {
                      style: {
                        lineWidth: 0,
                      },
                    },
                  },
                }}
                style={{
                  marginBlock: 20,
                }}
                tooltip={{
                  title: () => {
                    return chatMap.get(itemid)?.name;
                  },
                  formatter: (datum) => {
                    return {
                      name: dayjs.unix(Number(datum.clock)).format('HH:mm:ss'),
                      value: datum.value,
                    };
                  },
                }}
              />
            );
          })}
        </Card>
      )}
      {type === 'list' &&
        allData?.map((value, index) => {
          if (!value.length) return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;
          const itemid = value?.at(0)?.itemid;
          return (
            <div key={index}>
              <Typography.Title level={5}>{chatMap.get(itemid)?.name}</Typography.Title>
              <RkLine
                key={index}
                height={200}
                data={(value as RK_API.History[]).map((item) => ({
                  ...item,
                  value: Number((parseFloat(item.value) as number).toFixed(2)),
                }))}
                color={chatMap.get(itemid)?.color}
                xField="clock"
                yField="value"
                xAxis={{
                  label: {
                    formatter(text) {
                      if (!text) return '';
                      return dayjs.unix(Number(text)).format('HH:mm:ss');
                    },
                  },
                }}
                yAxis={{
                  grid: {
                    line: {
                      style: {
                        lineWidth: 0,
                      },
                    },
                  },
                }}
                style={{
                  marginBlockStart: 20,
                  marginBlockEnd: 36,
                }}
                tooltip={{
                  title: () => {
                    return chatMap.get(itemid)?.name;
                  },
                  formatter: (datum) => {
                    return {
                      name: dayjs.unix(Number(datum.clock)).format('HH:mm:ss'),
                      value: datum.value,
                    };
                  },
                }}
              />
            </div>
          );
        })}
    </Col>
  );
};

export default memo(LineChartCard);
