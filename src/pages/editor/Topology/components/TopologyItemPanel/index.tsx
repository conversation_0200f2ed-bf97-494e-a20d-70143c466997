import Icon from '@/components/Icon';
import { getAllMonitoringObjectGroup } from '@/services/http/monitoringObject';
import {
  CaretRightOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SearchOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { ProFormText } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, Collapse, Dropdown, MenuProps, Space, Spin, Tooltip, Typography } from 'antd';
import classNames from 'classnames/bind';
import { MenuInfo } from 'rc-menu/lib/interface';
import React, { useCallback, useRef, useState } from 'react';
import { DeviceTypeMap } from '../WebNetworkNode';
import IconModalForm from './IconModalForm';
import styles from './index.less';

const items: MenuProps['items'] = [
  {
    label: '图标配置',
    icon: <SettingOutlined />,
    key: 'icon',
  },
];

const cx = classNames.bind(styles);

const { Paragraph } = Typography;

const { Panel } = Collapse;

const TopologyItemPanel: React.FC<{
  hostIds: string[];
  startDrag: (
    e: React.MouseEvent<any, MouseEvent>,
    data: API.MonitoringObjectVO & {
      valuesByTag: string[];
      hostDeviceIcon?: string;
    },
  ) => void;
}> = ({ startDrag, hostIds }) => {
  const [collapsed, setCollapsed] = useState(true); // 新增状态变量

  const [activeKey, setActiveKey] = useState<string[]>([]);
  const [treeData, setTreeData] = useState<API.MonitoringObjectGroupVO[]>([]);

  const [modalVisit, setModalVisit] = useState(false);
  const currentMenuRef = useRef<Record<string, any>>({});
  const onMenuClick = (e: MenuInfo, data: Record<string, any>) => {
    e.domEvent.stopPropagation();
    if (e.key === 'icon') {
      setModalVisit(true);
      currentMenuRef.current = data;
    }
  };

  const { data, loading, refresh } = useRequest(getAllMonitoringObjectGroup, {
    initialData: [],
    onSuccess: (res) => {
      setTreeData(res || []);
    },
  });

  const onSearch = useCallback(
    (e: any) => {
      // const hosts = treeData?.flatMap((group) => group?.hostData);
      const groups = data?.filter((group) => {
        return group?.hostData?.find((item) => item?.name?.includes(e.target.value));
      });
      const ids = groups?.map((item) => item.groupId!) || [];
      setActiveKey(ids);
      setTreeData(groups || []);
    },
    [data],
  );

  return (
    <div
      className={cx('panel', {
        collapsed,
      })}
    >
      {/* 添加展开收起按钮 */}
      <Button
        className={cx('collapsed-icon')}
        type="text"
        icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        onClick={() => setCollapsed(!collapsed)} // 点击按钮切换状态
      />

      <ProFormText
        fieldProps={{
          suffix: <SearchOutlined />,
          onPressEnter: onSearch,
        }}
      />

      <div className={styles.content}>
        <Spin spinning={loading}>
          <Collapse
            collapsible="header"
            activeKey={activeKey}
            onChange={(key) => {
              setActiveKey(key as string[]);
            }}
            bordered={false}
            ghost
            expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
          >
            {treeData
              ?.filter((group) => group?.hostData?.length)
              .map((group) => {
                return (
                  <Panel
                    header={
                      <Dropdown
                        menu={{ items, onClick: (e) => onMenuClick(e, group) }}
                        trigger={['contextMenu']}
                      >
                        <Space size={4}>
                          <Icon type={`icon-${group?.icon?.icon || 'network-server'}`} />
                          <div onClick={(e) => e.preventDefault()}>{group?.name}</div>
                        </Space>
                      </Dropdown>
                    }
                    key={group.groupId!}
                  >
                    {group?.hostData?.map((item) => {
                      const isDisabled = hostIds?.includes(item.hostId!);
                      const valuesByTag =
                        item?.inheritedTags
                          ?.filter((obj) => obj.tag === 'rkmon_view_type_dev_type')
                          .map((obj) => obj.value || '') || [];

                      // 获取主机 icon
                      const hostDeviceIcon =
                        item?.icon?.icon ||
                        group?.icon?.icon ||
                        valuesByTag?.at(0) ||
                        'network-server';

                      return (
                        <Dropdown
                          key={item.hostId}
                          menu={{ items, onClick: (e) => onMenuClick(e, item) }}
                          trigger={['contextMenu']}
                        >
                          <Tooltip
                            title={`${
                              item?.icon?.name ||
                              group?.icon?.name ||
                              DeviceTypeMap[hostDeviceIcon as keyof typeof DeviceTypeMap]
                            } - ${item.name}`}
                          >
                            <Paragraph
                              disabled={isDisabled}
                              data-type="web-network"
                              draggable={!isDisabled}
                              onDragStart={(e) => {
                                startDrag(e, { ...item, valuesByTag, hostDeviceIcon });
                              }}
                              ellipsis={{
                                rows: 1,
                              }}
                              className={styles.item}
                              style={{
                                pointerEvents: isDisabled ? 'none' : 'auto',
                              }}
                            >
                              <Icon
                                type={`icon-${hostDeviceIcon}`}
                                className={cx(styles.ic, {
                                  gray: item.status !== '0' || isDisabled,
                                })}
                              />
                              {item.name}
                            </Paragraph>
                          </Tooltip>
                        </Dropdown>
                      );
                    })}
                  </Panel>
                );
              })}
          </Collapse>
        </Spin>
      </div>
      <IconModalForm
        open={modalVisit}
        onOpenChange={setModalVisit}
        initialValues={currentMenuRef.current}
        onSuccess={refresh}
      />
    </div>
  );
};

export default TopologyItemPanel;
