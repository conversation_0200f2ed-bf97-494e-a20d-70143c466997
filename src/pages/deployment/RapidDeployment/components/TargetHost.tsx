import { HOST_USABILITY } from '@/enums';
import { option2enum } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Space } from 'antd';
import { FC, memo, useCallback, useEffect, useState } from 'react';
import SelectHostDrawer from './SelectHostDrawer';

const TargetHost: FC<{
  value?: string[];
  onChange?: (value: string[]) => void;
  hostList?: API.HostPageVO[];
}> = ({ onChange, value = [], hostList }) => {
  const [dataSource, setDataSource] = useState<API.HostPageVO[]>([]);

  useEffect(() => {
    setDataSource(hostList || []);
  }, [hostList]);

  const handleSelect = useCallback(
    async (arr: API.HostPageVO[]) => {
      const newArr = [...dataSource, ...arr];
      setDataSource(newArr);
      const ids = newArr?.map((item) => item.id!) || [];
      onChange?.(ids);
    },
    [dataSource],
  );

  const handleDelete = (arr: API.HostPageVO[]) => {
    const newArr = dataSource.filter((item) => !arr.includes(item));
    setDataSource(newArr);
    const ids = newArr?.map((item) => item.id!) || [];
    onChange?.(ids);
  };

  const columns: ProColumns<API.HostPageVO>[] = [
    {
      title: '主机名',
      dataIndex: 'host',
    },
    {
      title: '状态',
      dataIndex: 'usability',
      valueEnum: option2enum(HOST_USABILITY),
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <a onClick={() => handleDelete([record])}>删除</a>
          </Space>
        );
      },
    },
  ];

  return (
    <ProTable<API.HostPageVO>
      {...defaultTableConfig}
      className="inner-table"
      search={false}
      options={false}
      columns={columns}
      dataSource={dataSource}
      headerTitle="目标主机列表"
      toolbar={{
        actions: [
          <SelectHostDrawer
            key="add"
            handleSelect={handleSelect}
            initialValues={{ hostList: value }}
          />,
        ],
      }}
    />
  );
};

export default memo(TargetHost);
