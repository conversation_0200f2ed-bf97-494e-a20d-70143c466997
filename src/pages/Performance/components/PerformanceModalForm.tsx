import {
  externalMonitoringPanelCreate,
  externalMonitoringPanelUpdate,
} from '@/services/http/externalMonitoring';
import { requiredRule } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ModalFormProps,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, message } from 'antd';
import React from 'react';

const PerformanceModalForm: React.FC<
  ModalFormProps & {
    isEdit?: boolean;
    onSuccess?: () => void;
  }
> = ({ isEdit, onSuccess, ...rest }) => {
  const title = isEdit ? '编辑' : '新建页面';
  return (
    <ModalForm
      width={600}
      style={{ minHeight: 200 }}
      title={title}
      onFinish={async (value) => {
        const { id } = value;
        const { result } = isEdit
          ? await externalMonitoringPanelUpdate({ id }, value)
          : await externalMonitoringPanelCreate(value);

        if (result) {
          message.success('操作成功!');
          onSuccess?.();
        }
        return result;
      }}
      trigger={
        <Button
          className={isEdit ? 'inner-table-link' : ''}
          type={isEdit ? 'link' : 'primary'}
          icon={!isEdit && <PlusOutlined />}
        >
          {title}
        </Button>
      }
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
      }}
      {...rest}
    >
      <div className="rk-none">
        <ProFormText name="id" />
      </div>
      <ProFormText name="name" label="名称" rules={[requiredRule]} />
      <ProFormTextArea
        name="uri"
        label="地址"
        rules={[requiredRule]}
        fieldProps={{ autoSize: { minRows: 2, maxRows: 6 } }}
      />
      <ProFormTextArea
        name="description"
        label="描述"
        fieldProps={{ autoSize: { minRows: 1, maxRows: 3 } }}
      />
    </ModalForm>
  );
};

export default PerformanceModalForm;
