import TransferTable from '@/components/TableTransfer';
import Modal, { ModalProps as AntModalProps } from 'antd/es/modal';
import React, { memo, useEffect, useRef } from 'react';

type DataSourceProps = RK_API.Auth & RK_API.HostGroup;

// 合并数组
const mergeArrays = (arr1: DataSourceProps[] = [], arr2: DataSourceProps[] = []) => {
  // 创建一个空对象，用于存储合并后的结果
  const merged: Record<string, any> = {};

  // 将第一个数组的元素添加到merged对象中
  for (let i = 0; i < arr1.length; i++) {
    const item = arr1[i];
    merged[item.id!] = item;
  }

  // 将第二个数组的元素添加到merged对象中
  for (let j = 0; j < arr2.length; j++) {
    const item = arr2[j];
    if (merged[item.id!]) {
      // 如果merged对象中已经存在具有相同"id"的元素，则将两个对象合并
      Object.assign(merged[item.id!], item);
    } else {
      // 如果merged对象中不存在具有相同"id"的元素，则直接添加到merged对象中
      merged[item.id!] = item;
    }
  }

  // 将merged对象中的值转换为数组，并返回合并后的结果
  return Object.values(merged);
};

const leftColumns = [
  {
    dataIndex: 'name',
    width: 150,
    ellipsis: true,
    title: '名称',
  },
];

type ModalProps = Omit<AntModalProps, 'onOk'> & {
  targetKeys?: string[];
  onOk?: (keys: string[], totalData: DataSourceProps[]) => void;
  titles?: string[];
  onChange?: (keys: DataSourceProps[]) => void;
  value?: DataSourceProps[];
  options?: DataSourceProps[];
  loading?: boolean;
};

const AssociatedPermission: React.FC<ModalProps> = (props) => {
  const { onOk, titles, value, options = [], loading, ...restProps } = props;
  const selectedKeys = useRef<string[]>([]);
  useEffect(() => {
    selectedKeys.current = value?.map((item) => item.id!) || [];
  }, [value]);
  const customOk = () => {
    const originArr = value?.filter((item) => selectedKeys.current.includes(item.id!)) || [];
    const selectList = options.filter((item) => selectedKeys.current.includes(item.id!)) || [];
    onOk?.(selectedKeys.current || [], mergeArrays(originArr, selectList));
  };

  return (
    <Modal title="配置" width={800} centered onOk={customOk} destroyOnClose {...restProps}>
      <TransferTable
        titles={titles || ['对象群组', '已选对象群组']}
        tableRowKey="id"
        dataSource={options?.map((item) => ({ ...item, id: item.id })) || []}
        loading={loading}
        leftColumns={leftColumns}
        rightColumns={leftColumns}
        onChange={(keys) => {
          selectedKeys.current = keys;
        }}
        targetKeys={selectedKeys.current}
        showSearch
        keywords="name"
      />
    </Modal>
  );
};

export default memo(AssociatedPermission);
