import React from 'react';
import MonacoEditor, { MonacoEditorProps } from 'react-monaco-editor';
// import * as monaco from 'monaco-editor';
import styles from './index.less';

// function registerShellLanguage() {
//   monaco.languages.register({
//     id: 'shell'
//   });

//   // 定义语法规则和颜色方案
//   monaco.languages.setMonarchTokensProvider('shell', {
//     tokenizer: {
//       root: [
//         [/(echo|cd|ls|mkdir|rm)\b/, 'keyword.shell'],
//         [/\$[\w]+|\$\{\w+\}|\$\(.*?\)/, 'variable.shell'],
//         [
//           /\b(if|then|else|elif|fi|case|esac|while|do|done|for|in|break|continue|return)\b/,
//           'keyword.control.shell'
//         ],
//         [/#[^\n\r]*/, 'comment.shell']
//       ]
//     }
//   });

//   // 设置语言配置信息
//   monaco.languages.setLanguageConfiguration('shell', {
//     comments: {
//       lineComment: '#'
//     }
//   });
// }

// registerShellLanguage();

const ShellEditor: React.FC<MonacoEditorProps> = (props) => {
  // useEffect(() => {
  //   registerShellLanguage();
  // }, []);
  return (
    <MonacoEditor
      className={styles.editor}
      height="100%"
      width="100%"
      theme="dark"
      options={{
        wordWrap: 'on',
        cursorStyle: 'block',
        lineNumbers: 'off',
        readOnly: true,
        selectOnLineNumbers: true,
        minimap: { enabled: false },
        scrollBeyondLastLine: false,
        lineNumbersMinChars: 0,
        lineDecorationsWidth: 0, // 更改行号的宽度
        glyphMargin: false, // 关闭行号区域左侧的图标区
        scrollbar: {
          alwaysConsumeMouseWheel: false,
        },
        automaticLayout: true, //以启用自适应布局
      }}
      language="shell"
      {...props}
    />
  );
};

export default ShellEditor;
