declare namespace RK_API {
  type Auth = {
    id?: string;
    permission?: number;
  };

  type BatchIdRequest = {
    ids?: string[];
  };

  type Response<T> = {
    /** JSON-RPC 协议的版本 */
    jsonrpc?: string;
    result?: T;
    /** 对应请求的标识符 */
    id?: string;
    error?: Error;
  };

  type Error = {
    /** 错误代码 */
    code?: string;
    /** 一个简短的错误摘要 */
    message?: string;
    /** 更详细的错误消息 */
    data?: string;
  };

  type Interface = {
    interfaceid?: string;
    hostid?: string;
    main: string;
    type: string;
    useip: string;
    ip: string;
    dns?: string;
    port: string;
    available?: string;
    error?: string;
    errors_from?: string;
    disable_until?: string;
    details?: any[];
    key?: string;
  };

  type Host = {
    [x: string]: any;
    maintenances: any[];
    hostid?: string;
    proxy_hostid: string;
    host: string;
    status: string;
    lastaccess: string;
    ipmi_authtype: string;
    ipmi_privilege: string;
    ipmi_username: string;
    ipmi_password: string;
    ipmi_disable_until: string;
    snmp_disable_until: string;
    maintenanceid: string;
    maintenance_status: string;
    maintenance_type: string;
    maintenance_from: string;
    name: string;
    description: string;
    tls_connect: string;
    tls_accept: string;
    tls_issuer: string;
    tls_subject: string;
    triggers?: Trigger[];
    items?: Item[];
    templateid: string;
    interfaces?: Interface[];
    discoveries?: Discoveries[];
    parentTemplates?: TemplateGetDTO[];
    tags?: TemplateTag[];
    inheritedTags?: TemplateTag[];
  };

  type HostGroup = {
    groupid?: string;
    name?: string;
    flags?: number;
    internal?: number;
    uuid?: string;
    hosts?: Host[];
  };

  type HostMacro = {
    hostmacroid?: string;
    hostid?: string;
    macro?: string;
    value?: string;
    type?: number;
    description?: string;
  };

  type Media = {
    /** 被使用的媒介类型ID. */
    mediatypeid?: string;
    /** 地址, 用户名或者其他接收标识符.
如果 媒介类型 是邮件, 值被定义为数组. 如果 媒介类型 是其他类型, 值被定义为字符串.
 */
    sendto?: string[];
    /** 是否启用媒介.
可用值:
0 - (default) 启用;
1 - 禁用.
 */
    active?: number;
    /** 触发媒介发送告警的告警级别.
每一位数字代表一个告警级别，并以二进制形式存储. 例如, 12 相当于二进制的 1100, 它表示告警级别为警告和一般严重的告警将触发告警媒介.
参阅 触发器对象 查看告警级别列表.
默认: 63
 */
    severity?: number;
    /** 时间窗口: 能够发送告警通知的 时间段 或者以分号分隔的用户宏.
默认: 1-7,00:00-24:00
 */
    period?: string;
  };

  type MediasItem = {
    severity?: string;
    period?: string;
    sendto?: string[];
    mediatypeid?: string;
    active?: string;
    mediaid?: string;
    userid?: string;
  };

  type MediatypesItem = {
    smtp_authentication?: string;
    mediatypeid?: string;
    maxsessions?: string;
    smtp_verify_host?: string;
    description?: string;
    maxattempts?: string;
    type?: string;
    timeout?: string;
    exec_path?: string;
    content_type?: string;
    event_menu_url?: string;
    exec_params?: string;
    gsm_modem?: string;
    script?: string;
    smtp_security?: string;
    smtp_verify_peer?: string;
    smtp_email?: string;
    smtp_helo?: string;
    show_event_menu?: string;
    smtp_port?: string;
    smtp_server?: string;
    passwd?: string;
    attempt_interval?: string;
    event_menu_name?: string;
    name?: string;
    process_tags?: string;
    parameters?: Record<string, any>[];
    username?: string;
    status?: string;
  };

  type ResultBoolean = {
    /** JSON-RPC 协议的版本 */
    jsonrpc?: string;
    /** 方法返回的数据 */
    result?: boolean;
    /** 对应请求的标识符 */
    id?: string;
  };

  type ResultError = {
    /** JSON-RPC 协议的版本 */
    jsonrpc?: string;
    error?: Error;
    /** 对应请求的标识符 */
    id?: string;
  };

  type ResultInteger = {
    /** JSON-RPC 协议的版本 */
    jsonrpc?: string;
    /** 方法返回的数据 */
    result?: number;
    /** 对应请求的标识符 */
    id?: string;
  };

  type ResultListTemplateGetDTO = {
    /** JSON-RPC 协议的版本 */
    jsonrpc?: string;
    /** 方法返回的数据 */
    result?: TemplateGetDTO[];
    /** 对应请求的标识符 */
    id?: string;
  };

  type ResultListUserGroupDTO = {
    /** JSON-RPC 协议的版本 */
    jsonrpc?: string;
    /** 方法返回的数据 */
    result?: UserGroupDTO[];
    /** 对应请求的标识符 */
    id?: string;
  };

  type ResultListUserInfoResponse = {
    /** JSON-RPC 协议的版本 */
    jsonrpc?: string;
    /** 方法返回的数据 */
    result?: UserInfoResponse[];
    /** 对应请求的标识符 */
    id?: string;
  };

  type ResultString = {
    /** JSON-RPC 协议的版本 */
    jsonrpc?: string;
    /** 方法返回的数据 */
    result?: string;
    /** 对应请求的标识符 */
    id?: string;
  };

  type ResultTemplateIdDTO = {
    /** JSON-RPC 协议的版本 */
    jsonrpc?: string;
    result?: TemplateIdDTO;
    /** 对应请求的标识符 */
    id?: string;
  };

  type ResultUserAuthenticationResponse = {
    /** JSON-RPC 协议的版本 */
    jsonrpc?: string;
    result?: UserAuthenticationResponse;
    /** 对应请求的标识符 */
    id?: string;
  };

  type ResultUserGroupIdDTO = {
    /** JSON-RPC 协议的版本 */
    jsonrpc?: string;
    result?: UserGroupIdDTO;
    /** 对应请求的标识符 */
    id?: string;
  };

  type ResultUserIdsResponse = {
    /** JSON-RPC 协议的版本 */
    jsonrpc?: string;
    result?: UserIdsResponse;
    /** 对应请求的标识符 */
    id?: string;
  };

  type Role = {
    readonly?: string;
    roleid?: string;
    name?: string;
    type?: string;
  };

  type TagAuth = {
    groupid?: string;
    tag?: string;
    value?: string;
  };

  type Template = {
    templateid?: string;
    host?: string;
    description?: string;
    name?: string;
    uuid?: string;
  };

  type TemplateCreateCommand = {
    templateid?: string;
    host?: string;
    description?: string;
    name?: string;
    uuid?: string;
    groups?: HostGroup[];
    tags?: TemplateTag[];
    templates?: Template[];
    macros?: HostMacro[];
  };

  type TemplateGetCommand = {
    countOutput?: boolean;
    editable?: boolean;
    excludeSearch?: boolean;
    filter?: Record<string, any>;
    limit?: number;
    output?: string;
    preservekeys?: boolean;
    search?: Record<string, any>;
    searchByAny?: boolean;
    searchWildcardsEnabled?: boolean;
    sortorder?: string;
    startSearch?: boolean;
    templateids?: string[];
    groupids?: string[];
    parentTemplateids?: string[];
    hostids?: string[];
    graphids?: string[];
    itemids?: string[];
    triggerids?: string[];
    with_items?: boolean;
    with_triggers?: boolean;
    with_graphs?: boolean;
    with_httptests?: boolean;
    evaltype?: number;
    tags?: string[];
    selectGroups?: string;
    selectTags?: string;
    selectHosts?: string;
    selectTemplates?: string;
    selectParentTemplates?: string;
    selectHttpTests?: string;
    selectItems?: string;
    selectDiscoveries?: string;
    selectTriggers?: string;
    selectGraphs?: string;
    selectMacros?: string;
    selectDashboards?: string;
    selectValueMaps?: string;
    limitSelects?: number;
    sortfield?: string[];
  };

  type TemplateGetDTO = {
    ipmi_privilege?: string;
    maintenance_status?: string;
    tls_psk_identity?: string;
    flags?: string;
    description?: string;
    tls_issuer?: string;
    uuid?: string;
    auto_compress?: string;
    proxy_hostid?: string;
    maintenanceid?: string;
    maintenance_from?: string;
    ipmi_authtype?: string;
    ipmi_username?: string;
    host?: string;
    tls_psk?: string;
    custom_interfaces?: string;
    proxy_address?: string;
    maintenance_type?: string;
    tls_accept?: string;
    templateid?: string;
    lastaccess?: string;
    ipmi_password?: string;
    name?: string;
    tls_connect?: string;
    tls_subject?: string;
    status?: string;
    jmx_available?: string;
    errors_from?: string;
    available?: string;
    snmp_errors_from?: string;
    error?: string;
    jmx_errors_from?: string;
    snmp_disable_until?: string;
    jmx_error?: string;
    jmx_disable_until?: string;
    disable_until?: string;
    ipmi_errors_from?: string;
    snmp_error?: string;
    snmp_available?: string;
    ipmi_available?: string;
    ipmi_error?: string;
    ipmi_disable_until?: string;
    triggers?: Trigger[];
    items?: Item[];
    tags?: TemplateTag[];
    parentTemplates?: TemplateGetDTO[];
    discoveries?: Discoveries[];
  };

  type Discoveries = {
    itemid: string;
    type: string;
    snmp_oid: string;
    hostid: string;
    name: string;
    key_: string;
    delay: string;
    history: string;
    trends: string;
    status: string;
    value_type: string;
    trapper_hosts: string;
    units: string;
    logtimefmt: string;
    templateid: string;
    valuemapid: string;
    params: string;
    ipmi_sensor: string;
    authtype: string;
    username: string;
    password: string;
    publickey: string;
    privatekey: string;
    flags: string;
    interfaceid: string;
    description: string;
    inventory_link: string;
    lifetime: string;
    jmx_endpoint: string;
    master_itemid: string;
    timeout: string;
    url: string;
    query_fields: any[];
    posts: string;
    status_codes: string;
    follow_redirects: string;
    post_type: string;
    http_proxy: string;
    headers: any[];
    retrieve_mode: string;
    request_method: string;
    ssl_cert_file: string;
    ssl_key_file: string;
    ssl_key_password: string;
    verify_peer: string;
    verify_host: string;
    allow_traps: string;
    discover: string;
    uuid: string;
    state: string;
    error: string;
    parameters: any[];
  };

  type TemplateIdDTO = {
    templateids?: string[];
  };

  type TemplateMassreAddCommand = {
    templates?: Template[];
    groups?: HostGroup[];
    macros?: HostMacro[];
    templates_link?: Template[];
  };

  type TemplateMassreMoveCommand = {
    templateids?: string[];
    groupids?: string[];
    macros?: string[];
    templateids_clear?: string[];
    templateids_link?: string[];
  };

  type TemplateMassreUpdateCommand = {
    templates?: Template[];
    groups?: HostGroup[];
    macros?: HostMacro[];
    templates_clear?: Template[];
    templates_link?: Template[];
  };

  type TemplateTag = {
    tag?: string;
    value?: string;
    id?: string;
  };

  type TemplateUpdateCommand = {
    templateid?: string;
    host?: string;
    description?: string;
    name?: string;
    uuid?: string;
    groups?: HostGroup[];
    tags?: TemplateTag[];
    macros?: HostMacro[];
    templates?: Template[];
    templates_clear?: Template[];
  };

  type User = {
    userid?: string;
  };

  type UserAuthenticationRequest = {
    extend?: boolean;
    sessionid?: string;
  };

  type UserAuthenticationResponse = {
    autologin?: string;
    debug_mode?: number;
    timezone?: string;
    roleid?: string;
    userdirectoryid?: string;
    attempt_clock?: string;
    refresh?: string;
    attempt_failed?: string;
    sessionid?: string;
    type?: number;
    userid?: string;
    attempt_ip?: string;
    url?: string;
    gui_access?: number;
    rows_per_page?: string;
    surname?: string;
    autologout?: string;
    name?: string;
    userip?: string;
    theme?: string;
    lang?: string;
    username?: string;
  };

  type UserCreateRequest = {
    /** 用户名称 */
    username?: string;
    /** 是否允许自动登录.
可用值:
0 - (default) 禁止自动登录;
1 - 允许自动登录.
 */
    autologin?: number;
    /** 会话过期时长. 接受具有后缀的秒或时间单位. 如果设置为 0s, 会话将永不过期.
默认: 15m.
 */
    autologout?: string;
    /** 用户语言代码, 示例, en_GB.
默认: default - 系统默认语言.
 */
    lang?: string;
    /** 自动刷新间隔. 接受具有后缀的秒或时间单位.
默认: 30s.
 */
    refresh?: string;
    /** 每页显示的对象条目.
默认: 50.
 */
    rowsPerPage?: number;
    /** 姓. */
    surname?: string;
    /** 用户的主题.
可用值:
default - (default) 系统默认主题;
blue-theme - 蓝主题;
dark-theme - 黑主题.
 */
    theme?: string;
    /** 用户登录后重定向页面的URL.
     */
    url?: string;
    /** 用户时区, 示例, Europe/London, UTC.
默认: default - 系统默认时区.
 */
    timezone?: string;
    /** 用户的角色ID. */
    roleid?: string;
    /** 用户的密码。
如果用户仅添加到具有 LDAP 访问权限的组，则可以省略。
 */
    passwd?: string;
    /** 要将用户添加到的用户 组。
用户组必须具有已定义的 usrgrpid 属性。
 */
    usrgrps?: Usrgrp[];
    /** 要创建的用户 media */
    medias?: Media[];
  };

  type UserGroupCreateRequest = {
    usrgrpid?: string;
    name?: string;
    debug_mode?: number;
    gui_access?: number;
    users_status?: number;
    rights?: Auth[];
    tag_filters?: TagAuth[];
    users?: User[];
  };

  type UserGroupDTO = {
    usrgrpid?: string;
    name?: string;
    debug_mode?: number;
    gui_access?: number;
    users_status?: number;
    users?: UserInfoDTO[];
  };

  type UserGroupGetReqeust = {
    countOutput?: boolean;
    editable?: boolean;
    excludeSearch?: boolean;
    filter?: Record<string, any>;
    limit?: number;
    output?: string;
    preservekeys?: boolean;
    search?: Record<string, any>;
    searchByAny?: boolean;
    searchWildcardsEnabled?: boolean;
    sortorder?: string;
    startSearch?: boolean;
    status?: number;
    userids?: string[];
    usrgrpids?: string[];
    selectTagFilters?: string;
    selectUsers?: string;
    selectRights?: string;
    limitSelects?: number;
    sortfield?: string[];
  };

  type UserGroupIdDTO = {
    userids?: string[];
  };

  type UserGroupUpdateRequest = {
    usrgrpid?: string;
    name?: string;
    debug_mode?: number;
    gui_access?: number;
    users_status?: number;
    rights?: Auth[];
    tag_filters?: TagAuth[];
    users?: User[];
  };

  type UserIdsResponse = {
    userids?: string[];
  };

  type UserInfoDTO = {
    autologin?: string;
    role?: Role;
    timezone?: string;
    users_status?: string;
    roleid?: string;
    attempt_clock?: string;
    refresh?: string;
    attempt_failed?: string;
    userid?: string;
    gui_access?: string;
    debug_mode?: string;
    attempt_ip?: string;
    url?: string;
    usrgrps?: UsrgrpsItem[];
    medias?: MediasItem[];
    rows_per_page?: string;
    surname?: string;
    autologout?: string;
    name?: string;
    theme?: string;
    lang?: string;
    username?: string;
    mediatypes?: MediatypesItem[];
  };

  type UserInfoRequest = {
    countOutput?: boolean;
    editable?: boolean;
    excludeSearch?: boolean;
    filter?: Record<string, any>;
    limit?: number;
    output?: string;
    preservekeys?: boolean;
    search?: Record<string, any>;
    searchByAny?: boolean;
    searchWildcardsEnabled?: boolean;
    sortorder?: string;
    startSearch?: boolean;
    mediaids?: string[];
    mediatypeids?: string[];
    userids?: string[];
    usrgrpids?: string[];
    getAccess?: boolean;
    selectMedias?: string;
    selectMediatypes?: string;
    selectUsrgrps?: string;
    selectRole?: string;
    sortfield?: string[];
  };

  type UserInfoResponse = {
    autologin?: string;
    role?: Role;
    timezone?: string;
    users_status?: string;
    roleid?: string;
    attempt_clock?: string;
    refresh?: string;
    attempt_failed?: string;
    userid?: string;
    gui_access?: string;
    debug_mode?: string;
    attempt_ip?: string;
    url?: string;
    usrgrps?: UsrgrpsItem[];
    medias?: MediasItem[];
    rows_per_page?: string;
    surname?: string;
    autologout?: string;
    name?: string;
    theme?: string;
    lang?: string;
    username?: string;
    mediatypes?: MediatypesItem[];
  };

  type UserLoginRequest = {
    /** 用户名 */
    username?: string;
    /** 密码 */
    password?: string;
  };

  type UserUpdateRequest = {
    /** 用户id */
    userid?: string;
    /** 用户名称 */
    username?: string;
    /** 是否允许自动登录.
可用值:
0 - (default) 禁止自动登录;
1 - 允许自动登录.
 */
    autologin?: number;
    /** 会话过期时长. 接受具有后缀的秒或时间单位. 如果设置为 0s, 会话将永不过期.
默认: 15m.
 */
    autologout?: string;
    /** 用户语言代码, 示例, en_GB.
默认: default - 系统默认语言.
 */
    lang?: string;
    /** 自动刷新间隔. 接受具有后缀的秒或时间单位.
默认: 30s.
 */
    refresh?: string;
    /** 每页显示的对象条目.
默认: 50.
 */
    rowsPerPage?: number;
    /** 姓. */
    surname?: string;
    /** 用户的主题.
可用值:
default - (default) 系统默认主题;
blue-theme - 蓝主题;
dark-theme - 黑主题.
 */
    theme?: string;
    /** 用户登录后重定向页面的URL.
     */
    url?: string;
    /** 用户时区, 示例, Europe/London, UTC.
默认: default - 系统默认时区.
 */
    timezone?: string;
    /** 用户的角色ID. */
    roleid?: string;
    /** 用户的密码。
如果用户仅添加到具有 LDAP 访问权限的组，则可以省略。
 */
    passwd?: string;
    /** 要将用户添加到的用户 组。
用户组必须具有已定义的 usrgrpid 属性。
 */
    usrgrps?: Usrgrp[];
    /** 要创建的用户 media */
    medias?: Media[];
  };

  type Usrgrp = {
    usrgrpid?: string;
  };

  type UsrgrpsItem = {
    debug_mode?: string;
    usrgrpid?: string;
    userdirectoryid?: string;
    name?: string;
    users_status?: string;
    gui_access?: string;
  };

  type Trigger = {
    triggerid: string;
    expression: string;
    recovery_expression?: string;
    description: string;
    url?: string;
    status: string;
    value: string;
    priority: string;
    lastchange: string;
    comments: string;
    error: string;
    templateid: string;
    type: string;
    state: string;
    flags: string;
    recovery_mode: string;
    recovery_expression: string;
    correlation_mode: string;
    correlation_tag: string;
    manual_close: string;
    opdata: string;
    event_name: string;
    uuid?: string;
    dependencies?: Trigger[];
    tags?: Tags[];
    hosts?: Host[];
    items?: Item[];
  };
  type Item = {
    itemid?: string;
    type?: string;
    snmp_oid: string;
    hostid: string;
    name: string;
    key_?: string;
    delay: string;
    history: string;
    trends: string;
    status: string;
    value_type: string;
    trapper_hosts: string;
    units: string;
    formula: string;
    logtimefmt: string;
    templateid: string;
    valuemapid: string;
    params: string;
    ipmi_sensor: string;
    authtype: string;
    username: string;
    password: string;
    publickey: string;
    privatekey: string;
    flags: string;
    interfaceid: string;
    description: string;
    inventory_link: string;
    lifetime: string;
    evaltype: string;
    jmx_endpoint: string;
    master_itemid: string;
    timeout: string;
    url: string;
    query_fields: any[];
    posts: string;
    status_codes?: string;
    follow_redirects?: string;
    post_type?: string;
    http_proxy?: string;
    headers?: any[];
    retrieve_mode?: string;
    request_method?: string;
    output_format?: string;
    ssl_cert_file?: string;
    ssl_key_file?: string;
    ssl_key_password?: string;
    verify_peer?: string;
    verify_host?: string;
    allow_traps?: string;
    uuid?: string;
    state?: string;
    error?: string;
    parameters?: any[];
    lastclock?: string;
    lastns?: string;
    lastvalue?: string;
    prevvalue?: string;
    triggers?: Trigger[];
    tags?: TemplateTag[];
    inheritedTags?: TemplateTag[];
    hosts?: Host[];
    valuemap?: any;
  };

  type GlobalMacro = {
    globalmacroid?: string;
    macro: string;
    value: string;
    description?: string;
    type: string;
  };
  interface Action {
    actionid?: string;
    name: string;
    eventsource: string;
    status: string;
    esc_period: string;
    pause_suppressed: string;
    notify_if_canceled: string;
    operations?: Operation[];
    recovery_operations?: RecoveryOperations[];
    update_operations?: RecoveryOperations[];
    filter?: Filter;
  }

  interface Operation {
    operationid: string;
    actionid: string;
    operationtype: string;
    esc_period: string;
    esc_step_from: string;
    esc_step_to: string;
    evaltype: string;
    opconditions: any[];
    opmessage: Opmessage;
    opmessage_grp?: any[];
    opmessage_usr?: Opmessageusr[];
    opcommand?: any;
    opcommand_hst?: any[];
    opcommand_grp?: any[];
  }

  interface RecoveryOperations {
    operationid: string;
    actionid: string;
    operationtype: string;
    evaltype: string;
    opconditions: any[];
    opmessage: Opmessage;
    opmessage_grp?: any[];
    opmessage_usr?: Opmessageusr[];
    opcommand?: any;
    opcommand_hst?: any[];
    opcommand_grp?: any[];
  }

  interface Opmessageusr {
    userid: string;
  }

  interface Opmessage {
    default_msg: string;
    subject: string;
    message: string;
    mediatypeid: string;
  }

  interface Filter {
    evaltype: string;
    formula: string;
    conditions: Condition[];
    eval_formula: string;
  }

  interface Condition {
    conditiontype: string;
    operator: string;
    value: string;
    value2: string;
    formulaid: string;
  }

  interface Problem {
    eventid: string;
    source: string;
    object: string;
    objectid: string;
    clock: string;
    ns: string;
    r_eventid: string;
    r_clock: string;
    r_ns: string;
    correlationid: string;
    userid: string;
    name: string;
    acknowledged: string;
    severity: string;
    opdata: string;
    acknowledges?: any[];
    suppression_data?: any[];
    suppressed?: string;
    urls?: any[];
    tags?: Tag[];
    inheritedTags?: Tag[];
  }
  interface Alert {
    alertid: string;
    actionid: string;
    eventid: string;
    userid: string;
    clock: string;
    mediatypeid: string;
    sendto: string;
    subject: string;
    message: string;
    status: string;
    retries: string;
    error: string;
    esc_step: string;
    alerttype: string;
    p_eventid: string;
    acknowledgeid: string;
    mediatypes?: Record<string, any>[];
  }
  interface Acknowledge {
    acknowledgeid: string;
    userid: string;
    eventid: string;
    clock: string;
    message: string;
    action: string;
    old_severity: string;
    new_severity: string;
  }

  interface History {
    itemid: string;
    clock: string;
    value: string;
    ns: string;
  }
  interface Trend {
    itemid: string;
    clock: string;
    num: string;
    value_min: string;
    value_avg: string;
    value_max: string;
  }
}
